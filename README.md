# Storyworth

A platform for preserving and sharing family stories and memories.

## Get Started

Follow these steps to set up the project locally.

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL
- AWS Account (for file storage)
- SendGrid Account (for email services)

### Environment Configuration

Create three `.env` files in the following locations with their respective variables:

1. Root directory (`.env`):
```
JWT_SECRET=your_jwt_secret
DATABASE_URL=your_database_url
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=your_sender_email
CLIENT_URL=http://localhost:5173
VITE_AWS_BUCKET_NAME=your_bucket_name
VITE_AWS_REGION=your_aws_region
```

2. Client directory (`client/.env`):
```
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=your_sender_email
CLIENT_URL=http://localhost:5173
VITE_AWS_BUCKET_NAME=your_bucket_name
VITE_AWS_REGION=your_aws_region
VITE_INTERCOM_APP_ID=ztbwnxvr
```

3. Server directory (`server/.env`):
```
JWT_SECRET=your_jwt_secret
DATABASE_URL=your_database_url
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=your_sender_email
CLIENT_URL=http://localhost:5173
AWS_BUCKET_NAME=your_bucket_name
AWS_BUCKET_REGION=your_aws_region
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
WHISPER_API_KEY=your_whisper_api_key

INTERCOM_ACCESS_TOKEN=************************************************************
INTERCOM_APP_ID=ztbwnxvr
```

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd storyworth
```

2. Install dependencies:
```bash
# Install root dependencies
npm install

# Install client dependencies
cd client
npm install

# Install server dependencies
cd ../server
npm install
```

3. Set up the database:
```bash
# Create database tables
npx tsx server/migrate.ts
```

4. Start the development servers:

```bash
# Start client (in client directory)
cd client
npm run dev

# Start server (in server directory)
cd server
npm run dev
```

The application should now be running at:
- Frontend: http://localhost:5173
- Backend: http://localhost:5000

### Additional Configuration

1. AWS Setup:
   - Create an S3 bucket in your AWS account
   - Configure CORS settings for your bucket
   - Create an IAM user with appropriate S3 permissions
   - Add AWS credentials to your server `.env` file

2. SendGrid Setup:
   - Create a SendGrid account
   - Generate an API key
   - Verify your sender email
   - Add SendGrid credentials to your `.env` files




