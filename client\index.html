<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <!-- Pre-connect early so the handshake happens in parallel -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Hint the browser that the stylesheet is high priority -->
    <link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@400;700&family=EB+Garamond:wght@400;700&family=Libre+Baskerville:wght@400;700&family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Roboto:wght@400;700&family=Poppins:wght@400;700&family=Raleway:wght@400;700&family=Work+Sans:wght@400;700&family=Merriweather:wght@400;700&family=Source+Sans+Pro:wght@400;700&family=Crimson+Text:wght@400;700&family=Spectral:wght@400;700&family=Caveat:wght@400;700&family=Dancing+Script:wght@400;700&family=Satisfy&family=Kalam:wght@400;700&family=Oswald:wght@400;700&family=Lato:wght@400;700&family=Abril+Fatface&family=Open+Sans:wght@400;700&family=Nunito:wght@400;700&family=Outfit:wght@400;700&display=swap" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@400;700&family=EB+Garamond:wght@400;700&family=Libre+Baskerville:wght@400;700&family=Lora:wght@400;700&family=Montserrat:wght@400;700&family=Roboto:wght@400;700&family=Poppins:wght@400;700&family=Raleway:wght@400;700&family=Work+Sans:wght@400;700&family=Merriweather:wght@400;700&family=Source+Sans+Pro:wght@400;700&family=Crimson+Text:wght@400;700&family=Spectral:wght@400;700&family=Caveat:wght@400;700&family=Dancing+Script:wght@400;700&family=Satisfy&family=Kalam:wght@400;700&family=Oswald:wght@400;700&family=Lato:wght@400;700&family=Abril+Fatface&family=Open+Sans:wght@400;700&family=Nunito:wght@400;700&family=Outfit:wght@400;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <title>Culinary Recipe Books</title>
  </head>
   <!-- Always include a descriptive title for SEO / a11y -->

<!-- Dynamically loaded fonts – keep a base sheet so first paint uses the right family -->
<!-- TODO:  generate a run-time loader when the user changes fonts -->
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
