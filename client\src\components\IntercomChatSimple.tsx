import { useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';

// Extend the Window interface to include Intercom
declare global {
  interface Window {
    Intercom: any;
    intercomSettings: any;
  }
}

export function IntercomChatSimple() {
  const { user } = useAuth();

  useEffect(() => {
    const appId = import.meta.env.VITE_INTERCOM_APP_ID;

    if (!appId) {
      console.warn('Intercom App ID not found');
      return;
    }

    // Simple Intercom initialization without user identification first
    if (!window.Intercom) {
      // Create Intercom function
      window.Intercom = function(...args: any[]) {
        (window.Intercom as any).q = (window.Intercom as any).q || [];
        (window.Intercom as any).q.push(args);
      };

      // Load script
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.async = true;
      script.src = `https://widget.intercom.io/widget/${appId}`;

      script.onload = () => {
        // Boot Intercom without user data first
        window.Intercom('boot', { app_id: appId });
      };

      document.head.appendChild(script);
    } else {
      // If already loaded, just boot
      window.Intercom('boot', { app_id: appId });
    }

    // Cleanup function
    return () => {
      if (window.Intercom) {
        window.Intercom('shutdown');
      }
    };
  }, []);

  // Update user information when user changes
  useEffect(() => {
    if (window.Intercom && user) {
      window.Intercom('update', {
        name: user.name || user.email,
        email: user.email,
        user_id: user.id?.toString(),
        custom_attributes: {
          role: user.role,
          plan: 'free'
        }
      });
    }
  }, [user]);

  return null;
}
