import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, X, Send, Minimize2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
 import { Textarea } from '@/components/ui/textarea';

interface ChatMessage {
  id: string;
  message: string;
  sender: 'user' | 'support';
  timestamp: Date;
}

interface LiveChatWidgetProps {
  isEnabled?: boolean;
  position?: 'bottom-right' | 'bottom-left';
}

export function LiveChatWidget({ isEnabled = true, position = 'bottom-right' }: LiveChatWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [hasStartedChat, setHasStartedChat] = useState(false);
  const [currentTicketId, setCurrentTicketId] = useState<number | null>(null);
  const [isCreatingTicket, setIsCreatingTicket] = useState(false);
  const [showTicketPrompt, setShowTicketPrompt] = useState(false);

  // Check if external chat service is configured
  useEffect(() => {
    // This would check if Zendesk Chat, Intercom, or other service is configured
    const checkChatService = () => {
      // For now, we'll simulate checking for external service
      const hasExternalService = import.meta.env.VITE_SUPPORT_SERVICE === 'zendesk' ||
                                 import.meta.env.VITE_SUPPORT_SERVICE === 'intercom';
      setIsConnected(hasExternalService);
    };

    checkChatService();
  }, []);

  const handleSendMessage = async () => {
    if (!currentMessage.trim()) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      message: currentMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    const messageToSend = currentMessage;
    setCurrentMessage('');
    setHasStartedChat(true);

    // Show automated response based on connection status
    setTimeout(() => {
      if (isConnected) {
        // If connected to external service, show live chat response
        const supportMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          message: "Thank you for contacting us! A support representative will be with you shortly.",
          sender: 'support',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, supportMessage]);
      } else {
        // If not connected, offer to create a support ticket after a few messages
        const messageCount = messages.filter(m => m.sender === 'user').length + 1;

        if (messageCount >= 2 && !currentTicketId && !showTicketPrompt) {
          setShowTicketPrompt(true);
          const supportMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            message: "I see you need help with something specific. Would you like me to create a support ticket so our team can assist you via email?",
            sender: 'support',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, supportMessage]);
        } else if (!showTicketPrompt && !currentTicketId) {
          const supportMessage: ChatMessage = {
            id: (Date.now() + 1).toString(),
            message: "I understand. Please tell me more about what you need help with.",
            sender: 'support',
            timestamp: new Date()
          };
          setMessages(prev => [...prev, supportMessage]);
        }
      }
    }, 1000);
  };

  const createSupportTicket = async () => {
    if (isCreatingTicket) return;

    setIsCreatingTicket(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        const errorMessage: ChatMessage = {
          id: Date.now().toString(),
          message: "Please log in to create a support ticket. You can also contact <NAME_EMAIL>",
          sender: 'support',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, errorMessage]);
        return;
      }

      // Collect all user messages as the ticket description
      const userMessages = messages.filter(m => m.sender === 'user').map(m => m.message);
      const description = userMessages.join('\n\n');

      const response = await fetch('/api/support/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          subject: 'Live Chat Support Request',
          description: description,
          priority: 'normal',
          category: 'general'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentTicketId(data.ticket.id);
        setShowTicketPrompt(false);

        const successMessage: ChatMessage = {
          id: Date.now().toString(),
          message: `Perfect! I've created support ticket #${data.ticket.id} for you. Our team will respond via email within 24 hours. Is there anything else I can help you with right now?`,
          sender: 'support',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, successMessage]);
      } else {
        throw new Error('Failed to create ticket');
      }
    } catch (error) {
      console.error('Error creating support ticket:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        message: "I'm sorry, I couldn't create a support ticket right now. Please try contacting <NAME_EMAIL>",
        sender: 'support',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsCreatingTicket(false);
    }
  };

  const declineTicket = () => {
    setShowTicketPrompt(false);
    const message: ChatMessage = {
      id: Date.now().toString(),
      message: "No problem! Feel free to continue chatting or check our Help Center for quick answers. How else can I assist you?",
      sender: 'support',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
  };

  const handleStartChat = () => {
    setHasStartedChat(true);
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      message: "Hello! How can we help you today?",
      sender: 'support',
      timestamp: new Date()
    };
    setMessages([welcomeMessage]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isEnabled) return null;

  const positionClasses = position === 'bottom-right'
    ? 'bottom-4 right-4'
    : 'bottom-4 left-4';

  return (
    <div className={`fixed ${positionClasses} z-50`}>
      {!isOpen ? (
        // Chat button
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-shadow"
          size="icon"
        >
          <MessageSquare className="h-6 w-6" />
        </Button>
      ) : (
        // Chat window
        <Card className={`w-80 h-96 shadow-xl transition-all duration-200 ${isMinimized ? 'h-12' : ''}`}>
          <CardHeader className="flex flex-row items-center justify-between p-4 bg-primary text-primary-foreground rounded-t-lg">
            <CardTitle className="text-sm font-medium">
              {isConnected ? 'Live Support Chat' : 'Support Chat'}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20"
                onClick={() => setIsMinimized(!isMinimized)}
              >
                <Minimize2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-primary-foreground hover:bg-primary-foreground/20"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          {!isMinimized && (
            <CardContent className="p-0 flex flex-col h-80">
              {!hasStartedChat ? (
                // Welcome screen
                <div className="flex-1 flex flex-col items-center justify-center p-6 text-center">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-semibold mb-2">Need Help?</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Start a conversation with our support team. We're here to help!
                  </p>
                  {!isConnected && (
                    <p className="text-xs text-orange-600 mb-4">
                      Live chat is currently offline. Messages will be sent as support tickets.
                    </p>
                  )}
                  <Button onClick={handleStartChat} className="w-full">
                    Start Chat
                  </Button>
                </div>
              ) : (
                <>
                  {/* Messages area */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-3">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[80%] p-3 rounded-lg text-sm ${
                            message.sender === 'user'
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted'
                          }`}
                        >
                          <p>{message.message}</p>
                          <p className={`text-xs mt-1 opacity-70`}>
                            {message.timestamp.toLocaleTimeString([], {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                      </div>
                    ))}

                    {/* Show ticket creation prompt */}
                    {showTicketPrompt && (
                      <div className="flex justify-start">
                        <div className="max-w-[80%] p-3 rounded-lg text-sm bg-muted">
                          <div className="flex gap-2 mt-2">
                            <Button
                              size="sm"
                              onClick={createSupportTicket}
                              disabled={isCreatingTicket}
                              className="text-xs"
                            >
                              {isCreatingTicket ? 'Creating...' : 'Yes, create ticket'}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={declineTicket}
                              className="text-xs"
                            >
                              No, continue chat
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Input area */}
                  <div className="border-t p-4">
                    <div className="flex gap-2">
                      <Textarea
                        value={currentMessage}
                        onChange={(e) => setCurrentMessage(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder="Type your message..."
                        className="flex-1 min-h-[40px] max-h-[80px] resize-none"
                        rows={1}
                      />
                      <Button
                        onClick={handleSendMessage}
                        disabled={!currentMessage.trim()}
                        size="icon"
                        className="h-10 w-10"
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                    {!isConnected && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Messages will be converted to support tickets
                      </p>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          )}
        </Card>
      )}
    </div>
  );
}

// Hook to conditionally render the chat widget
export function useLiveChatWidget() {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    // Only show chat widget on certain pages or for certain users
    const currentPath = window.location.pathname;
    const showOnPaths = ['/dashboard', '/recipe-books', '/help', '/contact'];

    setShouldShow(showOnPaths.some(path => currentPath.startsWith(path)));
  }, []);

  return shouldShow;
}
