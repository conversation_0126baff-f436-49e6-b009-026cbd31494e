import { useState } from 'react';
import { Loader2, Trash2, X } from 'lucide-react';
import { Button } from './ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import { Textarea } from './ui/textarea';
import { API_URL } from '@/lib/constants';
import { useToast } from '@/hooks/use-toast';

interface RecipeActionsProps {
  recipe: {
    id: number;
    title: string;
  };
  onRecipeDeleted: () => void;
  isOrganizer: boolean;
  isAdmin: boolean;
}

export function RecipeActions({ recipe, onRecipeDeleted, isOrganizer, isAdmin }: RecipeActionsProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [rejectionMessage, setRejectionMessage] = useState('');
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const { toast } = useToast();

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete recipe');
      }

      toast({
        title: 'Success',
        description: 'Recipe deleted successfully',
      });
      onRecipeDeleted();
    } catch (error) {
      console.error('Error deleting recipe:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete recipe',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleReject = async () => {
    if (!rejectionMessage.trim()) {
      toast({
        title: 'Error',
        description: 'Please provide a rejection message',
        variant: 'destructive',
      });
      return;
    }

    setIsRejecting(true);
    try {
      const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({ rejectionMessage }),
      });

      if (!response.ok) {
        throw new Error('Failed to reject recipe');
      }

      toast({
        title: 'Success',
        description: 'Recipe rejected successfully',
      });
      setIsRejectDialogOpen(false);
      onRecipeDeleted();
    } catch (error) {
      console.error('Error rejecting recipe:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to reject recipe',
        variant: 'destructive',
      });
    } finally {
      setIsRejecting(false);
    }
  };

  if (!isOrganizer && !isAdmin) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="destructive" size="sm">
            <X className="h-4 w-4 mr-2" />
            Reject
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Recipe</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this recipe. This message will be sent to the contributor.
            </DialogDescription>
          </DialogHeader>
          <Textarea
            placeholder="Enter rejection reason..."
            value={rejectionMessage}
            onChange={(e) => setRejectionMessage(e.target.value)}
            className="min-h-[100px]"
          />
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRejectDialogOpen(false)}
              disabled={isRejecting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={isRejecting}
            >
              {isRejecting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Rejecting...
                </>
              ) : (
                'Reject Recipe'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Button
        variant="destructive"
        size="sm"
        onClick={handleDelete}
        disabled={isDeleting}
      >
        {isDeleting ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Deleting...
          </>
        ) : (
          <>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </>
        )}
      </Button>
    </div>
  );
} 