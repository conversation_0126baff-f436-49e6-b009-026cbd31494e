import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { UserRole } from "@/lib/constants";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function AuthForm() {
  const [, setLocation] = useLocation();
  const { login, register, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Get return URL from query parameters
  const params = new URLSearchParams(window.location.search);
  const returnUrl = params.get('returnUrl');

  useEffect(() => {
    // If user is already logged in, redirect to return URL or default page
    if (user) {
      if (returnUrl) {
        setLocation(returnUrl);
      } else {
        // Redirect based on user role
        if (user.role === UserRole.CONTRIBUTOR) {
          setLocation('/recipe-books');
        } else if (user.role === UserRole.ORGANIZER) {
          setLocation('/organizer');
        } else if (user.role === UserRole.ADMIN) {
          setLocation('/admin');
        }
      }
    }
  }, [user, returnUrl, setLocation]);

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    const formData = new FormData(e.currentTarget);
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    try {
      await login(email, password);
      toast({
        title: "Success",
        description: "Login successful!",
      });
      // The redirect will be handled by the useEffect above
    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: "Error",
        description: "Invalid credentials",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    const formData = new FormData(e.currentTarget);
    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    const confirmPassword = formData.get("confirmPassword") as string;
    const role = formData.get("role") as keyof typeof UserRole;

    if (password !== confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    try {
      await register(name, email, password, role);
      toast({
        title: "Success",
        description: "Registration successful!",
      });
      // The redirect will be handled by the useEffect above
    } catch (error) {
      toast({
        title: "Error",
        description: "Registration failed",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // If user is already logged in, show loading state while redirecting
  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F8F7F4] flex items-center justify-center py-12 px-4">
      <Card className="w-full max-w-md bg-white border-none shadow-lg">
        <CardContent className="p-8">
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="login" className="text-[#2E4B7A]">Login</TabsTrigger>
              <TabsTrigger value="register" className="text-[#2E4B7A]">Register</TabsTrigger>
            </TabsList>

            <TabsContent value="login">
              <form onSubmit={handleLogin} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-[#2E4B7A]">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    placeholder="Enter your email"
                    className="border-[#E6D5C4]/30 focus:border-[#9B7A5D]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-[#2E4B7A]">Password</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    required
                    placeholder="Enter your password"
                    className="border-[#E6D5C4]/30 focus:border-[#9B7A5D]"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full bg-[#9B7A5D] hover:bg-[#8B6A4D] text-white"
                  disabled={isLoading}
                >
                  {isLoading ? "Logging in..." : "Login"}
                </Button>
              </form>
            </TabsContent>

            <TabsContent value="register">
              <form onSubmit={handleRegister} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-[#2E4B7A]">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    required
                    placeholder="Enter your name"
                    className="border-[#E6D5C4]/30 focus:border-[#9B7A5D]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-[#2E4B7A]">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    placeholder="Enter your email"
                    className="border-[#E6D5C4]/30 focus:border-[#9B7A5D]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-[#2E4B7A]">Password</Label>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    required
                    placeholder="Enter your password"
                    className="border-[#E6D5C4]/30 focus:border-[#9B7A5D]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-[#2E4B7A]">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    required
                    placeholder="Confirm your password"
                    className="border-[#E6D5C4]/30 focus:border-[#9B7A5D]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role" className="text-[#2E4B7A]">Role</Label>
                  <Select name="role" required>
                    <SelectTrigger className="border-[#E6D5C4]/30 focus:border-[#9B7A5D]">
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={UserRole.CONTRIBUTOR}>Contributor</SelectItem>
                      <SelectItem value={UserRole.ORGANIZER}>Organizer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-[#9B7A5D] hover:bg-[#8B6A4D] text-white"
                  disabled={isLoading}
                >
                  {isLoading ? "Registering..." : "Register"}
                </Button>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
} 