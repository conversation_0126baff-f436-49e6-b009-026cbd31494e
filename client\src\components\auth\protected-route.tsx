import { ReactNode, useEffect, useRef } from 'react';
import { useLocation } from 'wouter';
import { useAuth } from '@/hooks/use-auth';
import { UserRole } from '@/lib/constants';

interface ProtectedRouteProps {
  children: ReactNode;
  allowedRoles?: string[];
  redirectTo?: string;
}

export function ProtectedRoute({
  children,
  allowedRoles = [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR],
  redirectTo = '/login'
}: ProtectedRouteProps) {
  const { user, isLoading } = useAuth();
  const [, setLocation] = useLocation();
  const redirectingRef = useRef(false);

  useEffect(() => {
    if (!isLoading) {
      if (!user && !redirectingRef.current) {
        redirectingRef.current = true;
        const currentPath = window.location.pathname;
        setLocation(`${redirectTo}?returnUrl=${encodeURIComponent(currentPath)}`);
      } else if (user && !allowedRoles.includes(user.role) && !redirectingRef.current) {
        redirectingRef.current = true;
        setLocation(redirectTo);
      }
    }
  }, [isLoading, user, allowedRoles, redirectTo, setLocation]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!user || !allowedRoles.includes(user.role)) {
    return null;
  }

  return <>{children}</>;
} 