import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

type Contributor = {
  initials: string;
  name: string;
  email: string;
  role: "Organizer" | "Contributor";
  color: string;
};

const contributors: Contributor[] = [
  {
    initials: "<PERSON><PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Organizer",
    color: "bg-secondary",
  },
  {
    initials: "TS",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Contributor",
    color: "bg-primary",
  },
  {
    initials: "<PERSON>",
    name: "Aunt <PERSON>",
    email: "<EMAIL>",
    role: "Contributor",
    color: "bg-muted",
  },
];

export function ContributorsList() {
  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <h2 className="font-serif text-xl font-semibold mb-4">Current Contributors</h2>
        
        <div className="space-y-4">
          {contributors.map((contributor, index) => (
            <div 
              key={index}
              className="flex items-center justify-between pb-3 border-b border-border"
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full ${contributor.color} flex items-center justify-center text-white`}>
                  {contributor.initials}
                </div>
                <div>
                  <p className="font-medium">{contributor.name}</p>
                  <p className="text-xs text-muted-foreground">{contributor.email}</p>
                </div>
              </div>
              <Badge variant="outline" className={
                contributor.role === "Organizer" 
                  ? "bg-primary/10 text-primary" 
                  : "bg-secondary/10 text-secondary"
              }>
                {contributor.role}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
