// import { <PERSON><PERSON> } from "@/components/ui/button";
// import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
// import { Textarea } from "@/components/ui/textarea";
// import { useQuery } from "@tanstack/react-query";
// import { useAuth } from "@/hooks/use-auth";
// import { useToast } from "@/hooks/use-toast";
// import { useState } from "react";
// import { API_URL } from '@/lib/constants';

// const API_BASE = API_URL;

// interface Project {
//   id: number;
//   name: string;
//   description: string;
// }

// export function InviteForm() {
//   const { user } = useAuth();
//   const { toast } = useToast();
//   const [selectedProject, setSelectedProject] = useState<string>("");
//   const [emails, setEmails] = useState<string>("<EMAIL>");
//   const [deadline, setDeadline] = useState<string>(() => {
//     const date = new Date();
//     date.setMinutes(date.getMinutes() + 10);
//     return date.toISOString().slice(0, 16);
//   });
//   const [reminderFrequency, setReminderFrequency] = useState<string>("1min");
//   const [isLoading, setIsLoading] = useState(false);

//   // Fetch only the organizer's projects
//   const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
//     queryKey: ['organizer-projects'],
//     queryFn: async () => {
//       const token = localStorage.getItem('token');
//       if (!token) {
//         throw new Error('Not authenticated');
//       }

//       const response = await fetch(`${API_BASE}/organizer/my-projects`, {
//         headers: {
//           "Authorization": `Bearer ${token}`,
//         },
//       });

//       if (!response.ok) {
//         const error = await response.json();
//         throw new Error(error.message || 'Failed to fetch recipe books');
//       }

//       const data = await response.json();
//       return data.projects || [];
//     },
//     enabled: !!user && user.role === 'organizer'
//   });

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     if (!selectedProject || !emails.trim()) return;

//     setIsLoading(true);
//     try {
//       const emailList = emails.split(",").map(email => email.trim());
      
//       for (const email of emailList) {
//         const response = await fetch(`${API_BASE}/organizer/projects/${selectedProject}/invite`, {
//           method: "POST",
//           headers: {
//             "Content-Type": "application/json",
//             "Authorization": `Bearer ${localStorage.getItem('token')}`,
//           },
//           body: JSON.stringify({
//             email,
//             deadline: deadline || undefined,
//             reminderFrequency: "1min"
//           }),
//         });

//         if (!response.ok) {
//           const error = await response.json();
//           throw new Error(error.message || "Failed to send invitation");
//         }
//       }

//       toast({
//         title: "Success",
//         description: "Invitations sent successfully!"
//       });
//       setEmails("");
//       setDeadline("");
//       setReminderFrequency("weekly");
//     } catch (error) {
//       toast({
//         title: "Error",
//         description: error instanceof Error ? error.message : "Failed to send invitations",
//         variant: "destructive"
//       });
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>Invite Contributors</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <form onSubmit={handleSubmit} className="space-y-4">
//           <div className="space-y-2">
//             <Label htmlFor="project">Select Project</Label>
//             <Select value={selectedProject} onValueChange={setSelectedProject}>
//               <SelectTrigger>
//                 <SelectValue placeholder="Select a project" />
//               </SelectTrigger>
//               <SelectContent>
//                 {projectsLoading ? (
//                   <SelectItem value="loading" disabled>Loading...</SelectItem>
//                 ) : projects?.length === 0 ? (
//                   <SelectItem value="none" disabled>No recipe books available</SelectItem>
//                 ) : (
//                   projects?.map((project) => (
//                     <SelectItem key={project.id} value={project.id.toString()}>
//                       {project.name}
//                     </SelectItem>
//                   ))
//                 )}
//               </SelectContent>
//             </Select>
//           </div>

//           <div className="space-y-2">
//             <Label htmlFor="emails">Contributor Emails</Label>
//             <Input
//               id="emails"
//               placeholder="Enter email addresses (comma-separated)"
//               value={emails}
//               onChange={(e) => setEmails(e.target.value)}
//             />
//           </div>

//           <div className="space-y-2">
//             <Label htmlFor="deadline">Submission Deadline (Optional)</Label>
//             <Input
//               id="deadline"
//               type="datetime-local"
//               value={deadline}
//               onChange={(e) => setDeadline(e.target.value)}
//             />
//           </div>

//           <div className="space-y-2">
//             <Label htmlFor="reminderFrequency">Reminder Frequency</Label>
//             <Select value={reminderFrequency} onValueChange={setReminderFrequency}>
//               <SelectTrigger>
//                 <SelectValue placeholder="Select reminder frequency" />
//               </SelectTrigger>
//               <SelectContent>
//                 <SelectItem value="1min">Every 1 minute (Test)</SelectItem>
//                 {/* <SelectItem value="2min">Every 2 minutes (Test)</SelectItem>
//                 <SelectItem value="5min">Every 5 minutes (Test)</SelectItem>
//                 <SelectItem value="15min">Every 15 minutes (Test)</SelectItem>
//                 <SelectItem value="30min">Every 30 minutes (Test)</SelectItem>
//                 <SelectItem value="1hour">Every hour (Test)</SelectItem> */}
//                 <SelectItem value="daily">Daily</SelectItem>
//                 <SelectItem value="weekly">Weekly</SelectItem>
//                 <SelectItem value="biweekly">Bi-weekly</SelectItem>
//                 <SelectItem value="monthly">Monthly</SelectItem>
//               </SelectContent>
//             </Select>
//           </div>
          
//           <div className="flex justify-between items-center">
//             <div className="text-sm text-muted-foreground">You can invite up to 5 more contributors</div>
//             <Button type="submit" disabled={!selectedProject || !emails.trim() || isLoading}>
//               {isLoading ? "Sending..." : "Send Invitations"}
//             </Button>
//           </div>
//         </form>
//       </CardContent>
//     </Card>
//   );
// }
