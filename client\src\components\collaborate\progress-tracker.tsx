import { Card, CardContent } from "@/components/ui/card";

type ProgressData = {
  name: string;
  progress: number;
};

const progressData: ProgressData[] = [
  { name: "Overall Completion", progress: 65 },
  { name: "<PERSON>", progress: 100 },
  { name: "<PERSON>", progress: 75 },
  { name: "<PERSON>", progress: 25 },
];

export function ProgressTracker() {
  return (
    <Card>
      <CardContent className="p-6">
        <h2 className="font-serif text-xl font-semibold mb-4">Submission Progress</h2>
        
        <div className="space-y-4">
          {progressData.map((item, index) => (
            <div key={index}>
              <div className="flex justify-between items-center mb-1">
                <p className="text-sm font-medium">{item.name}</p>
                <span className="text-sm font-medium">{item.progress}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full" 
                  style={{ width: `${item.progress}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
