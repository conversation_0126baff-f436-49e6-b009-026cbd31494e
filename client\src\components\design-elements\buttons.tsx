import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Bookmark, Filter, Plus, Share } from "lucide-react";

export function Buttons() {
  return (
    <div className="mb-16">
      <h2 className="font-serif text-2xl font-semibold mb-6">Buttons</h2>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <h3 className="font-serif text-lg font-semibold mb-4">Primary Buttons</h3>
            <div className="space-y-4">
              <Button className="w-full" variant="default">
                Primary Button
              </Button>
              
              <Button className="w-full" variant="default">
                <Plus className="mr-2 h-4 w-4" />
                With Icon
              </Button>
              
              <Button className="w-full" variant="default" disabled>
                Disabled
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <h3 className="font-serif text-lg font-semibold mb-4">Secondary Buttons</h3>
            <div className="space-y-4">
              <Button className="w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground">
                Secondary Button
              </Button>
              
              <Button className="w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground">
                <Bookmark className="mr-2 h-4 w-4" />
                With Icon
              </Button>
              
              <Button className="w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground" disabled>
                Disabled
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <h3 className="font-serif text-lg font-semibold mb-4">Outline Buttons</h3>
            <div className="space-y-4">
              <Button className="w-full" variant="outline">
                Outline Button
              </Button>
              
              <Button className="w-full border-secondary text-secondary hover:text-secondary hover:bg-secondary/10" variant="outline">
                <Share className="mr-2 h-4 w-4" />
                Share Recipe
              </Button>
              
              <Button className="w-full" variant="outline" disabled>
                Disabled
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
