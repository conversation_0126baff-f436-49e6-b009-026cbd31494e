import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { BookOpen, Bookmark, Camera, File, CloudUpload, Mic, Plus } from "lucide-react";
import { mockImages } from "@/lib/utils";

export function Cards() {
  return (
    <div>
      <h2 className="font-serif text-2xl font-semibold mb-6">Cards</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Recipe Card */}
        <div className="bg-card rounded-lg overflow-hidden shadow-md card-hover">
          <img 
            src={mockImages.cookbookRecipes[0]} 
            alt="Mediterranean Salad" 
            className="w-full h-48 object-cover"
          />
          <div className="p-5">
            <h3 className="font-serif text-xl font-semibold mb-2">Mediterranean Salad</h3>
            <p className="text-sm text-muted-foreground mb-3">
              A refreshing salad with cucumbers, tomatoes, olives, and feta cheese.
            </p>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                  Vegetarian
                </Badge>
                <Badge variant="outline" className="bg-secondary/10 text-secondary border-secondary/20">
                  30 min
                </Badge>
              </div>
              <Button variant="ghost" size="sm" className="text-secondary">
                <Bookmark className="h-4 w-4 mr-1" />
                <span>Save</span>
              </Button>
            </div>
          </div>
        </div>
        
        {/* Project Card */}
        <div className="bg-card rounded-lg overflow-hidden shadow-md card-hover">
          <div className="bg-primary h-20 flex items-center justify-center text-white">
            <BookOpen className="h-8 w-8" />
          </div>
          <div className="p-5">
            <h3 className="font-serif text-xl font-semibold mb-2">Family Favorites</h3>
            <div className="flex items-center space-x-2 mb-3">
              <div className="w-7 h-7 rounded-full bg-secondary flex items-center justify-center text-white text-xs">JD</div>
              <div className="w-7 h-7 rounded-full bg-primary flex items-center justify-center text-white text-xs">TS</div>
              <div className="w-7 h-7 rounded-full bg-muted flex items-center justify-center text-muted-foreground text-xs">+3</div>
            </div>
            <div className="mb-4">
              <div className="w-full bg-muted rounded-full h-2">
                <div className="bg-secondary h-2 rounded-full" style={{ width: '65%' }}></div>
              </div>
              <p className="text-xs mt-1 text-muted-foreground">12 recipes collected (65%)</p>
            </div>
            <div className="flex justify-between">
              <span className="text-xs text-muted-foreground">Deadline: Sep 30</span>
              <Button variant="ghost" size="sm" className="text-primary">
                <span>View</span>
              </Button>
            </div>
          </div>
        </div>
        
        {/* Category Card */}
        <div className="bg-card rounded-lg overflow-hidden shadow-md card-hover p-5">
          <h3 className="font-serif text-xl font-semibold mb-4">Submit a Recipe</h3>
          <div className="grid grid-cols-2 gap-3 mb-4">
            <Button variant="outline" className="flex flex-col items-center justify-center py-6">
              <File className="h-6 w-6 text-primary mb-2" />
              <span className="text-sm font-medium">Form</span>
            </Button>
            <Button variant="outline" className="flex flex-col items-center justify-center py-6">
              <Mic className="h-6 w-6 text-primary mb-2" />
              <span className="text-sm font-medium">Voice</span>
            </Button>
            <Button variant="outline" className="flex flex-col items-center justify-center py-6">
              <Camera className="h-6 w-6 text-primary mb-2" />
              <span className="text-sm font-medium">Photo</span>
            </Button>
            <Button variant="outline" className="flex flex-col items-center justify-center py-6">
              <CloudUpload className="h-6 w-6 text-primary mb-2" />
              <span className="text-sm font-medium">Upload</span>
            </Button>
          </div>
          <Button className="w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground">
            <Plus className="h-4 w-4 mr-2" />
            <span>New Recipe</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
