import { cn } from "@/lib/utils";

const colors = [
  {
    name: "Primary",
    colorClass: "bg-primary",
    lightCode: "#6B8F71",
    darkCode: "#6B8F71",
  },
  {
    name: "Secondary",
    colorClass: "bg-secondary",
    lightCode: "#D27D5C",
    darkCode: "#D27D5C",
  },
  {
    name: "Background",
    colorClass: "bg-background",
    lightCode: "#FFFCF8",
    darkCode: "#1A1E2E",
  },
  {
    name: "Card",
    colorClass: "bg-card",
    lightCode: "#FFFFFF",
    darkCode: "#252B3D",
  },
];

type ColorCardProps = {
  name: string;
  colorClass: string;
  lightCode: string;
  darkCode: string;
};

const ColorCard = ({ name, colorClass, lightCode, darkCode }: ColorCardProps) => {
  return (
    <div className="rounded-lg overflow-hidden shadow-md">
      <div className={cn("h-32", colorClass)}></div>
      <div className="p-4 bg-card">
        <h3 className="font-medium">{name}</h3>
        <p className="text-sm opacity-70 dark:hidden">{lightCode}</p>
        <p className="text-sm opacity-70 hidden dark:block">{darkCode}</p>
      </div>
    </div>
  );
};

export function ColorPalette() {
  return (
    <div className="mb-16">
      <h2 className="font-serif text-2xl font-semibold mb-6">Color Palette</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {colors.map((color) => (
          <ColorCard
            key={color.name}
            name={color.name}
            colorClass={color.colorClass}
            lightCode={color.lightCode}
            darkCode={color.darkCode}
          />
        ))}
      </div>
    </div>
  );
}
