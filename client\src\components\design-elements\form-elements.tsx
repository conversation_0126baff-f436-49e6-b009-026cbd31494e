import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export function FormElements() {
  return (
    <div className="mb-16">
      <h2 className="font-serif text-2xl font-semibold mb-6">Form Elements</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardContent className="p-6">
            <h3 className="font-serif text-lg font-semibold mb-4">Text Inputs</h3>
            <div className="space-y-4">
              <div className="space-y-1">
                <Label htmlFor="recipe-title">Recipe Title</Label>
                <Input
                  id="recipe-title"
                  placeholder="Grandma's Apple Pie"
                />
              </div>
              
              <div className="space-y-1">
                <Label htmlFor="recipe-description">Recipe Description</Label>
                <Textarea
                  id="recipe-description"
                  placeholder="A family favorite passed down for generations..."
                  rows={3}
                />
              </div>
              
              <div className="space-y-1">
                <Label htmlFor="prep-time">Preparation Time</Label>
                <div className="relative">
                  <Input
                    id="prep-time"
                    type="number"
                    placeholder="30"
                    className="pr-12"
                  />
                  <div className="absolute right-0 top-0 bottom-0 px-3 flex items-center pointer-events-none text-muted-foreground">
                    mins
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <h3 className="font-serif text-lg font-semibold mb-4">Selection Controls</h3>
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="meal-type">Meal Type</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select meal type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="breakfast">Breakfast</SelectItem>
                    <SelectItem value="lunch">Lunch</SelectItem>
                    <SelectItem value="dinner">Dinner</SelectItem>
                    <SelectItem value="dessert">Dessert</SelectItem>
                    <SelectItem value="snack">Snack</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-3">
                <Label>Dietary Restrictions</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="vegetarian" />
                    <Label htmlFor="vegetarian" className="font-normal">Vegetarian</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="vegan" />
                    <Label htmlFor="vegan" className="font-normal">Vegan</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="gluten-free" />
                    <Label htmlFor="gluten-free" className="font-normal">Gluten Free</Label>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <Label>Difficulty Level</Label>
                <RadioGroup defaultValue="medium">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="easy" id="easy" />
                      <Label htmlFor="easy" className="font-normal">Easy</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="medium" id="medium" />
                      <Label htmlFor="medium" className="font-normal">Medium</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="hard" id="hard" />
                      <Label htmlFor="hard" className="font-normal">Hard</Label>
                    </div>
                  </div>
                </RadioGroup>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
