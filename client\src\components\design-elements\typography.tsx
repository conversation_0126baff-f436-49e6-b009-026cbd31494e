import { Card, CardContent } from "@/components/ui/card";

export function Typography() {
  return (
    <div className="mb-16">
      <h2 className="font-serif text-2xl font-semibold mb-6">Typography</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardContent className="p-6">
            <h3 className="font-serif text-lg font-semibold mb-4">Headings - Playfair Display</h3>
            <div className="space-y-4">
              <p className="font-serif text-4xl">Header 1</p>
              <p className="font-serif text-3xl">Header 2</p>
              <p className="font-serif text-2xl">Header 3</p>
              <p className="font-serif text-xl">Header 4</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <h3 className="font-serif text-lg font-semibold mb-4">Body - Inter</h3>
            <div className="space-y-4">
              <p className="text-base font-normal">Regular paragraph text. Clean and readable for recipe instructions and descriptions.</p>
              <p className="text-base font-medium">Medium text weight for emphasis.</p>
              <p className="text-base font-bold">Bold text for important information.</p>
              <p className="text-sm">Smaller text for metadata and secondary information.</p>
            </div>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardContent className="p-6">
            <h3 className="font-serif text-lg font-semibold mb-4">Accent - Caveat</h3>
            <div className="space-y-4">
              <p className="font-handwritten text-2xl">Recipe notes in handwritten style</p>
              <p className="font-handwritten text-xl">Family secret ingredients</p>
              <p className="font-handwritten text-lg">Grandma's special touch</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
