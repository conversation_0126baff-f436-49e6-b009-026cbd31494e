import { <PERSON> } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";

export function Navigation() {
  const { user } = useAuth();

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href="/">
              <a className="text-xl font-semibold">Recipe Book</a>
            </Link>
          </div>

          <div className="flex items-center gap-4">
            {user?.role === 'contributor' && (
              <Link href="/contributor/dashboard">
                <Button variant="ghost">My Contributions</Button>
              </Link>
            )}
            
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <>
                <Link href="/organizer/recipe-approvals">
                  <Button variant="ghost">Recipe Approvals</Button>
                </Link>
                <Link href="/recipe-books">
                  <Button variant="ghost">Recipe Books</Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
