import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PricingTier, PricingModel, PAGE_COUNT_PER_RECIPE, MINIMUM_PAGE_COUNT } from '@/lib/constants';
import { Slider } from '@/components/ui/slider';

interface PricingCalculatorProps {
  initialTier?: string;
  recipeCount?: number;
  onPricingChange?: (pricing: {
    tier: string;
    basePrice: number;
    pagePrice: number;
    totalPrice: number;
    estimatedPages: number;
  }) => void;
  className?: string;
  compact?: boolean;
}

export function PricingCalculator({
  initialTier = PricingTier.SMALL,
  recipeCount = 0,
  onPricingChange,
  className = '',
  compact = false
}: PricingCalculatorProps) {
  const [tier, setTier] = useState(initialTier);
  const [customRecipeCount, setCustomRecipeCount] = useState(recipeCount);
  const [estimatedPages, setEstimatedPages] = useState(Math.max(MINIMUM_PAGE_COUNT, recipeCount * PAGE_COUNT_PER_RECIPE));

  // Update tier when initialTier prop changes
  useEffect(() => {
    setTier(initialTier);
  }, [initialTier]);

  // Calculate pricing whenever tier or estimated pages change
  useEffect(() => {
    // Use either the provided recipe count or the custom one
    const effectiveRecipeCount = recipeCount || customRecipeCount;

    // Calculate estimated pages based on recipe count
    const calculatedPages = Math.max(MINIMUM_PAGE_COUNT, effectiveRecipeCount * PAGE_COUNT_PER_RECIPE);
    setEstimatedPages(calculatedPages);

    // Calculate pricing
    const tierData = PricingModel[tier as keyof typeof PricingModel];
    const basePrice = tierData.basePrice;
    const pagePrice = tierData.pricePerPage * calculatedPages;
    const totalPrice = basePrice + pagePrice;

    // Notify parent component if callback provided
    if (onPricingChange) {
      onPricingChange({
        tier,
        basePrice,
        pagePrice,
        totalPrice,
        estimatedPages: calculatedPages
      });
    }
  }, [tier, recipeCount, customRecipeCount, onPricingChange]);

  const handleTierChange = (value: string) => {
    setTier(value);
  };

  const handleRecipeCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const count = parseInt(e.target.value) || 0;
    setCustomRecipeCount(count);
  };

  const handlePageSliderChange = (value: number[]) => {
    // If user manually adjusts the page count, update it
    // This overrides the automatic calculation
    setEstimatedPages(value[0]);
  };

  // Get current tier data
  const tierData = PricingModel[tier as keyof typeof PricingModel];

  // Calculate pricing
  const basePrice = tierData.basePrice;
  const pagePrice = tierData.pricePerPage * estimatedPages;
  const totalPrice = basePrice + pagePrice;

  if (compact) {
    return (
      <Card className={`${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Pricing Estimate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span>Tier:</span>
              <span className="font-medium">{tierData.name}</span>
            </div>
            <div className="flex justify-between">
              <span>Contributors:</span>
              <span className="font-medium">Max {tierData.maxContributors}</span>
            </div>
            <div className="flex justify-between">
              <span>Recipes:</span>
              <span className="font-medium">{tierData.minRecipes}-{tierData.maxRecipes}</span>
            </div>
            <div className="flex justify-between">
              <span>Base Price:</span>
              <span className="font-medium">${basePrice.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Page Count:</span>
              <span className="font-medium">{estimatedPages} pages</span>
            </div>
            <div className="flex justify-between">
              <span>Page Price:</span>
              <span className="font-medium">${pagePrice.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-lg font-bold">
              <span>Total:</span>
              <span>${totalPrice.toFixed(2)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardHeader>
        <CardTitle>Pricing Calculator</CardTitle>
        <CardDescription>
          Estimate the cost of your cookbook based on size and number of recipes
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="tier">Cookbook Size</Label>
          <Select value={tier} onValueChange={handleTierChange}>
            <SelectTrigger id="tier">
              <SelectValue placeholder="Select tier" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={PricingTier.SMALL}>
                {PricingModel[PricingTier.SMALL].name} - {PricingModel[PricingTier.SMALL].description}
              </SelectItem>
              <SelectItem value={PricingTier.MEDIUM}>
                {PricingModel[PricingTier.MEDIUM].name} - {PricingModel[PricingTier.MEDIUM].description}
              </SelectItem>
              <SelectItem value={PricingTier.LARGE}>
                {PricingModel[PricingTier.LARGE].name} - {PricingModel[PricingTier.LARGE].description}
              </SelectItem>
            </SelectContent>
          </Select>
          <div className="text-sm text-muted-foreground mt-1">
            <p>Max Contributors: <span className="font-medium">{tierData.maxContributors}</span></p>
            <p>Recipe Range: <span className="font-medium">{tierData.minRecipes}-{tierData.maxRecipes}</span></p>
          </div>
        </div>

        {!recipeCount && (
          <div className="space-y-2">
            <Label htmlFor="recipeCount">Estimated Number of Recipes</Label>
            <Input
              id="recipeCount"
              type="number"
              min="0"
              value={customRecipeCount}
              onChange={handleRecipeCountChange}
            />
            <p className="text-sm text-muted-foreground">
              Each recipe takes approximately {PAGE_COUNT_PER_RECIPE} pages
            </p>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="pageCount">Estimated Page Count: {estimatedPages}</Label>
          <Slider
            id="pageCount"
            min={MINIMUM_PAGE_COUNT}
            max={200}
            step={1}
            value={[estimatedPages]}
            onValueChange={handlePageSliderChange}
          />
          <p className="text-sm text-muted-foreground">
            Minimum page count is {MINIMUM_PAGE_COUNT} pages
          </p>
        </div>

        <div className="space-y-4 pt-4">
          <div className="flex justify-between">
            <span className="font-medium">Base Price ({tierData.name} Tier):</span>
            <span>${basePrice.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Page Price ({estimatedPages} pages × ${tierData.pricePerPage.toFixed(2)}):</span>
            <span>${pagePrice.toFixed(2)}</span>
          </div>
          <div className="h-px bg-border"></div>
          <div className="flex justify-between text-lg font-bold">
            <span>Total Estimated Price:</span>
            <span>${totalPrice.toFixed(2)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
