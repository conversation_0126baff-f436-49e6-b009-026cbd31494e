import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '../ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { useToast } from '../ui/use-toast';
import { api } from '../../lib/api';
import { Edit, Trash2 } from 'lucide-react';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: string;
  contributor: {
    id: number;
    name: string;
    email: string;
  };
}

interface Project {
  id: number;
  title: string;
  description: string;
  status: string;
}

export function ProjectView() {
  const { projectId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [project, setProject] = useState<Project | null>(null);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchProject = async () => {
    try {
      const response = await api.get(`/organizer/projects/${projectId}`);
      setProject(response.data.project);
    } catch (error) {
      console.error('Error fetching project:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch project details',
        variant: 'destructive',
      });
    }
  };

  const fetchRecipes = async () => {
    try {
      const response = await api.get(`/organizer/projects/${projectId}/recipes`);
      setRecipes(response.data.recipes);
    } catch (error) {
      console.error('Error fetching recipes:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch recipes',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProject();
    fetchRecipes();
  }, [projectId]);

  const handleDeleteRecipe = async (recipeId: number) => {
    try {
      await api.delete(`/organizer/recipes/${recipeId}`);
      toast({
        title: 'Success',
        description: 'Recipe deleted successfully',
      });
      fetchRecipes();
    } catch (error) {
      console.error('Error deleting recipe:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete recipe',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!project) {
    return <div>Project not found</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{project.title}</h1>
        <Button onClick={() => navigate('/organizer/projects')}>
          Back to Projects
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Project Details</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">{project.description}</p>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Recipes</h2>
        {recipes.map((recipe) => (
          <Card key={recipe.id} className="mb-4">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>{recipe.title}</CardTitle>
                <p className="text-sm text-gray-500">
                  By {recipe.contributor.name}
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate(`/organizer/recipes/${recipe.id}`)}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteRecipe(recipe.id)}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>
    </div>
  );
} 