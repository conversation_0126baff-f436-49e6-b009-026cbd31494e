import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, MoreHorizontal, Crown, UserCircle2, Users } from "lucide-react";
import { mockImages } from "@/lib/utils";

type ProjectCardProps = {
  title: string;
  recipeCount: number;
  lastUpdated: string;
  status: "In Progress" | "Draft" | "Completed";
  contributors: { initials: string; color: string }[];
  imageIndex: number;
  bgColor: string;
  role: string;
};

export function ProjectCard({
  title,
  recipeCount,
  lastUpdated,
  status,
  contributors,
  imageIndex,
  bgColor,
  role,
}: ProjectCardProps) {
  const statusColors = {
    "In Progress": "bg-secondary/10 text-secondary",
    "Draft": "bg-primary/10 text-primary",
    "Completed": "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400",
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return <Crown className="h-4 w-4" />;
      case 'organizer':
        return <Users className="h-4 w-4" />;
      case 'contributor':
        return <UserCircle2 className="h-4 w-4" />;
      default:
        return <UserCircle2 className="h-4 w-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'organizer':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'contributor':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div className="bg-card rounded-lg overflow-hidden shadow-md card-hover">
      <div className={`h-40 ${bgColor} relative`}>
        <img 
          src={mockImages.foodPhotography[imageIndex % mockImages.foodPhotography.length]} 
          alt={title} 
          className="w-full h-full object-cover mix-blend-multiply opacity-50" 
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <h3 className="font-serif text-2xl font-bold text-white">{title}</h3>
        </div>
      </div>
      <div className="p-5">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            {contributors.map((contributor, index) => (
              <div 
                key={index}
                className={`w-7 h-7 rounded-full ${contributor.color} flex items-center justify-center text-white text-xs`}
              >
                {contributor.initials}
              </div>
            ))}
            <Button size="sm" variant="outline" className="w-7 h-7 p-0 rounded-full">
              <Plus className="h-3 w-3" />
            </Button>
          </div>
          <Badge variant="outline" className={getRoleColor(role)}>
            <span className="flex items-center gap-1">
              {getRoleIcon(role)}
              <span className="capitalize">{role}</span>
            </span>
          </Badge>
        </div>
        <div className="flex justify-between items-center mb-4">
          <div>
            <span className="text-sm block">{recipeCount} recipes</span>
            <span className="text-xs text-muted-foreground">Last updated {lastUpdated}</span>
          </div>
          <Badge variant="outline" className={statusColors[status]}>
            {status}
          </Badge>
        </div>
        <div className="flex space-x-2">
          <Button className="flex-1">Edit</Button>
          <Button variant="outline" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export function NewProjectCard() {
  return (
    <div className="bg-card rounded-lg overflow-hidden shadow-md card-hover border-2 border-dashed border-border flex flex-col items-center justify-center p-8 text-center">
      <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
        <Plus className="h-6 w-6 text-primary" />
      </div>
      <h3 className="font-serif text-xl font-semibold mb-2">Create New Cookbook</h3>
      <p className="text-sm text-muted-foreground mb-4">Start a new collection of family recipes</p>
      <Button>Get Started</Button>
    </div>
  );
}
