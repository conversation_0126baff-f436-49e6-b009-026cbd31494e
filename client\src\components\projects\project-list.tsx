// import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
// import { useLocation } from "wouter";
// import { Badge } from "@/components/ui/badge";
// import { Crown, UserCircle2, Users } from "lucide-react";

// interface Project {
//   id: number;
//   name: string;
//   description: string;
//   status: string;
//   role: string;
//   createdAt: string;
// }

// interface ProjectListProps {
//   projects: Project[];
// }

// export function ProjectList({ projects }: ProjectListProps) {
//   const [, navigate] = useLocation();

//   const getStatusColor = (status: string) => {
//     switch (status.toLowerCase()) {
//       case 'pending':
//         return 'bg-yellow-500';
//       case 'accepted':
//         return 'bg-green-500';
//       case 'rejected':
//         return 'bg-red-500';
//       default:
//         return 'bg-gray-500';
//     }
//   };

//   const getRoleIcon = (role: string) => {
//     switch (role.toLowerCase()) {
//       case 'admin':
//         return <Crown className="h-4 w-4" />;
//       case 'organizer':
//         return <Users className="h-4 w-4" />;
//       case 'contributor':
//         return <UserCircle2 className="h-4 w-4" />;
//       default:
//         return <UserCircle2 className="h-4 w-4" />;
//     }
//   };

//   const getRoleColor = (role: string) => {
//     switch (role.toLowerCase()) {
//       case 'admin':
//         return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
//       case 'organizer':
//         return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
//       case 'contributor':
//         return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
//       default:
//         return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
//     }
//   };

//   return (
//     <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
//       {projects.map((project) => (
//         <Card key={project.id} className="hover:shadow-lg transition-shadow">
//           <CardHeader>
//             <CardTitle className="flex items-center justify-between">
//               <span>{project.name}</span>
//               <div className="flex items-center gap-2">
//                 <Badge variant="outline" className={getRoleColor(project.role)}>
//                   <span className="flex items-center gap-1">
//                     {getRoleIcon(project.role)}
//                     <span className="capitalize">{project.role}</span>
//                   </span>
//                 </Badge>
//                 <Badge className={getStatusColor(project.status)}>
//                   {project.status === 'accepted' ? 'Active' : project.status}
//                 </Badge>
//               </div>
//             </CardTitle>
//             <CardDescription>
//               Created on {new Date(project.createdAt).toLocaleDateString()}
//             </CardDescription>
//           </CardHeader>
//           <CardContent>
//             <p className="text-sm text-muted-foreground mb-4">
//               {project.description}
//             </p>
//             <div className="flex justify-end">
//               <Button
//                 variant="outline"
//                 onClick={() => navigate(`/recipe-books/${project.id}`)}
//               >
//                 View Details
//               </Button>
//             </div>
//           </CardContent>
//         </Card>
//       ))}
//     </div>
//   );
// } 