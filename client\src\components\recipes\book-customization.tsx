import React from 'react';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Palette, Type, BookText, Image as ImageIcon, BookCopy, Upload, Check, BookOpen, Plus, Trash2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';

export interface BookTheme {
  id: string;
  name: string;
  description: string;
  colors: {
    background: string;
    text: string;
    heading: string;
    accent: string;
  };
}

export interface FontOption {
  id: string;
  name: string;
  headingFont: string;
  bodyFont: string;
  accentFont?: string;
}

export interface ChapterStyle {
  id: string;
  name: string;
  description: string;
  preview: string;
}

export interface BookCustomizationOptions {
  theme: string;
  font: string;
  chapterStyle: string;
  cover: string;
  coverTitle?: string;
  coverSubtitle?: string;
  coverImage?: string; // Custom cover image URL
  useCustomCoverImage?: boolean; // Whether to use the custom image
  dedication?: string; // Dedication text
  familyQuotes?: string[]; // Array of family quotes
  includeDedication?: boolean; // Whether to include a dedication page
  includeQuotes?: boolean; // Whether to include family quotes
}

interface BookCustomizationProps {
  options: BookCustomizationOptions;
  onChange: (options: BookCustomizationOptions) => void;
}

// Predefined themes inspired by Adobe Color palettes
const themes: BookTheme[] = [
  // Classic/Traditional themes
  {
    id: 'classic',
    name: 'Classic',
    description: 'Traditional and elegant',
    colors: {
      background: '#FFFFFF',
      text: '#2E4B7A',
      heading: '#2E4B7A',
      accent: '#9B7A5D',
    },
  },
  {
    id: 'heritage',
    name: 'Heritage',
    description: 'Timeless and sophisticated',
    colors: {
      background: '#F8F6F0',
      text: '#3A3238',
      heading: '#5D4037',
      accent: '#8D6E63',
    },
  },
  {
    id: 'antique',
    name: 'Antique',
    description: 'Old-world charm',
    colors: {
      background: '#F5F1E6',
      text: '#4E342E',
      heading: '#3E2723',
      accent: '#8D6E63',
    },
  },

  // Rustic/Earthy themes
  {
    id: 'rustic',
    name: 'Rustic',
    description: 'Warm and homey',
    colors: {
      background: '#F8F7F4',
      text: '#5C4033',
      heading: '#8B4513',
      accent: '#D2B48C',
    },
  },
  {
    id: 'farmhouse',
    name: 'Farmhouse',
    description: 'Country-inspired comfort',
    colors: {
      background: '#F9F5EB',
      text: '#5D4037',
      heading: '#33691E',
      accent: '#7CB342',
    },
  },
  {
    id: 'terracotta',
    name: 'Terracotta',
    description: 'Earthy and warm',
    colors: {
      background: '#FBF7F4',
      text: '#795548',
      heading: '#BF360C',
      accent: '#FF8A65',
    },
  },

  // Modern/Contemporary themes
  {
    id: 'modern',
    name: 'Modern',
    description: 'Clean and minimalist',
    colors: {
      background: '#FFFFFF',
      text: '#333333',
      heading: '#000000',
      accent: '#4A90E2',
    },
  },
  {
    id: 'minimal',
    name: 'Minimal',
    description: 'Sleek and understated',
    colors: {
      background: '#FAFAFA',
      text: '#424242',
      heading: '#212121',
      accent: '#757575',
    },
  },
  {
    id: 'nordic',
    name: 'Nordic',
    description: 'Scandinavian simplicity',
    colors: {
      background: '#F5F5F5',
      text: '#455A64',
      heading: '#263238',
      accent: '#78909C',
    },
  },

  // Vintage/Retro themes
  {
    id: 'vintage',
    name: 'Vintage',
    description: 'Nostalgic and retro',
    colors: {
      background: '#F9F5E9',
      text: '#5D4037',
      heading: '#3E2723',
      accent: '#A1887F',
    },
  },
  {
    id: 'retro',
    name: 'Retro',
    description: '50s-inspired style',
    colors: {
      background: '#FFF8E1',
      text: '#5D4037',
      heading: '#D84315',
      accent: '#FF8A65',
    },
  },

  // Colorful themes
  {
    id: 'coastal',
    name: 'Coastal',
    description: 'Beach-inspired blues',
    colors: {
      background: '#ECEFF1',
      text: '#37474F',
      heading: '#0277BD',
      accent: '#4FC3F7',
    },
  },
  {
    id: 'botanical',
    name: 'Botanical',
    description: 'Fresh and natural greens',
    colors: {
      background: '#F1F8E9',
      text: '#33691E',
      heading: '#2E7D32',
      accent: '#81C784',
    },
  },
  {
    id: 'berry',
    name: 'Berry',
    description: 'Rich berry tones',
    colors: {
      background: '#FCE4EC',
      text: '#4A148C',
      heading: '#880E4F',
      accent: '#EC407A',
    },
  },
  {
    id: 'spice',
    name: 'Spice',
    description: 'Warm spice colors',
    colors: {
      background: '#FFF3E0',
      text: '#BF360C',
      heading: '#E65100',
      accent: '#FF9800',
    },
  },
];

// Predefined font options with expanded Google Fonts selection
const fonts: FontOption[] = [
  // Elegant/Classic options
  {
    id: 'elegant',
    name: 'Elegant',
    headingFont: 'Playfair Display, serif',
    bodyFont: 'Inter, sans-serif',
  },
  {
    id: 'garamond',
    name: 'Garamond',
    headingFont: 'EB Garamond, serif',
    bodyFont: 'EB Garamond, serif',
  },
  {
    id: 'baskerville',
    name: 'Baskerville',
    headingFont: 'Libre Baskerville, serif',
    bodyFont: 'Libre Baskerville, serif',
  },
  {
    id: 'lora',
    name: 'Lora',
    headingFont: 'Lora, serif',
    bodyFont: 'Lora, serif',
  },

  // Modern/Contemporary options
  {
    id: 'contemporary',
    name: 'Contemporary',
    headingFont: 'Montserrat, sans-serif',
    bodyFont: 'Roboto, sans-serif',
  },
  {
    id: 'poppins',
    name: 'Poppins',
    headingFont: 'Poppins, sans-serif',
    bodyFont: 'Poppins, sans-serif',
  },
  {
    id: 'raleway',
    name: 'Raleway',
    headingFont: 'Raleway, sans-serif',
    bodyFont: 'Raleway, sans-serif',
  },
  {
    id: 'workSans',
    name: 'Work Sans',
    headingFont: 'Work Sans, sans-serif',
    bodyFont: 'Work Sans, sans-serif',
  },

  // Traditional options
  {
    id: 'traditional',
    name: 'Traditional',
    headingFont: 'Merriweather, serif',
    bodyFont: 'Source Sans Pro, sans-serif',
  },
  {
    id: 'crimson',
    name: 'Crimson',
    headingFont: 'Crimson Text, serif',
    bodyFont: 'Crimson Text, serif',
  },
  {
    id: 'spectral',
    name: 'Spectral',
    headingFont: 'Spectral, serif',
    bodyFont: 'Spectral, serif',
  },

  // Handwritten/Script options
  {
    id: 'handwritten',
    name: 'Caveat',
    headingFont: 'Caveat, cursive',
    bodyFont: 'Outfit, sans-serif',
    accentFont: 'Caveat, cursive',
  },
  {
    id: 'dancingScript',
    name: 'Dancing Script',
    headingFont: 'Dancing Script, cursive',
    bodyFont: 'Roboto, sans-serif',
    accentFont: 'Dancing Script, cursive',
  },
  {
    id: 'satisfy',
    name: 'Satisfy',
    headingFont: 'Satisfy, cursive',
    bodyFont: 'Open Sans, sans-serif',
    accentFont: 'Satisfy, cursive',
  },
  {
    id: 'kalam',
    name: 'Kalam',
    headingFont: 'Kalam, cursive',
    bodyFont: 'Nunito, sans-serif',
    accentFont: 'Kalam, cursive',
  },

  // Display options
  {
    id: 'oswald',
    name: 'Oswald',
    headingFont: 'Oswald, sans-serif',
    bodyFont: 'Lato, sans-serif',
  },
  {
    id: 'abril',
    name: 'Abril Fatface',
    headingFont: 'Abril Fatface, display',
    bodyFont: 'Lora, serif',
  },
];

// Cover design interface
export interface CoverDesign {
  id: string;
  name: string;
  description: string;
  backgroundImage?: string;
  backgroundColor?: string;
  textColor?: string;
  accentColor?: string;
  titlePosition: 'top' | 'center' | 'bottom';
  layout: 'centered' | 'image-top' | 'image-bottom' | 'full-image';
}

// Predefined cover designs
const coverDesigns: CoverDesign[] = [
  {
    id: 'classic',
    name: 'Classic',
    description: 'Elegant and timeless design',
    backgroundColor: '#FFFFFF',
    textColor: '#2E4B7A',
    accentColor: '#9B7A5D',
    titlePosition: 'center',
    layout: 'centered'
  },
  {
    id: 'rustic',
    name: 'Rustic',
    description: 'Warm and homey feel',
    backgroundImage: 'https://images.unsplash.com/photo-1495195134817-aeb325a55b65?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    textColor: '#FFFFFF',
    accentColor: '#D2B48C',
    titlePosition: 'center',
    layout: 'full-image'
  },
  {
    id: 'modern',
    name: 'Modern',
    description: 'Clean and minimalist',
    backgroundColor: '#F8F8F8',
    textColor: '#333333',
    accentColor: '#4A90E2',
    titlePosition: 'top',
    layout: 'image-bottom'
  },
  {
    id: 'vintage',
    name: 'Vintage',
    description: 'Nostalgic and retro style',
    backgroundImage: 'https://images.unsplash.com/photo-1516541196182-6bdb0516ed27?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80',
    textColor: '#F9F5E9',
    accentColor: '#A1887F',
    titlePosition: 'center',
    layout: 'full-image'
  }
];

// Predefined chapter styles
const chapterStyles: ChapterStyle[] = [
  {
    id: 'simple',
    name: 'Simple',
    description: 'Clean and straightforward',
    preview: 'Chapter 1: Breakfast',
  },
  {
    id: 'decorative',
    name: 'Decorative',
    description: 'Ornate with flourishes',
    preview: '✦ Chapter 1 ✦\nBreakfast',
  },
  {
    id: 'numbered',
    name: 'Numbered',
    description: 'Prominent numbers',
    preview: '1\nBreakfast',
  },
  {
    id: 'minimal',
    name: 'Minimal',
    description: 'Understated and elegant',
    preview: 'BREAKFAST',
  },
];

// Generic tab content component for all customization options
interface CustomizationTabContentProps<T> {
  label: string;
  options: T[];
  selectedValue: string;
  onChange: (value: string) => void;
  getOptionId: (option: T) => string;
  getOptionName: (option: T) => string;
  getOptionDescription?: (option: T) => string;
  renderPreview: (option: T) => React.ReactNode;
}
function CustomizationTabContent<T>({
  label,
  options,
  selectedValue,
  onChange,
  getOptionId,
  getOptionName,
  getOptionDescription,
  renderPreview
}: CustomizationTabContentProps<T>) {
  return (
    <div className="space-y-2">
      <Label htmlFor={`${label.toLowerCase().replace(/\s+/g, '-')}-group`} className="text-xs">{label}</Label>
      <RadioGroup
        value={selectedValue}
        onValueChange={onChange}
        id={`${label.toLowerCase().replace(/\s+/g, '-')}-group`}
        aria-label={label}
        className="grid grid-cols-1 gap-3"
      >
        {/* Rest of the component remains the same */}
        {options.map((option) => {
          const id = getOptionId(option);
          return (
            <div key={id} className="relative">
              <RadioGroupItem
                value={id}
                id={`option-${id}`}
                className="sr-only"
              />
              <Label
                htmlFor={`option-${id}`}
                className="flex flex-col gap-1 rounded-md border-2 p-2 hover:bg-accent/5 [&:has([data-state=checked])]:border-primary"
              >
                <div className="font-semibold text-sm">{getOptionName(option)}</div>
                {getOptionDescription && (
                  <div className="text-xs text-muted-foreground truncate">
                    {getOptionDescription(option)}
                  </div>
                )}
                <div className="mt-1">
                  {renderPreview(option)}
                </div>
              </Label>
            </div>
          );
        })}
      </RadioGroup>
    </div>
  );
}

// ThemeCategory component
function ThemeCategory({
  label,
  themes,
  selectedTheme,
  onThemeChange
}: {
  label: string;
  themes: BookTheme[];
  selectedTheme: string;
  onThemeChange: (value: string) => void
}) {
  return (
    <div className="space-y-2">
      <Label className="text-xs text-muted-foreground">{label}</Label>
      <RadioGroup
        value={selectedTheme}
        onValueChange={onThemeChange}
        className="grid grid-cols-1 gap-2"
      >
        {themes.map((theme) => (
          <div key={theme.id} className="relative">
            <RadioGroupItem
              value={theme.id}
              id={`theme-${theme.id}`}
              className="sr-only"
            />
            <Label
              htmlFor={`theme-${theme.id}`}
              className="flex flex-col gap-1 rounded-md border-2 p-2 hover:bg-accent/5 [&:has([data-state=checked])]:border-primary"
            >
              <div className="font-semibold text-sm">{theme.name}</div>
              <div className="text-xs text-muted-foreground truncate">
                {theme.description}
              </div>
              <div className="mt-1">
                <div
                  className="h-8 w-full rounded-md"
                  style={{ backgroundColor: theme.colors.background }}
                >
                  <div className="flex h-full items-center justify-center">
                    <div
                      className="h-5 w-5 rounded-full"
                      style={{ backgroundColor: theme.colors.accent }}
                    ></div>
                    <div
                      className="ml-2 h-3 w-12 rounded"
                      style={{ backgroundColor: theme.colors.heading }}
                    ></div>
                  </div>
                </div>
              </div>
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}

// FontCategory component
function FontCategory({
  label,
  fonts,
  selectedFont,
  onFontChange
}: {
  label: string;
  fonts: FontOption[];
  selectedFont: string;
  onFontChange: (value: string) => void
}) {
  return (
    <div className="space-y-2">
      <Label className="text-xs text-muted-foreground">{label}</Label>
      <RadioGroup
        value={selectedFont}
        onValueChange={onFontChange}
        className="grid grid-cols-1 gap-2"
      >
        {fonts.map((font) => (
          <div key={font.id} className="relative">
            <RadioGroupItem
              value={font.id}
              id={`font-${font.id}`}
              className="sr-only"
            />
            <Label
              htmlFor={`font-${font.id}`}
              className="flex flex-col gap-1 rounded-md border-2 p-2 hover:bg-accent/5 [&:has([data-state=checked])]:border-primary"
            >
              <div className="font-semibold text-sm">{font.name}</div>
              <div className="mt-1">
                <div
                  className="text-base"
                  style={{ fontFamily: font.headingFont }}
                >
                  Heading Font
                </div>
                <div
                  className="text-xs"
                  style={{ fontFamily: font.bodyFont }}
                >
                  Body text sample
                </div>
                {font.accentFont && (
                  <div
                    className="text-xs italic"
                    style={{ fontFamily: font.accentFont }}
                  >
                    Accent text
                  </div>
                )}
              </div>
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}

export function BookCustomization({ options, onChange }: BookCustomizationProps) {
  const handleThemeChange = (value: string) => {
    onChange({ ...options, theme: value });
  };

  const handleFontChange = (value: string) => {
    onChange({ ...options, font: value });
  };

  const handleChapterStyleChange = (value: string) => {
    onChange({ ...options, chapterStyle: value });
  };

  const handleCoverChange = (value: string) => {
    onChange({ ...options, cover: value });
  };

  const handleCoverTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ ...options, coverTitle: e.target.value });
  };

  const handleCoverSubtitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ ...options, coverSubtitle: e.target.value });
  };

  const handleCustomImageToggle = (checked: boolean) => {
    onChange({ ...options, useCustomCoverImage: checked });
  };

  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          onChange({
            ...options,
            coverImage: event.target.result as string,
            useCustomCoverImage: true
          });
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Dedication handlers
  const handleDedicationToggle = (checked: boolean) => {
    onChange({ ...options, includeDedication: checked });
  };

  const handleDedicationChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange({ ...options, dedication: e.target.value });
  };

  // Family quotes handlers
  const handleQuotesToggle = (checked: boolean) => {
    onChange({ ...options, includeQuotes: checked });
  };

  const handleQuoteChange = (index: number, value: string) => {
    const newQuotes = [...(options.familyQuotes || [])];
    newQuotes[index] = value;
    onChange({ ...options, familyQuotes: newQuotes });
  };

  const handleAddQuote = () => {
    const newQuotes = [...(options.familyQuotes || []), ''];
    onChange({ ...options, familyQuotes: newQuotes });
  };

  const handleRemoveQuote = (index: number) => {
    const newQuotes = [...(options.familyQuotes || [])];
    newQuotes.splice(index, 1);
    onChange({ ...options, familyQuotes: newQuotes });
  };

  return (
    <div className="w-full">
      <Tabs defaultValue="theme" className="w-full">
          <TabsList className="w-full grid grid-cols-5 p-1 h-auto min-h-[2.5rem]">
            <TabsTrigger value="theme" className="px-1 py-1 h-8 flex items-center justify-center text-[10px]">
              <Palette className="h-3 w-3 mr-1" />
              Theme
            </TabsTrigger>
            <TabsTrigger value="font" className="px-1 py-1 h-8 flex items-center justify-center text-[10px]">
              <Type className="h-3 w-3 mr-1" />
              Font
            </TabsTrigger>
            <TabsTrigger value="chapter" className="px-1 py-1 h-8 flex items-center justify-center text-[10px]">
              <BookText className="h-3 w-3 mr-1" />
              Ch.
            </TabsTrigger>
            <TabsTrigger value="cover" className="px-1 py-1 h-8 flex items-center justify-center text-[10px]">
              <BookCopy className="h-3 w-3 mr-1" />
              Cover
            </TabsTrigger>
            <TabsTrigger value="pages" className="px-1 py-1 h-8 flex items-center justify-center text-[10px]">
              <BookOpen className="h-3 w-3 mr-1" />
              Pages
            </TabsTrigger>
          </TabsList>

          <TabsContent value="theme" className="pt-2">
            <div className="space-y-4">
              <Label className="text-xs font-medium">Select Theme</Label>

              {/* Theme Categories */}
              <div className="space-y-4">
                <ThemeCategory
                  label="Classic & Traditional"
                  themes={themes.slice(0, 3)}
                  selectedTheme={options.theme}
                  onThemeChange={handleThemeChange}
                />

                <ThemeCategory
                  label="Rustic & Earthy"
                  themes={themes.slice(3, 6)}
                  selectedTheme={options.theme}
                  onThemeChange={handleThemeChange}
                />

                <ThemeCategory
                  label="Modern & Contemporary"
                  themes={themes.slice(6, 9)}
                  selectedTheme={options.theme}
                  onThemeChange={handleThemeChange}
                />

                <ThemeCategory
                  label="Vintage & Retro"
                  themes={themes.slice(9, 11)}
                  selectedTheme={options.theme}
                  onThemeChange={handleThemeChange}
                />

                <ThemeCategory
                  label="Colorful"
                  themes={themes.slice(11)}
                  selectedTheme={options.theme}
                  onThemeChange={handleThemeChange}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="font" className="pt-2">
            <div className="space-y-4">
              <Label className="text-xs font-medium">Select Font Style</Label>

              {/* Font Categories */}
              <div className="space-y-4">
                <FontCategory
                  label="Elegant & Classic"
                  fonts={fonts.slice(0, 4)}
                  selectedFont={options.font}
                  onFontChange={handleFontChange}
                />

                <FontCategory
                  label="Modern & Contemporary"
                  fonts={fonts.slice(4, 8)}
                  selectedFont={options.font}
                  onFontChange={handleFontChange}
                />

                <FontCategory
                  label="Traditional"
                  fonts={fonts.slice(8, 11)}
                  selectedFont={options.font}
                  onFontChange={handleFontChange}
                />

                <FontCategory
                  label="Handwritten & Script"
                  fonts={fonts.slice(11, 15)}
                  selectedFont={options.font}
                  onFontChange={handleFontChange}
                />

                <FontCategory
                  label="Display & Statement"
                  fonts={fonts.slice(15)}
                  selectedFont={options.font}
                  onFontChange={handleFontChange}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="chapter" className="pt-2">
            <CustomizationTabContent
              label="Select Chapter Style"
              options={chapterStyles}
              selectedValue={options.chapterStyle}
              onChange={handleChapterStyleChange}
              getOptionId={(style) => style.id}
              getOptionName={(style) => style.name}
              getOptionDescription={(style) => style.description}
              renderPreview={(style) => (
                <div className="border-t pt-1 text-center font-serif">
                  {style.preview.split('\n').map((line, i) => (
                    <div key={i} className={i === 0 ? 'text-sm font-bold' : 'text-xs'}>
                      {line}
                    </div>
                  ))}
                </div>
              )}
            />
          </TabsContent>

          <TabsContent value="cover" className="pt-2">
            <div className="space-y-3">
              <CustomizationTabContent
                label="Cover Design"
                options={coverDesigns}
                selectedValue={options.cover}
                onChange={handleCoverChange}
                getOptionId={(cover) => cover.id}
                getOptionName={(cover) => cover.name}
                getOptionDescription={(cover) => cover.description}
                renderPreview={(cover) => (
                  <div
                    className="h-24 rounded-md overflow-hidden relative"
                    style={{
                      backgroundColor: cover.backgroundColor || 'transparent',
                      backgroundImage: cover.backgroundImage ? `url(${cover.backgroundImage})` : 'none',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}
                  >
                    {/* Add overlay for better text readability on image backgrounds */}
                    {cover.backgroundImage && (
                      <div className="absolute inset-0 bg-black bg-opacity-30"></div>
                    )}
                    <div className={`absolute inset-0 flex flex-col items-center justify-${cover.titlePosition === 'top' ? 'start' : cover.titlePosition === 'bottom' ? 'end' : 'center'} p-2 text-center z-10`}>
                      <div
                        className="text-xs font-bold"
                        style={{ color: cover.textColor || '#000' }}
                      >
                        {options.coverTitle || 'Family Cookbook'}
                      </div>
                      <div
                        className="text-[10px]"
                        style={{ color: cover.textColor || '#000' }}
                      >
                        {options.coverSubtitle || 'Treasured Recipes'}
                      </div>
                    </div>
                  </div>
                )}
              />

              <div className="space-y-2 mt-2">
                <Label className="text-xs">Cover Text</Label>
                <div className="space-y-2">
                  <Input
                    placeholder="Cover Title"
                    value={options.coverTitle || ''}
                    onChange={handleCoverTitleChange}
                    className="text-xs h-8"
                  />
                  <Input
                    placeholder="Cover Subtitle"
                    value={options.coverSubtitle || ''}
                    onChange={handleCoverSubtitleChange}
                    className="text-xs h-8"
                  />
                </div>
              </div>

              <div className="space-y-2 mt-4 border-t pt-3">
                <div className="flex items-center justify-between">
                  <Label className="text-xs">Use Custom Cover Image</Label>
                  <Switch
                    checked={options.useCustomCoverImage}
                    onCheckedChange={handleCustomImageToggle}
                  />
                </div>

                {options.useCustomCoverImage && (
                  <div className="space-y-2 mt-2">
                    <div className="flex flex-col gap-2">
                      {options.coverImage ? (
                        <div className="relative h-32 rounded-md overflow-hidden">
                          <img
                            src={options.coverImage}
                            alt="Custom cover"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                            <label
                              htmlFor="cover-image-upload"
                              className="bg-white text-black text-xs px-2 py-1 rounded cursor-pointer flex items-center"
                            >
                              <Upload className="h-3 w-3 mr-1" />
                              Change Image
                            </label>
                          </div>
                        </div>
                      ) : (
                        <label
                          htmlFor="cover-image-upload"
                          className="h-32 border-2 border-dashed rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-accent/5"
                        >
                          <ImageIcon className="h-6 w-6 text-muted-foreground mb-2" />
                          <span className="text-xs text-muted-foreground">Click to upload image</span>
                          <span className="text-[10px] text-muted-foreground mt-1">Recommended: 1200×800px</span>
                        </label>
                      )}
                      <input
                        id="cover-image-upload"
                        type="file"
                        accept="image/*"
                        onChange={handleCoverImageChange}
                        className="hidden"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pages" className="pt-2">
            <div className="space-y-4">
              {/* Dedication Section */}
              <div className="space-y-2 border rounded-md p-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Dedication Page</Label>
                  <Switch
                    checked={options.includeDedication}
                    onCheckedChange={handleDedicationToggle}
                  />
                </div>

                {options.includeDedication && (
                  <div className="space-y-2 mt-2">
                    <Label className="text-xs text-muted-foreground">
                      Add a personal dedication to appear at the beginning of your book
                    </Label>
                    <Textarea
                      placeholder="To my family..."
                      value={options.dedication || ''}
                      onChange={handleDedicationChange}
                      className="text-xs min-h-[100px] resize-none"
                     maxLength={500}
                    />
                   <div className="text-xs text-muted-foreground text-right mt-1">
                     {(options.dedication?.length || 0)}/500 characters
                   </div>
                  </div>
                )}
              </div>

              {/* Family Quotes Section */}
              <div className="space-y-2 border rounded-md p-2">
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Family Quotes</Label>
                  <Switch
                    checked={options.includeQuotes}
                    onCheckedChange={handleQuotesToggle}
                  />
                </div>

                {options.includeQuotes && (
                  <div className="space-y-3 mt-2">
                    <Label className="text-xs text-muted-foreground">
                      Add meaningful quotes to appear throughout your book
                    </Label>

                    {/* List of quotes */}
                    <div className="space-y-2">
                      {(options.familyQuotes || []).map((quote, index) => (
                        <div key={index} className="flex gap-2 items-start">
                          <Textarea
                            placeholder="Enter a quote..."
                            value={quote}
                            onChange={(e) => handleQuoteChange(index, e.target.value)}
                            className="text-xs min-h-[60px] resize-none flex-1"
                          />
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveQuote(index)}
                            className="h-8 w-8 text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>

                    {/* Add quote button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleAddQuote}
                      className="w-full text-xs h-8 mt-2"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add Quote
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>
    </div>
  );
}

// Export the predefined options for use in other components
export { themes, fonts, chapterStyles, coverDesigns };
