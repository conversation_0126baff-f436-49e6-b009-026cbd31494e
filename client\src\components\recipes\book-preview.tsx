import { useState, useEffect, useMemo } from 'react';
import { X, ChevronLeft, ChevronRight, Book, Settings, Download, Loader2, Eye, Truck, CreditCard, Package } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { RecipeImages } from './recipe-images';
import { BookCustomization, BookCustomizationOptions, themes, fonts, chapterStyles, coverDesigns } from './book-customization';
import { ShippingAddressForm } from './shipping-address-form';
import { useToast } from '@/hooks/use-toast';
import { pdf } from '@react-pdf/renderer';
import { RecipeBookPDFSimple } from './recipe-book-pdf-simple';

// Page dimension constants for Blurb book formats
// These values are based on standard book dimensions and will be used for pagination
const PAGE_DIMENSIONS = {
  // Standard book dimensions in pixels (assuming 96 DPI)
  WIDTH: 800, // Max width of the book page in preview
  HEIGHT: 1000, // Standard height for content area
  MARGIN: 50, // Standard margin
  CONTENT_HEIGHT: 900, // Available content height (HEIGHT - margins)

  // Content size estimations (in pixels)
  TITLE_HEIGHT: 60,
  SUBTITLE_HEIGHT: 40,
  PARAGRAPH_HEIGHT: 24,
  IMAGE_HEIGHT: 300,
  INGREDIENT_ITEM_HEIGHT: 24,
  INSTRUCTION_STEP_HEIGHT: 100,
  HEADER_HEIGHT: 50,
  FOOTER_HEIGHT: 40,
};

// Function to generate dynamic styles based on customization options
const generateBookPreviewStyles = (options: BookCustomizationOptions) => {
  // Get the selected theme, font, and chapter style
  const selectedTheme = themes.find(t => t.id === options.theme) || themes[0];
  const selectedFont = fonts.find(f => f.id === options.font) || fonts[0];

  return `
  .preview-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid ${selectedTheme.colors.accent}30;
    background-color: white;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .preview-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    background-color: #f8f8f8;
  }

  .book-page {
    background-color: ${selectedTheme.colors.background};
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    border-radius: 4px;
    max-width: 800px;
    margin: 0 auto;
    margin-bottom: 2rem;
    color: ${selectedTheme.colors.text};
  }

  .book-page h1 {
    font-family: ${selectedFont.headingFont};
    font-weight: 700;
    color: ${selectedTheme.colors.heading};
  }

  .book-page h2, .book-page h3, .book-page h4 {
    font-family: ${selectedFont.headingFont};
    color: ${selectedTheme.colors.heading};
  }

  .book-page p {
    font-family: ${selectedFont.bodyFont};
    line-height: 1.6;
  }

  .step-number {
    font-family: ${selectedFont.headingFont};
    color: ${selectedTheme.colors.accent}30;
    font-size: 4rem;
    line-height: 1;
    font-weight: 300;
  }

  .recipe-image {
    border-radius: 4px;
    overflow: hidden;
  }

  .recipe-details {
    font-family: ${selectedFont.bodyFont};
    font-size: 0.9rem;
    color: ${selectedTheme.colors.text};
  }

  .recipe-details strong {
    color: ${selectedTheme.colors.heading};
  }

  /* Accent text for handwritten style */
  .accent-text {
    font-family: ${selectedFont.accentFont || selectedFont.bodyFont};
    color: ${selectedTheme.colors.accent};
  }

  /* Scrollbar styles */
  .preview-content::-webkit-scrollbar {
    width: 8px;
  }

  .preview-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .preview-content::-webkit-scrollbar-thumb {
    background: ${selectedTheme.colors.accent};
    border-radius: 4px;
  }

  .preview-content::-webkit-scrollbar-thumb:hover {
    background: ${selectedTheme.colors.heading};
  }
`;
}

// Utility function to estimate content height
const estimateContentHeight = (recipe: Recipe): number => {
  if (!recipe) return 0;

  let totalHeight = 0;

  // Add header heights
  totalHeight += PAGE_DIMENSIONS.TITLE_HEIGHT; // Recipe category
  totalHeight += PAGE_DIMENSIONS.SUBTITLE_HEIGHT; // Recipe title

  // Add image and description section height
  totalHeight += PAGE_DIMENSIONS.IMAGE_HEIGHT; // Recipe image

  // Add ingredients section height
  totalHeight += PAGE_DIMENSIONS.HEADER_HEIGHT; // "Ingredients:" header
  totalHeight += Math.ceil(recipe.ingredients.length / 2) * PAGE_DIMENSIONS.INGREDIENT_ITEM_HEIGHT; // Ingredients in two columns

  // Add instructions section height
  totalHeight += PAGE_DIMENSIONS.HEADER_HEIGHT; // "Instructions:" header
  totalHeight += recipe.instructions.reduce((acc, instruction) => {
    // Count newlines to estimate instruction complexity
    const lines = instruction.split('\n').length;
    return acc + PAGE_DIMENSIONS.INSTRUCTION_STEP_HEIGHT * Math.max(1, lines);
  }, 0);

  // Add footer
  totalHeight += PAGE_DIMENSIONS.FOOTER_HEIGHT;

  // Add additional images section if present
  if (recipe.images && recipe.images.length > 1) {
    totalHeight += PAGE_DIMENSIONS.IMAGE_HEIGHT / 2; // Half height for additional images
  }

  return totalHeight;
};

// Function to determine if a recipe needs pagination
const shouldPaginateRecipe = (recipe: Recipe): boolean => {
  const contentHeight = estimateContentHeight(recipe);
  return contentHeight > PAGE_DIMENSIONS.CONTENT_HEIGHT;
};

// Helper function to get step titles based on content
const getStepTitle = (index: number, instruction: string) => {
  const lowerInstruction = instruction.toLowerCase();
  if (lowerInstruction.includes('preheat') || lowerInstruction.includes('prepare')) return 'Prepare the Chicken:';
  if (lowerInstruction.includes('vegetable')) return 'Cook the Vegetables:';
  if (lowerInstruction.includes('sear') || lowerInstruction.includes('heat')) return 'Sear the Chicken:';
  if (lowerInstruction.includes('bake') || lowerInstruction.includes('oven')) return 'Bake:';
  if (lowerInstruction.includes('finish') || lowerInstruction.includes('garnish')) return 'Finish:';
  if (lowerInstruction.includes('serve') || lowerInstruction.includes('plate')) return 'Serve:';

  // Default titles based on position
  const defaultTitles = [
    'Prepare the Ingredients:',
    'Cook the Main Components:',
    'Combine Everything:',
    'Finish the Dish:',
    'Final Touches:',
    'Serve:'
  ];

  return index < defaultTitles.length ? defaultTitles[index] : `Step ${index + 1}:`;
};

interface Ingredient {
  name: string;
  amount: number;
  unit: string;
}

// Interface for paginated content
interface PaginatedContent {
  type: 'header' | 'image' | 'description' | 'ingredients' | 'instructions' | 'footer' | 'additional-images';
  content: any;
  height: number;
}

// Interface for a paginated recipe
interface PaginatedRecipe {
  recipeId: number;
  pages: PaginatedContent[][];
  totalPages: number;
}

interface Recipe {
  id: number;
  title: string;
  description: string;
  images: string[];
  ingredients: Ingredient[];
  instructions: string[];
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  tags?: string[];
  contributor: {
    id: number;
    name: string;
  };
  // Pagination properties
  _paginatedContent?: PaginatedRecipe;
}

interface BookPreviewProps {
  recipes: Recipe[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customizationOptions?: BookCustomizationOptions;
  onCustomizationChange?: (options: BookCustomizationOptions) => void;
  showCustomization?: boolean;
}

// Helper function to render chapter title based on selected style and category
const renderChapterTitle = (category: string, pageIndex: number, options?: BookCustomizationOptions) => {
  // Get the selected chapter style
  const selectedChapterStyle = options ?
    chapterStyles.find(s => s.id === options.chapterStyle) || chapterStyles[0] :
    chapterStyles[0];

  switch (selectedChapterStyle.id) {
    case 'decorative':
      return (
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold uppercase">✦ {category} ✦</h1>
        </div>
      );
    case 'numbered':
      return (
        <div className="text-center mb-6">
          <div className="text-5xl font-bold mb-2"> {pageIndex + 1}</div>
          <h1 className="text-2xl uppercase">{category}</h1>
        </div>
      );
    case 'minimal':
      return (
        <div className="mb-6">
          <h1 className="text-3xl font-bold tracking-widest uppercase text-center">{category}</h1>
        </div>
      );
    case 'simple':
    default:
      return (
        <h1 className="text-3xl font-bold mb-6 uppercase">{category}</h1>
      );
  }
};

// Function to paginate a recipe into multiple pages
const paginateRecipe = (recipe: Recipe, recipeCategory: string): PaginatedRecipe => {
  if (!recipe) {
    return { recipeId: 0, pages: [], totalPages: 0 };
  }

  // If recipe is already paginated, return the cached pagination
  if (recipe._paginatedContent) {
    return recipe._paginatedContent;
  }

  const pages: PaginatedContent[][] = [];
  let currentPage: PaginatedContent[] = [];
  let currentPageHeight = 0;

  // Helper function to add content to the current page or create a new page
  const addContent = (content: PaginatedContent) => {
    // If adding this content would exceed page height, start a new page
    if (currentPageHeight + content.height > PAGE_DIMENSIONS.CONTENT_HEIGHT && currentPage.length > 0) {
      pages.push([...currentPage]);
      currentPage = [];
      currentPageHeight = 0;
    }

    // Add content to current page
    currentPage.push(content);
    currentPageHeight += content.height;
  };

  // Add recipe header (always on first page)
  addContent({
    type: 'header',
    content: {
      category: recipeCategory,
      title: recipe.title
    },
    height: PAGE_DIMENSIONS.TITLE_HEIGHT + PAGE_DIMENSIONS.SUBTITLE_HEIGHT
  });

  // Add image and description section
  addContent({
    type: 'image',
    content: {
      image: recipe.images && recipe.images.length > 0 ? recipe.images[0] : null,
      description: recipe.description,
      details: {
        servings: recipe.servings || 2,
        prepTime: recipe.prepTime || 15,
        cookTime: recipe.cookTime || 25
      }
    },
    height: PAGE_DIMENSIONS.IMAGE_HEIGHT
  });

  // Add ingredients section
  const ingredientsHeight = PAGE_DIMENSIONS.HEADER_HEIGHT +
    Math.ceil(recipe.ingredients.length / 2) * PAGE_DIMENSIONS.INGREDIENT_ITEM_HEIGHT;

  addContent({
    type: 'ingredients',
    content: recipe.ingredients,
    height: ingredientsHeight
  });

  // Add instructions section (can be split across pages)
  // Make sure instructions are not empty
  if (recipe.instructions && recipe.instructions.length > 0) {
    addContent({
      type: 'instructions',
      content: recipe.instructions,
      height: PAGE_DIMENSIONS.HEADER_HEIGHT + recipe.instructions.reduce((acc, instruction) => {
        const lines = instruction.split('\n').length;
        return acc + PAGE_DIMENSIONS.INSTRUCTION_STEP_HEIGHT * Math.max(1, lines);
      }, 0)
    });
  }

  // Add footer
  addContent({
    type: 'footer',
    content: null,
    height: PAGE_DIMENSIONS.FOOTER_HEIGHT
  });

  // Add additional images if present
  if (recipe.images && recipe.images.length > 1) {
    addContent({
      type: 'additional-images',
      content: recipe.images.slice(1),
      height: PAGE_DIMENSIONS.IMAGE_HEIGHT / 2
    });
  }

  // Add the last page if it has content
  if (currentPage.length > 0) {
    pages.push(currentPage);
  }

  // Create the paginated recipe
  const paginatedRecipe: PaginatedRecipe = {
    recipeId: recipe.id,
    pages,
    totalPages: pages.length
  };

  // Cache the pagination in the recipe object
  recipe._paginatedContent = paginatedRecipe;

  return paginatedRecipe;
};

// Function to render the dedication page
function renderDedicationPage(options: BookCustomizationOptions) {
  if (!options.includeDedication || !options.dedication) return null;

  // Get the selected font
  const selectedFont = fonts.find(f => f.id === options.font) || fonts[0];
  // Get the selected theme
  const selectedTheme = themes.find(t => t.id === options.theme) || themes[0];

  return (
    <div className="flex flex-col items-center justify-center min-h-[800px] py-16">
      <div className="max-w-md mx-auto text-center">
        <h1
          className="text-2xl font-bold mb-12"
          style={{
            fontFamily: selectedFont.headingFont,
            color: selectedTheme.colors.heading
          }}
        >
          Dedication
        </h1>

        <div
          className="text-lg italic mb-8"
          style={{
            fontFamily: selectedFont.bodyFont,
            color: selectedTheme.colors.text
          }}
        >
          {options.dedication}
        </div>
      </div>
    </div>
  );
}

// Function to render a quote page
function renderQuotePage(quote: string, options: BookCustomizationOptions) {

  if (!quote) {
    console.log('Quote is empty, not rendering');
    return null;
  }

  // Get the selected font
  const selectedFont = fonts.find(f => f.id === options.font) || fonts[0];
  // Get the selected theme
  const selectedTheme = themes.find(t => t.id === options.theme) || themes[0];

  return (
    <div className="flex flex-col items-center justify-center min-h-[800px] py-16">
      <div
        className="max-w-md mx-auto text-center px-8 py-12 border-t border-b"
        style={{
          borderColor: selectedTheme.colors.accent
        }}
      >
        <div
          className="text-xl italic mb-6"
          style={{
            fontFamily: selectedFont.accentFont || selectedFont.bodyFont,
            color: selectedTheme.colors.text
          }}
        >
          "{quote}"
        </div>
      </div>
    </div>
  );
}

// Function to render the cover page
function renderCoverPage(options: BookCustomizationOptions, projectName?: string) {
  // Get the selected cover design
  const selectedCover = coverDesigns.find(c => c.id === options.cover) || coverDesigns[0];

  // Default title and subtitle if not provided
  const title = options.coverTitle || projectName || 'Family Cookbook';
  const subtitle = options.coverSubtitle || 'Treasured Recipes';

  return (
    <div className="flex flex-col items-center justify-center min-h-[800px]">
      <div
        className="w-full h-full min-h-[800px] rounded-md overflow-hidden relative flex flex-col items-center"
        style={{
          backgroundColor: options.useCustomCoverImage ? 'transparent' : (selectedCover.backgroundColor || 'transparent'),
          backgroundImage: options.useCustomCoverImage && options.coverImage
            ? `url(${options.coverImage})`
            : selectedCover.backgroundImage
              ? `url(${selectedCover.backgroundImage})`
              : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        {/* Add a subtle overlay for better text readability on image backgrounds */}
        {((options.useCustomCoverImage && options.coverImage) || selectedCover.backgroundImage) && (
          <div className="absolute inset-0 bg-black bg-opacity-30"></div>
        )}
        <div className={`absolute inset-0 flex flex-col items-center justify-${selectedCover.titlePosition === 'top' ? 'start pt-16' : selectedCover.titlePosition === 'bottom' ? 'end pb-16' : 'center'} p-8 text-center z-10`}>
          <h1
            className="text-4xl md:text-5xl font-bold mb-4"
            style={{
              color: selectedCover.textColor || '#000',
              fontFamily: (fonts.find(f => f.id === options.font) || fonts[0]).headingFont
            }}
          >
            {title}
          </h1>
          <h2
            className="text-xl md:text-2xl"
            style={{
              color: selectedCover.textColor || '#000',
              fontFamily: (fonts.find(f => f.id === options.font) || fonts[0]).headingFont
            }}
          >
            {subtitle}
          </h2>
        </div>
      </div>
    </div>
  );
}

function renderRecipePage(
  recipe: Recipe,
  splitIngredients: (ingredients: Ingredient[]) => Ingredient[][],
  currentPage: number,
  options?: BookCustomizationOptions,
) {
  if (!recipe) return null;

  const [leftIngredients, rightIngredients] = splitIngredients(recipe.ingredients);

  // Determine recipe category based on tags or default to "MEALS WITH CHICKEN"
  const recipeCategory = recipe.tags && recipe.tags.length > 0
    ? `MEALS WITH ${recipe.tags[0].toUpperCase()}`
    : "MEALS WITH CHICKEN";

  return (
    <div>
      {/* Recipe Category Header */}
      {renderChapterTitle(recipeCategory, currentPage, options)}

      {/* Recipe Title */}
      <h2 className="text-xl font-bold mb-6 uppercase">{recipe.title}</h2>

      <div className="flex flex-col md:flex-row gap-6 mb-6">
        {/* Recipe Image */}
        {recipe.images && recipe.images.length > 0 ? (
          <div className="w-full md:w-1/2 recipe-image">
            <RecipeImages
              images={[recipe.images[0]]}
              recipeTitle={recipe.title}
              className="w-full h-auto object-cover"
            />
          </div>
        ) : (
          <div className="w-full md:w-1/2 bg-gray-100 flex items-center justify-center h-[300px] recipe-image">
            <span className="text-gray-400">No image available</span>
          </div>
        )}

        {/* Recipe Description */}
        <div className="w-full md:w-1/2 flex flex-col justify-between">
          <p className="text-sm mb-4">{recipe.description}</p>

          {/* Recipe Details */}
          <div className="mt-auto recipe-details">
            <p><strong>Serves:</strong> {recipe.servings || 2}</p>
            <p><strong>Prep Time:</strong> {recipe.prepTime || 15} minutes</p>
            <p><strong>Cook Time:</strong> {recipe.cookTime || 25}-30 minutes</p>
          </div>
        </div>
      </div>

      {/* Ingredients Section */}
      <h3 className="text-xl font-bold mb-4">Ingredients:</h3>
      <div className="flex flex-col md:flex-row gap-8 mb-8">
        <ul className="list-disc pl-5 space-y-2 w-full md:w-1/2">
          {leftIngredients.map((ingredient, idx) => (
            <li key={idx}>
              {ingredient.amount} {ingredient.unit} {ingredient.name}
            </li>
          ))}
        </ul>
        <ul className="list-disc pl-5 space-y-2 w-full md:w-1/2">
          {rightIngredients.map((ingredient, idx) => (
            <li key={idx}>
              {ingredient.amount} {ingredient.unit} {ingredient.name}
            </li>
          ))}
        </ul>
      </div>

      {/* Instructions Section */}
      <h3 className="text-xl font-bold mb-4">Instructions:</h3>
      <div className="space-y-8">
        {recipe.instructions.map((instruction, idx) => (
          <div key={idx} className="flex gap-6">
            <div className="step-number">
              {idx + 1}
            </div>
            <div className="flex-1">
              <h4 className="font-bold mb-2">
                {getStepTitle(idx, instruction)}
              </h4>
              <ul className="list-disc pl-5 space-y-2">
                {instruction.split('\n').map((step, stepIdx) => (
                  <li key={stepIdx}>{step}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>

      {/* Footer message */}
      <p className="mt-8 text-center">Enjoy your healthy and delicious meal!</p>

      {/* Additional images - only show if there are more than one image */}
      {recipe.images && recipe.images.length > 1 && (
        <div className="mt-8 grid grid-cols-2 gap-4">
          {recipe.images.slice(1, 3).map((image, idx) => (
            <div key={idx} className="recipe-image h-48">
              <RecipeImages
                images={[image]}
                recipeTitle={`${recipe.title} additional image ${idx + 1}`}
                className="w-full h-full object-cover"
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export function BookPreview({
  recipes,
  open,
  onOpenChange,
  customizationOptions,
  onCustomizationChange,
  showCustomization = false
}: BookPreviewProps) {
  // Start at page 0 (cover) by default
  const [currentPage, setCurrentPage] = useState(0);
  // Initialize the panel visibility based on the showCustomization prop
  const [showCustomizationPanel, setShowCustomizationPanel] = useState(showCustomization);
  // Track the current sub-page for paginated recipes
  const [recipeSubPage, setRecipeSubPage] = useState<Record<number, number>>({});
  // Track paginated recipes
  const [paginatedRecipes, setPaginatedRecipes] = useState<Record<number, PaginatedRecipe>>({});
  // Print order state
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [orderProgress, setOrderProgress] = useState(0);
  const [showAddressForm, setShowAddressForm] = useState(false);

  // Blurb API testing state
  const [showShippingCalculator, setShowShippingCalculator] = useState(false);
  const [showOrderTracking, setShowOrderTracking] = useState(false);
  const [userOrders, setUserOrders] = useState([]);
  const [shippingCost, setShippingCost] = useState(null);
  const [selectedOrderTracking, setSelectedOrderTracking] = useState(null);
  const [trackingDetails, setTrackingDetails] = useState(null);

  const { toast } = useToast();

  // Default customization options
  const [options, setOptions] = useState<BookCustomizationOptions>({
    theme: 'classic',
    font: 'elegant',
    chapterStyle: 'simple',
    cover: 'classic',
    coverImage: '',
    useCustomCoverImage: false,
    dedication: '',
    familyQuotes: [],
    includeDedication: false,
    includeQuotes: false
  });

  // Use provided options if available
  useEffect(() => {
    if (customizationOptions) {
      // Log the incoming customization options
      console.log('Received customization options in BookPreview:', customizationOptions);

      // Ensure familyQuotes is an array
      const processedOptions = { ...customizationOptions };

      if (processedOptions.familyQuotes) {
        if (!Array.isArray(processedOptions.familyQuotes)) {
          try {
            processedOptions.familyQuotes = JSON.parse(processedOptions.familyQuotes as unknown as string);
          } catch (e) {
            console.error('Error parsing familyQuotes in BookPreview:', e);
            processedOptions.familyQuotes = [];
          }
        }

        // Log the processed quotes
        console.log('Processed familyQuotes in BookPreview:', processedOptions.familyQuotes);
      }

      setOptions(processedOptions);
    }
  }, [customizationOptions]);

  // Calculate additional pages (cover, dedication, quotes)
  const hasDedication = options.includeDedication && options.dedication;
  const hasQuotes = options.includeQuotes && options.familyQuotes && options.familyQuotes.length > 0;
  const quotePages = hasQuotes ? options.familyQuotes!.length : 0;
  const additionalPages = 1 + (hasDedication ? 1 : 0) + quotePages; // Cover + dedication + quotes

  // Include all pages in total
  const totalPages = recipes.length + additionalPages;

  // Track what type of page we're showing
  const isShowingCover = currentPage === 0;
  const isShowingDedication = hasDedication && currentPage === 1;

  // Function to determine if current page is a quote page
  const getQuoteIndex = () => {
    if (!hasQuotes) return -1;
    const quoteStartPage = 1 + (hasDedication ? 1 : 0);
    if (currentPage >= quoteStartPage && currentPage < quoteStartPage + quotePages) {
      return currentPage - quoteStartPage;
    }
    return -1;
  };

  const isShowingQuote = getQuoteIndex() >= 0;

  // Calculate the recipe index based on additional pages
  const getRecipeIndex = () => {
    return currentPage - additionalPages;
  };

  // Process recipes for pagination
  useEffect(() => {
    if (recipes.length === 0) return;

    // Create a new object to store paginated recipes
    const newPaginatedRecipes: Record<number, PaginatedRecipe> = {};

    // Process each recipe
    recipes.forEach(recipe => {
      // Determine recipe category
      const recipeCategory = recipe.tags && recipe.tags.length > 0
        ? `MEALS WITH ${recipe.tags[0].toUpperCase()}`
        : "MEALS WITH CHICKEN";

      // Check if recipe needs pagination
      if (shouldPaginateRecipe(recipe)) {
        // Paginate the recipe
        const paginatedRecipe = paginateRecipe(recipe, recipeCategory);
        newPaginatedRecipes[recipe.id] = paginatedRecipe;
      }
    });

    // Update paginated recipes state
    setPaginatedRecipes(newPaginatedRecipes);
  }, [recipes]);

  // Get the current recipe and its pagination info
  const getCurrentRecipe = () => {
    const recipeIndex = getRecipeIndex();
    if (recipeIndex < 0 || recipeIndex >= recipes.length) return null;

    const recipe = recipes[recipeIndex];
    if (!recipe) return null;

    // Get the current sub-page for this recipe
    const subPageIndex = recipeSubPage[recipe.id] || 0;

    // Get pagination info for this recipe
    const paginationInfo = paginatedRecipes[recipe.id];

    return {
      recipe,
      subPageIndex,
      isPaginated: !!paginationInfo,
      totalSubPages: paginationInfo?.totalPages || 1
    };
  };

  // Generate styles based on current options
  const dynamicStyles = useMemo(() => {
    return generateBookPreviewStyles(options);
  }, [options]);

  // Add custom styles to the document
useEffect(() => {
  let styleElement = document.querySelector<HTMLStyleElement>(
    'style[data-book-preview]'
  );
  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.setAttribute('data-book-preview', 'true');
    document.head.appendChild(styleElement);
  }
  styleElement.textContent = dynamicStyles;
  return () => {
    // Remove only on unmount
    styleElement?.remove();
  };
}, [dynamicStyles]);

  // Handle customization changes
  const handleCustomizationChange = (newOptions: BookCustomizationOptions) => {
    setOptions(newOptions);
    if (onCustomizationChange) {
      onCustomizationChange(newOptions);
    }
  };

  // Generate PDF using React PDF (matches preview exactly)
  const generatePDFFromPreview = async (onProgress?: (progress: number) => void): Promise<Blob> => {
    try {
      onProgress?.(10);

      // Create the React PDF document with the same data as the preview
      const pdfDocument = (
        <RecipeBookPDFSimple
          recipes={recipes}
          options={options}
          projectName="Family Recipe Collection"
        />
      );

      onProgress?.(50);

      // Generate the PDF blob
      const pdfBlob = await pdf(pdfDocument).toBlob();

      onProgress?.(100);
      return pdfBlob;

    } catch (error) {
      console.error('Error generating PDF with React PDF:', error);
      throw error;
    }
  };

  // Handle print order creation
  const handleCreatePrintOrder = async (shippingAddress: any) => {
    if (recipes.length === 0) {
      toast({
        title: "No Recipes",
        description: "Please add some recipes before creating a print order.",
        variant: "destructive",
      });
      return;
    }

    setIsCreatingOrder(true);
    setOrderProgress(0);

    try {
      toast({
        title: "Generating PDF",
        description: "Creating your recipe book PDF...",
      });

      // Generate PDF from the exact preview content
      const pdfBlob = await generatePDFFromPreview((progress) => {
        setOrderProgress(progress * 0.7); // Use 70% of progress for PDF generation
      });

      setOrderProgress(70);

      // Create FormData to send PDF and other data
      const formData = new FormData();
      formData.append('pdf', pdfBlob, 'recipe-book.pdf');
      formData.append('customization', JSON.stringify(options));
      formData.append('shippingAddress', JSON.stringify(shippingAddress));
      formData.append('metadata', JSON.stringify({
        title: 'Family Recipe Collection',
        author: 'Family',
        description: 'A treasured collection of family recipes',
        createdAt: new Date().toISOString()
      }));

      setOrderProgress(80);

      // Send to our backend which will handle Blurb API integration
      const response = await fetch('/api/blurb/create-order', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      setOrderProgress(90);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create print order');
      }

      const result = await response.json();
      setOrderProgress(100);

      toast({
        title: "Print Order Created Successfully",
        description: `Your order has been submitted. Order ID: ${result.orderId}`,
      });

      setShowAddressForm(false);

    } catch (error) {
      console.error('Error creating print order:', error);
      toast({
        title: "Print Order Failed",
        description: error instanceof Error ? error.message : "There was an error creating your print order. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingOrder(false);
      setOrderProgress(0);
    }
  };

  // Test S3 configuration (for debugging)
  const handleTestS3 = async () => {
    try {
      const response = await fetch('/api/pdf/debug-s3', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get S3 debug info');
      }

      const result = await response.json();

      console.log('S3 Configuration:', result);

      toast({
        title: "S3 Configuration",
        description: `Bucket: ${result.config.bucketName}, Region: ${result.config.region}. Check console for details.`,
      });

    } catch (error) {
      console.error('Error getting S3 debug info:', error);
      toast({
        title: "S3 Debug Failed",
        description: error instanceof Error ? error.message : "Failed to get S3 debug info.",
        variant: "destructive",
      });
    }
  };

  // Test image accessibility (for debugging)
  const handleTestImages = async () => {
    if (recipes.length === 0) {
      toast({
        title: "No Recipes",
        description: "Please add some recipes before testing images.",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch('/api/pdf/test-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ recipes })
      });

      if (!response.ok) {
        throw new Error('Failed to test images');
      }

      const result = await response.json();

      console.log('Image test results:', result);

      toast({
        title: "Image Test Complete",
        description: `${result.summary.accessible}/${result.summary.total} images accessible. Check console for details.`,
        variant: result.summary.failed > 0 ? "destructive" : "default",
      });

    } catch (error) {
      console.error('Error testing images:', error);
      toast({
        title: "Image Test Failed",
        description: error instanceof Error ? error.message : "Failed to test images.",
        variant: "destructive",
      });
    }
  };

  // Test HTML generation (for debugging)
  const handleTestHTML = async () => {
    if (recipes.length === 0) {
      toast({
        title: "No Recipes",
        description: "Please add some recipes before testing HTML.",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch('/api/pdf/test-html', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          recipes,
          options,
          metadata: {
            title: 'Family Recipe Collection',
            author: 'Family',
            description: 'A treasured collection of family recipes'
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate test HTML');
      }

      const htmlContent = await response.text();

      // Open HTML in new window for inspection
      const newWindow = window.open();
      if (newWindow) {
        newWindow.document.write(htmlContent);
        newWindow.document.close();
      }

      toast({
        title: "HTML Generated",
        description: "Test HTML opened in new window for inspection.",
      });

    } catch (error) {
      console.error('Error generating test HTML:', error);
      toast({
        title: "HTML Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate test HTML.",
        variant: "destructive",
      });
    }
  };

  // Test Blurb API configuration
  const handleTestBlurbAPI = async () => {
    try {
      const response = await fetch('/api/blurb/test', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to test Blurb API');
      }

      const result = await response.json();

      toast({
        title: "Blurb API Test",
        description: `API configured: ${result.configured}. Environment: ${result.environment}`,
        variant: result.configured ? "default" : "destructive"
      });

    } catch (error) {
      console.error('Error testing Blurb API:', error);
      toast({
        title: "Blurb API Test Failed",
        description: error instanceof Error ? error.message : "Failed to test Blurb API.",
        variant: "destructive",
      });
    }
  };

  // Test address validation
  const handleTestAddressValidation = async () => {
    const testAddress = {
      name: "John Doe",
      street: "123 Main St",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "US"
    };

    try {
      const response = await fetch('/api/blurb/validate-address', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ address: testAddress })
      });

      if (!response.ok) {
        throw new Error('Failed to validate address');
      }

      const result = await response.json();

      toast({
        title: "Address Validation Test",
        description: `Address valid: ${result.isValid}. ${result.message}`,
        variant: result.isValid ? "default" : "destructive"
      });

    } catch (error) {
      console.error('Error testing address validation:', error);
      toast({
        title: "Address Validation Failed",
        description: error instanceof Error ? error.message : "Failed to test address validation.",
        variant: "destructive",
      });
    }
  };

  // Calculate shipping costs
  const handleCalculateShipping = async () => {
    const testAddress = {
      name: "John Doe",
      street: "123 Main St",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "US"
    };

    try {
      const response = await fetch('/api/blurb/calculate-shipping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          address: testAddress,
          customization: options,
          metadata: {
            title: 'Family Recipe Collection',
            recipes: recipes,
            recipeCount: recipes.length
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to calculate shipping');
      }

      const result = await response.json();
      setShippingCost(result);

      toast({
        title: "Shipping Calculated",
        description: `Book: $${result.bookSpecs.price}, Shipping: $${result.selectedShipping.cost}, Total: $${result.total}`,
      });

    } catch (error) {
      console.error('Error calculating shipping:', error);
      toast({
        title: "Shipping Calculation Failed",
        description: error instanceof Error ? error.message : "Failed to calculate shipping.",
        variant: "destructive",
      });
    }
  };

  // Get user orders
  const handleGetOrders = async () => {
    try {
      const response = await fetch('/api/blurb/orders', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get orders');
      }

      const result = await response.json();
      setUserOrders(result.orders);
      setShowOrderTracking(true);

      toast({
        title: "Orders Retrieved",
        description: `Found ${result.orders.length} orders. Click to view tracking details.`,
      });

      console.log('User orders:', result.orders);

    } catch (error) {
      console.error('Error getting orders:', error);
      toast({
        title: "Get Orders Failed",
        description: error instanceof Error ? error.message : "Failed to get orders.",
        variant: "destructive",
      });
    }
  };

  // Get detailed tracking for an order
  const handleGetTracking = async (orderId: number) => {
    try {
      const response = await fetch(`/api/blurb/order/${orderId}/tracking`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get tracking details');
      }

      const result = await response.json();
      setTrackingDetails(result);
      setSelectedOrderTracking(orderId);

      toast({
        title: "Tracking Details Retrieved",
        description: `Status: ${result.status}. ${result.trackingNumber ? `Tracking: ${result.trackingNumber}` : 'No tracking number yet.'}`,
      });

      console.log('Tracking details:', result);

    } catch (error) {
      console.error('Error getting tracking:', error);
      toast({
        title: "Tracking Failed",
        description: error instanceof Error ? error.message : "Failed to get tracking details.",
        variant: "destructive",
      });
    }
  };

  // Handle PDF download using server-side Puppeteer
  const handleDownloadPDF = async () => {
    if (recipes.length === 0) {
      toast({
        title: "No Recipes",
        description: "Please add some recipes before generating a PDF.",
        variant: "destructive",
      });
      return;
    }

    setIsCreatingOrder(true);
    setOrderProgress(0);

    try {
      toast({
        title: "Generating PDF",
        description: "Creating high-quality PDF that matches your preview exactly...",
      });

      setOrderProgress(20);

      // Send book data to server for Puppeteer PDF generation
      const response = await fetch('/api/pdf/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          recipes,
          options,
          metadata: {
            title: 'Family Recipe Collection',
            author: 'Family',
            description: 'A treasured collection of family recipes'
          }
        })
      });

      setOrderProgress(80);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate PDF on server');
      }

      // Check response headers
      console.log('Response headers:');
      response.headers.forEach((value, key) => {
        console.log(`${key}: ${value}`);
      });

      // Download the PDF
      const blob = await response.blob();

      // Debug the blob
      console.log('PDF blob size:', blob.size, 'bytes');
      console.log('PDF blob type:', blob.type);

      // Verify the blob is not empty
      if (blob.size === 0) {
        throw new Error('Received empty PDF blob from server');
      }

      // Check PDF header
      const arrayBuffer = await blob.slice(0, 10).arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      const header = String.fromCharCode(...uint8Array.slice(0, 4));
      console.log('PDF header:', header);
      console.log('First 10 bytes:', Array.from(uint8Array));

      if (header !== '%PDF') {
        throw new Error(`Invalid PDF header: ${header}. File may be corrupted.`);
      }

      // Create object URL for download
      const url = URL.createObjectURL(blob);

      // Also open in new tab for immediate viewing
      const newWindow = window.open(url, '_blank');
      if (!newWindow) {
        console.warn('Popup blocked, falling back to download only');
      }

      // Download the file
      const link = document.createElement('a');
      link.href = url;
      link.download = `family-recipe-collection-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL after a delay to allow download to complete
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);

      setOrderProgress(100);

      toast({
        title: "PDF Generated Successfully",
        description: `PDF (${Math.round(blob.size / 1024)} KB) has been downloaded and opened in a new tab. Check your Downloads folder if the new tab doesn't open.`,
      });

    } catch (error) {
      console.error('Error generating server PDF:', error);
      toast({
        title: "PDF Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingOrder(false);
      setOrderProgress(0);
    }
  };



  const nextPage = () => {
    // Check if we're on a recipe page and if it has pagination
    const current = getCurrentRecipe();
    if (current && current.isPaginated) {
      const { recipe, subPageIndex, totalSubPages } = current;
      // If we're not on the last sub-page, go to the next sub-page
      if (subPageIndex < totalSubPages - 1) {
        setRecipeSubPage({
          ...recipeSubPage,
          [recipe.id]: subPageIndex + 1
        });
        return;
      }
    }

    // Otherwise, go to the next main page
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);

      // Reset sub-page for the next recipe if it's paginated
      const nextRecipeIndex = getRecipeIndex() + 1;
      if (nextRecipeIndex >= 0 && nextRecipeIndex < recipes.length) {
        const nextRecipe = recipes[nextRecipeIndex];
        if (nextRecipe && paginatedRecipes[nextRecipe.id]) {
          setRecipeSubPage({
            ...recipeSubPage,
            [nextRecipe.id]: 0
          });
        }
      }
    }
  };

  const prevPage = () => {
    // Check if we're on a recipe page and if it has pagination
    const current = getCurrentRecipe();
    if (current && current.isPaginated) {
      const { recipe, subPageIndex } = current;
      // If we're not on the first sub-page, go to the previous sub-page
      if (subPageIndex > 0) {
        setRecipeSubPage({
          ...recipeSubPage,
          [recipe.id]: subPageIndex - 1
        });
        return;
      }
    }

    // Otherwise, go to the previous main page
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);

      // If going to a previous recipe that's paginated, set to its last sub-page
      const prevRecipeIndex = getRecipeIndex() - 1;
      if (prevRecipeIndex >= 0 && prevRecipeIndex < recipes.length) {
        const prevRecipe = recipes[prevRecipeIndex];
        const prevPagination = prevRecipe && paginatedRecipes[prevRecipe.id];
        if (prevPagination) {
          setRecipeSubPage({
            ...recipeSubPage,
            [prevRecipe.id]: 0 // Start at the first page when navigating backward
          });
        }
      }
    }
  };

  // Split ingredients into two columns
  const splitIngredients = (ingredients: Ingredient[]) => {
    const midpoint = Math.ceil(ingredients.length / 2);
    return [
      ingredients.slice(0, midpoint),
      ingredients.slice(midpoint)
    ];
  };

  if (!open) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl p-0 h-[90vh]">
        <div className="preview-container book-preview-container">
          {/* Header with controls */}
          <div className="preview-header book-controls book-navigation">
            <div className="flex items-center gap-2">
              <Book className="h-5 w-5" />
              <span className="font-medium">Book Preview</span>
            </div>
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={() => setShowCustomizationPanel(!showCustomizationPanel)}
              >
                <Settings className="h-4 w-4" />
                {showCustomizationPanel ? 'Hide Customization' : 'Customize'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleTestS3}
                title="Check S3 bucket configuration and URL generation"
              >
                <Settings className="h-4 w-4" />
                Debug S3
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleTestImages}
                disabled={recipes.length === 0}
                title="Test if recipe images are accessible from S3"
              >
                <Eye className="h-4 w-4" />
                Test Images
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleTestHTML}
                disabled={recipes.length === 0}
                title="Test HTML generation (opens in new window for debugging)"
              >
                <Eye className="h-4 w-4" />
                Test HTML
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleDownloadPDF}
                disabled={isCreatingOrder || recipes.length === 0}
                title="Generate high-quality PDF that matches your preview exactly"
              >
                {isCreatingOrder ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    {orderProgress > 0 ? `${Math.round(orderProgress)}%` : 'Processing...'}
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Download PDF
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={() => setShowAddressForm(true)}
                disabled={isCreatingOrder || recipes.length === 0}
              >
                <Book className="h-4 w-4" />
                Order Print Book
              </Button>

              {/* Blurb API Testing Buttons */}
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleTestBlurbAPI}
                title="Test Blurb API configuration"
              >
                <Package className="h-4 w-4" />
                Test Blurb API
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleTestAddressValidation}
                title="Test address validation with sample address"
              >
                <Truck className="h-4 w-4" />
                Test Address
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleCalculateShipping}
                disabled={recipes.length === 0}
                title="Calculate shipping costs for current book"
              >
                <CreditCard className="h-4 w-4" />
                Test Shipping
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={handleGetOrders}
                title="Get user's print orders"
              >
                <Package className="h-4 w-4" />
                Get Orders
              </Button>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={prevPage}
                  disabled={(() => {
                    // Check if we're on a recipe page and if it has pagination
                    const current = getCurrentRecipe();
                    if (current && current.isPaginated) {
                      const { subPageIndex } = current;
                      // If we're not on the first sub-page, we can go to the previous sub-page
                      if (subPageIndex > 0) {
                        return false;
                      }
                    }
                    // Otherwise, check if we're on the first main page
                    return currentPage === 0;
                  })()}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="flex items-center gap-2">
                  {isShowingCover ? (
                    <span className="text-sm bg-primary/10 px-2 py-0.5 rounded-sm">Cover</span>
                  ) : isShowingDedication ? (
                    <span className="text-sm bg-primary/10 px-2 py-0.5 rounded-sm">Dedication</span>
                  ) : isShowingQuote ? (
                    <span className="text-sm bg-primary/10 px-2 py-0.5 rounded-sm">Quote {getQuoteIndex() + 1}</span>
                  ) : (() => {
                    // Get current recipe and pagination info
                    const current = getCurrentRecipe();
                    if (!current) return <span className="text-sm">Page {getRecipeIndex() + 1} of {recipes.length}</span>;

                    const { subPageIndex, isPaginated, totalSubPages } = current;

                    if (isPaginated) {
                      return (
                        <span className="text-sm">
                          Recipe {getRecipeIndex() + 1} of {recipes.length}
                          <span className="ml-1 text-xs text-muted-foreground">
                            (Page {subPageIndex + 1} of {totalSubPages})
                          </span>
                        </span>
                      );
                    }

                    return <span className="text-sm">Recipe {getRecipeIndex() + 1} of {recipes.length}</span>;
                  })()}
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={nextPage}
                  disabled={(() => {
                    // Check if we're on a recipe page and if it has pagination
                    const current = getCurrentRecipe();
                    if (current && current.isPaginated) {
                      const { subPageIndex, totalSubPages } = current;
                      // If we're not on the last sub-page, we can go to the next sub-page
                      if (subPageIndex < totalSubPages - 1) {
                        return false;
                      }
                    }
                    // Otherwise, check if we're on the last main page
                    return currentPage >= totalPages - 1;
                  })()}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onOpenChange(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex flex-1 overflow-hidden">
            {/* Customization panel */}
            {showCustomizationPanel && (
              <div className="w-72 min-w-[18rem] sm:w-64 lg:w-80 max-w-xs border-r-2 border-primary/20 p-3 overflow-y-auto bg-background shadow-lg">
                <h3 className="text-base font-semibold mb-2">Book Customization</h3>
                <BookCustomization
                  options={options}
                  onChange={handleCustomizationChange}
                />
              </div>
            )}

            {/* Book content */}
            <div className={`preview-content ${showCustomizationPanel ? 'flex-1' : 'w-full'}`}>
              <div className="book-page">
                {isShowingCover ? (
                  // Show cover page
                  renderCoverPage(options)
                ) : isShowingDedication ? (
                  // Show dedication page
                  renderDedicationPage(options)
                ) : isShowingQuote ? (
                  // Show quote page
                  renderQuotePage(options.familyQuotes?.[getQuoteIndex()] ?? '', options)
                ) : (() => {
                  // Show recipe page
                  if (recipes.length === 0 || getRecipeIndex() < 0) return null;

                  const current = getCurrentRecipe();
                  if (!current) return null;

                  const { recipe, subPageIndex, isPaginated } = current;

                  // If recipe is paginated, render the appropriate sub-page
                  if (isPaginated) {
                    const paginationInfo = paginatedRecipes[recipe.id];
                    if (!paginationInfo) return null;

                    // Get the content for this sub-page
                    const pageContent = paginationInfo.pages[subPageIndex] || [];

                    // Render paginated content
                    return (
                      <div>
                        {/* Render each content section based on its type */}
                        {pageContent.map((section, idx) => {
                          switch (section.type) {
                            case 'header':
                              return (
                                <div key={`header-${idx}`}>
                                  {/* Recipe Category Header */}
                                  {subPageIndex === 0 && renderChapterTitle(section.content.category, getRecipeIndex(), options)}

                                  {/* Recipe Title - only on first page */}
                                  {subPageIndex === 0 && (
                                    <h2 className="text-xl font-bold mb-6 uppercase">{section.content.title}</h2>
                                  )}

                                  {/* Continuation header for sub-pages after the first */}
                                  {subPageIndex > 0 && (
                                    <div className="mb-4">
                                      <h2 className="text-xl font-bold uppercase">{section.content.title} (Continued)</h2>
                                    </div>
                                  )}
                                </div>
                              );

                            case 'image':
                              return (
                                <div key={`image-${idx}`} className="flex flex-col md:flex-row gap-6 mb-6">
                                  {/* Recipe Image */}
                                  {section.content.image ? (
                                    <div className="w-full md:w-1/2 recipe-image">
                                      <RecipeImages
                                        images={[section.content.image]}
                                        recipeTitle={recipe.title}
                                        className="w-full h-auto object-cover"
                                      />
                                    </div>
                                  ) : (
                                    <div className="w-full md:w-1/2 bg-gray-100 flex items-center justify-center h-[300px] recipe-image">
                                      <span className="text-gray-400">No image available</span>
                                    </div>
                                  )}

                                  {/* Recipe Description */}
                                  <div className="w-full md:w-1/2 flex flex-col justify-between">
                                    <p className="text-sm mb-4">{section.content.description}</p>

                                    {/* Recipe Details */}
                                    <div className="mt-auto recipe-details">
                                      <p><strong>Serves:</strong> {section.content.details.servings}</p>
                                      <p><strong>Prep Time:</strong> {section.content.details.prepTime} minutes</p>
                                      <p><strong>Cook Time:</strong> {section.content.details.cookTime}-30 minutes</p>
                                    </div>
                                  </div>
                                </div>
                              );

                            case 'ingredients':
                              return (
                                <div key={`ingredients-${idx}`}>
                                  <h3 className="text-xl font-bold mb-4">Ingredients:</h3>
                                  <div className="flex flex-col md:flex-row gap-8 mb-8">
                                    {(() => {
                                      const [leftIngredients, rightIngredients] = splitIngredients(section.content);
                                      return (
                                        <>
                                          <ul className="list-disc pl-5 space-y-2 w-full md:w-1/2">
                                            {leftIngredients.map((ingredient, idx) => (
                                              <li key={idx}>
                                                {ingredient.amount} {ingredient.unit} {ingredient.name}
                                              </li>
                                            ))}
                                          </ul>
                                          <ul className="list-disc pl-5 space-y-2 w-full md:w-1/2">
                                            {rightIngredients.map((ingredient, idx) => (
                                              <li key={idx}>
                                                {ingredient.amount} {ingredient.unit} {ingredient.name}
                                              </li>
                                            ))}
                                          </ul>
                                        </>
                                      );
                                    })()}
                                  </div>
                                </div>
                              );

                            case 'instructions':
                              return (
                                <div key={`instructions-${idx}`}>
                                  <h3 className="text-xl font-bold mb-4">Instructions:</h3>
                                  <div className="space-y-8">
                                    {section.content.map((instruction: string, idx: number) => (
                                      <div key={idx} className="flex gap-6">
                                        <div className="step-number">
                                          {idx + 1}
                                        </div>
                                        <div className="flex-1">
                                          <h4 className="font-bold mb-2">
                                            {getStepTitle(idx, instruction)}
                                          </h4>
                                          <ul className="list-disc pl-5 space-y-2">
                                            {instruction.split('\n').map((step: string, stepIdx: number) => (
                                              <li key={stepIdx}>{step}</li>
                                            ))}
                                          </ul>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              );

                            case 'footer':
                              return (
                                <p key={`footer-${idx}`} className="mt-8 text-center">Enjoy your healthy and delicious meal!</p>
                              );

                            case 'additional-images':
                              // Only render additional images if we have them
                              return section.content && section.content.length > 0 ? (
                                <div key={`additional-images-${idx}`} className="mt-8 grid grid-cols-2 gap-4">
                                  {section.content.slice(0, 2).map((image: string, imgIdx: number) => (
                                    <div key={imgIdx} className="recipe-image h-48">
                                      <RecipeImages
                                        images={[image]}
                                        recipeTitle={`${recipe.title} additional image ${imgIdx + 1}`}
                                        className="w-full h-full object-cover"
                                      />
                                    </div>
                                  ))}
                                </div>
                              ) : null;

                            default:
                              return null;
                          }
                        })}
                      </div>
                    );
                  }

                  // If not paginated, render the normal recipe page
                  return renderRecipePage(recipe, splitIngredients, getRecipeIndex(), options);
                })()}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>

      {/* Shipping Address Form */}
      <ShippingAddressForm
        open={showAddressForm}
        onOpenChange={setShowAddressForm}
        onSubmit={handleCreatePrintOrder}
        isLoading={isCreatingOrder}
        loadingProgress={orderProgress}
      />

      {/* Order Tracking Dialog */}
      <Dialog open={showOrderTracking} onOpenChange={setShowOrderTracking}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <div className="space-y-4">
            <h2 className="text-2xl font-bold">Order Tracking</h2>

            {userOrders.length === 0 ? (
              <p className="text-gray-500">No orders found.</p>
            ) : (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Your Orders ({userOrders.length})</h3>

                {userOrders.map((order: any) => (
                  <div key={order.id} className="border rounded-lg p-4 space-y-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-semibold">Order #{order.id}</h4>
                        <p className="text-sm text-gray-600">Blurb Order: {order.blurbOrderId}</p>
                        <p className="text-sm text-gray-600">Status: <span className="font-medium">{order.status}</span></p>
                        <p className="text-sm text-gray-600">Created: {new Date(order.createdAt).toLocaleDateString()}</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleGetTracking(order.id)}
                        className="flex items-center gap-1"
                      >
                        <Package className="h-4 w-4" />
                        Track Order
                      </Button>
                    </div>

                    {selectedOrderTracking === order.id && trackingDetails && (
                      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                        <h5 className="font-semibold mb-3">Tracking Details</h5>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <p><strong>Status:</strong> {trackingDetails.status}</p>
                            <p><strong>Order Date:</strong> {new Date(trackingDetails.orderDate).toLocaleDateString()}</p>
                            {trackingDetails.trackingNumber && (
                              <p><strong>Tracking Number:</strong> {trackingDetails.trackingNumber}</p>
                            )}
                            {trackingDetails.carrier && (
                              <p><strong>Carrier:</strong> {trackingDetails.carrier}</p>
                            )}
                          </div>
                          <div>
                            {trackingDetails.estimatedDelivery && (
                              <p><strong>Estimated Delivery:</strong> {new Date(trackingDetails.estimatedDelivery).toLocaleDateString()}</p>
                            )}
                            <p><strong>Shipping To:</strong></p>
                            <div className="text-sm text-gray-600">
                              <p>{trackingDetails.shippingAddress.name}</p>
                              <p>{trackingDetails.shippingAddress.street}</p>
                              <p>{trackingDetails.shippingAddress.city}, {trackingDetails.shippingAddress.state} {trackingDetails.shippingAddress.zipCode}</p>
                            </div>
                          </div>
                        </div>

                        {trackingDetails.trackingHistory && trackingDetails.trackingHistory.length > 0 && (
                          <div>
                            <h6 className="font-semibold mb-2">Tracking History</h6>
                            <div className="space-y-2">
                              {trackingDetails.trackingHistory.map((event: any, idx: number) => (
                                <div key={idx} className="flex gap-3 p-2 bg-white rounded border-l-4 border-blue-500">
                                  <div className="flex-shrink-0 w-20 text-sm text-gray-500">
                                    {new Date(event.date).toLocaleDateString()}
                                  </div>
                                  <div className="flex-1">
                                    <p className="font-medium">{event.status}</p>
                                    <p className="text-sm text-gray-600">{event.description}</p>
                                    {event.location && (
                                      <p className="text-xs text-gray-500">📍 {event.location}</p>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}