import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ImageUpload } from "@/components/ui/image-upload";
import { Loader2, <PERSON>an } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { API_URL } from "@/lib/constants";
import { RecipeForm } from "./recipe-form";

interface OcrRecipeUploadProps {
  projectId: number;
  onComplete?: (recipe: any) => void;
}

export function OcrRecipeUpload({ projectId, onComplete }: OcrRecipeUploadProps) {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [ocrLoading, setOcrLoading] = useState(false);
  const [ocrSuccess, setOcrSuccess] = useState(false);
  const [ocrD<PERSON>, setOcrData] = useState<any>(null);
  const [showEditForm, setShowEditForm] = useState(false);
  const { toast } = useToast();

  const resetForm = () => {
    setSelectedImages([]);
    setOcrData(null);
    setOcrSuccess(false);
    setShowEditForm(false);
  };

  const handleImagesUpload = (urls: string[]) => {
    setSelectedImages(urls);
  };

  const processOcr = async () => {
    if (selectedImages.length === 0) {
      toast({
        title: "Error",
        description: "Please upload an image of a recipe first",
        variant: "destructive",
      });
      return;
    }

    setOcrLoading(true);
    try {
      const response = await fetch(`${API_URL}/contributor/recipes/ocr`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          imageKey: selectedImages[0],
          projectId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || "Failed to process image");
      }

      const data = await response.json();
      setOcrData(data);
      setOcrSuccess(true);
      
      toast({
        title: "Success",
        description: "Recipe scanned and processed successfully!",
      });

      if (onComplete) {
        onComplete(data.recipe);
      }
    } catch (error) {
      toast({
        title: "OCR Processing Failed",
        description: error instanceof Error ? error.message : "Failed to process recipe image",
        variant: "destructive",
      });
    } finally {
      setOcrLoading(false);
    }
  };

  const handleEditRecipe = () => {
    setShowEditForm(true);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Scan className="h-5 w-5 mr-2" />
          Scan Recipe from Image
        </CardTitle>
        <CardDescription>
          Upload a photo of a handwritten or printed recipe and we'll automatically extract the text
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!ocrSuccess ? (
          <>
            <div className="mb-4">
              <ImageUpload 
                onUpload={handleImagesUpload} 
                maxFiles={1} 
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                initialFiles={selectedImages}
              />
            </div>
            <Button 
              onClick={processOcr} 
              disabled={selectedImages.length === 0 || ocrLoading}
              className="w-full"
            >
              {ocrLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Scan className="mr-2 h-4 w-4" />
                  Scan Recipe
                </>
              )}
            </Button>
          </>
        ) : (
          <>
            <div className="mb-4 space-y-2">
              <h3 className="font-medium">Extraction Results</h3>
              <div className="border rounded-md p-4 bg-muted/30">
                <p><strong>Title:</strong> {ocrData?.recipe?.title}</p>
                <p><strong>Description:</strong> {ocrData?.recipe?.description}</p>
                <p><strong>Ingredients:</strong> {ocrData?.recipe?.ingredients?.length} detected</p>
                <p><strong>Instructions:</strong> {ocrData?.recipe?.instructions?.length} steps detected</p>
              </div>
            </div>
            
            <div className="flex flex-col space-y-2">
              <Button 
                variant="outline" 
                onClick={handleEditRecipe}
                className="w-full"
              >
                Edit Recipe
              </Button>
              <Button 
                variant="secondary"
                onClick={resetForm}
                className="w-full"
              >
                Scan Another Recipe
              </Button>
            </div>
          </>
        )}

        {showEditForm && ocrData && (
          <div className="mt-8">
            <h3 className="font-semibold text-lg mb-4">Edit Recipe Details</h3>
            <RecipeForm
              initialData={{
                ...ocrData.recipe,
                projectId
              }}
              projects={[{ id: projectId, name: "Current Project", organizerId: 0 }]}
              onSubmit={async (data) => {
                try {
                  const response = await fetch(`${API_URL}/contributor/recipes/${ocrData.recipe.id}`, {
                    method: "PUT",
                    headers: {
                      "Content-Type": "application/json",
                      Authorization: `Bearer ${localStorage.getItem("token")}`,
                    },
                    body: JSON.stringify(data),
                  });
                  
                  if (!response.ok) {
                    throw new Error("Failed to update recipe");
                  }
                  
                  toast({
                    title: "Success",
                    description: "Recipe updated successfully",
                  });
                  
                  resetForm();
                  
                  if (onComplete) {
                    const updatedRecipe = await response.json();
                    onComplete(updatedRecipe);
                  }
                } catch (error) {
                  toast({
                    title: "Error",
                    description: error instanceof Error ? error.message : "Failed to update recipe",
                    variant: "destructive",
                  });
                }
              }}
            />
            <Button 
              variant="outline" 
              onClick={() => setShowEditForm(false)}
              className="mt-4"
            >
              Cancel
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 