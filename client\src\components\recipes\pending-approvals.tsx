import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { CheckCircle, XCircle } from "lucide-react";
import { API_URL } from '@/lib/constants';
import { useState } from "react";

interface Recipe {
  id: number;
  title: string;
  description: string;
  category: string;
  status: 'pending' | 'approved' | 'rejected';
  contributor: {
    id: number;
    name: string;
  };
  createdAt: string;
}

function PendingApprovals({ projectId }: { projectId: number }) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [processingRecipes, setProcessingRecipes] = useState<Set<number>>(new Set());

  const { data: pendingRecipes, isLoading } = useQuery<Recipe[]>({
    queryKey: ['pending-approvals', projectId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/organizer/projects/${projectId}/pending-recipes`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch pending recipes');
      const data = await response.json();
      return data;
    }
  });

  const updateStatusMutation = useMutation({
    mutationFn: async ({ recipeId, status }: { recipeId: number; status: 'approved' | 'rejected' }) => {
      setProcessingRecipes(prev => new Set(prev).add(recipeId));
      try {
        const response = await fetch(`${API_URL}/organizer/recipes/${recipeId}/status`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({ status })
        });
        
        if (!response.ok) {
          throw new Error('Failed to update recipe status');
        }
        
        return response.json();
      } finally {
        setProcessingRecipes(prev => {
          const newSet = new Set(prev);
          newSet.delete(recipeId);
          return newSet;
        });
      }
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['pending-approvals', projectId] });
      toast({
        title: "Success",
        description: variables.status === 'rejected' ? "Recipe rejected and deleted" : "Recipe approved successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update recipe status",
        variant: "destructive",
      });
    }
  });

  if (isLoading) {
    return <div className="text-center py-4">Loading pending approvals...</div>;
  }

  if (!pendingRecipes?.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pending Approvals</CardTitle>
          <CardDescription>Review submitted recipes</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-4">
            No recipes pending approval.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pending Approvals</CardTitle>
        <CardDescription>Review and approve submitted recipes</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pendingRecipes.map((recipe) => {
            const isProcessing = processingRecipes.has(recipe.id);
            return (
              <Card key={recipe.id} className="border border-muted">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium text-lg mb-1">{recipe.title}</h4>
                      <p className="text-sm text-muted-foreground mb-2">{recipe.description}</p>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-muted-foreground">
                          By: {recipe.contributor.name}
                        </span>
                        <span className="text-muted-foreground">
                          Submitted: {new Date(recipe.createdAt).toLocaleDateString()}
                        </span>
                        <Badge variant="outline">Pending Review</Badge>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => updateStatusMutation.mutate({ 
                          recipeId: recipe.id, 
                          status: 'approved' 
                        })}
                        className="bg-emerald-500 text-white hover:bg-emerald-600"
                        disabled={isProcessing}
                      >
                        {isProcessing ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </span>
                        ) : (
                          <>
                            <CheckCircle className="w-4 h-4 mr-2" />
                            Approve
                          </>
                        )}
                      </Button>
                      <Button
                        onClick={() => updateStatusMutation.mutate({ 
                          recipeId: recipe.id, 
                          status: 'rejected' 
                        })}
                        variant="destructive"
                        disabled={isProcessing}
                      >
                        {isProcessing ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                          </span>
                        ) : (
                          <>
                            <XCircle className="w-4 h-4 mr-2" />
                            Reject
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}

export default PendingApprovals;
