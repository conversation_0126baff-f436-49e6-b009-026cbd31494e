import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { API_URL } from '@/lib/constants';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  contributor: {
    id: number;
    name: string;
  };
  createdAt: string;
}

interface PendingRecipesProps {
  projectId: number;
}

export function PendingRecipes({ projectId }: PendingRecipesProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch pending recipes
  const { data: recipes, isLoading } = useQuery({
    queryKey: ['pending-recipes', projectId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/organizer/projects/${projectId}/pending-recipes`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch pending recipes');
      return response.json();
    }
  });

  // Handle recipe approval
  const approvalMutation = useMutation({
    mutationFn: async ({ recipeId, status }: { recipeId: number; status: 'approved' | 'rejected' }) => {
      const response = await fetch(`${API_URL}/organizer/recipes/${recipeId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ status })
      });
      if (!response.ok) throw new Error('Failed to update recipe status');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pending-recipes', projectId] });
      toast({
        title: "Success",
        description: "Recipe status updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update recipe status",
        variant: "destructive",
      });
    }
  });

  const handleApproval = (recipeId: number, status: 'approved' | 'rejected') => {
    approvalMutation.mutate({ recipeId, status });
  };

  if (isLoading) {
    return <div className="text-center py-4">Loading pending recipes...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pending Recipe Submissions</CardTitle>
        <CardDescription>Review and approve recipe submissions from contributors</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recipes?.length === 0 ? (
            <p className="text-muted-foreground">No pending recipe submissions.</p>
          ) : (
            recipes?.map((recipe: Recipe) => (
              <Card key={recipe.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold">{recipe.title}</h3>
                      <p className="text-sm text-muted-foreground">{recipe.description}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-sm">By {recipe.contributor.name}</span>
                        <Badge variant="outline">Pending</Badge>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        variant="default" 
                        onClick={() => handleApproval(recipe.id, 'approved')}
                        disabled={approvalMutation.isPending}
                      >
                        Approve
                      </Button>
                      <Button 
                        variant="destructive" 
                        onClick={() => handleApproval(recipe.id, 'rejected')}
                        disabled={approvalMutation.isPending}
                      >
                        Reject
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
