import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, ChefHat, Tag } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { API_URL } from '@/lib/constants';

const API_BASE = API_URL;

interface Recipe {
  id: number;
  title: string;
  description: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
  }>;
  instructions: string[];
  prepTime: number;
  cookTime: number;
  servings: number;
  difficulty: string;
  tags: string[];
  status: string;
  createdAt: string;
  contributor: {
    id: number;
    name: string;
    email: string;
  };
}

interface ProjectRecipesProps {
  projectId: number;
}

export function ProjectRecipes({ projectId }: ProjectRecipesProps) {
  const { toast } = useToast();

  const { data: recipes, isLoading, error } = useQuery<{ recipes: Recipe[] }>({
    queryKey: ['project-recipes', projectId],
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated.');
      }

      const response = await fetch(`${API_BASE}/organizer/projects/${projectId}/recipes`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch recipes');
      }

      return response.json();
    }
  });

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p className="mt-2 text-muted-foreground">Loading recipes...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        Failed to load recipes. Please try again later.
      </div>
    );
  }

  if (!recipes?.recipes || recipes.recipes.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No recipes have been added to this project yet.
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {recipes.recipes.map((recipe) => (
        <Card key={recipe.id} className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{recipe.title}</span>
              <Badge variant="outline" className="capitalize">
                {recipe.difficulty}
              </Badge>
            </CardTitle>
            <CardDescription>
              By {recipe.contributor.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              {recipe.description}
            </p>
            <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                <span>{recipe.prepTime + recipe.cookTime} mins</span>
              </div>
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-1" />
                <span>{recipe.servings} servings</span>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {recipe.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
} 