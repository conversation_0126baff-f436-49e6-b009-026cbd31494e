import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import { Recipe, Ingredient } from '@/types';
import { BookCustomizationOptions, themes, fonts, coverDesigns } from './book-customization';

interface RecipeBookPDFProps {
  recipes: Recipe[];
  options: BookCustomizationOptions;
  projectName?: string;
}

// Create styles based on customization options using built-in fonts
const createStyles = (options: BookCustomizationOptions) => {
  const selectedTheme = themes.find(t => t.id === options.theme) || themes[0];
  const selectedCover = coverDesigns.find(c => c.id === options.cover) || coverDesigns[0];

  return StyleSheet.create({
    page: {
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: 40,
      fontFamily: 'Helvetica',
      fontSize: 11,
      lineHeight: 1.4,
      color: selectedTheme.colors.text,
    },
    coverPage: {
      flexDirection: 'column',
      backgroundColor: selectedCover.backgroundColor || '#f8f9fa',
      padding: 0,
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100%',
    },
    coverContent: {
      textAlign: 'center',
      padding: 60,
    },
    coverTitle: {
      fontSize: 36,
      marginBottom: 20,
      color: selectedCover.textColor || selectedTheme.colors.heading,
      fontFamily: 'Helvetica-Bold',
    },
    coverSubtitle: {
      fontSize: 18,
      color: selectedCover.textColor || selectedTheme.colors.text,
      fontFamily: 'Helvetica',
    },
    dedicationPage: {
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      minHeight: '100%',
    },
    dedicationTitle: {
      fontSize: 24,
      marginBottom: 40,
      color: selectedTheme.colors.heading,
      fontFamily: 'Helvetica-Bold',
    },
    dedicationText: {
      fontSize: 14,
      maxWidth: 400,
      lineHeight: 1.6,
      color: selectedTheme.colors.text,
      fontFamily: 'Helvetica-Oblique',
    },
    quotePage: {
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      minHeight: '100%',
    },
    quoteText: {
      fontSize: 16,
      maxWidth: 400,
      lineHeight: 1.6,
      borderTop: `2px solid ${selectedTheme.colors.accent}`,
      borderBottom: `2px solid ${selectedTheme.colors.accent}`,
      paddingVertical: 30,
      color: selectedTheme.colors.text,
      fontFamily: 'Helvetica-Oblique',
    },
    chapterTitle: {
      fontSize: 20,
      textAlign: 'center',
      marginBottom: 30,
      paddingVertical: 15,
      backgroundColor: selectedTheme.colors.accent,
      color: '#ffffff',
      fontFamily: 'Helvetica-Bold',
    },
    recipeTitle: {
      fontSize: 18,
      marginBottom: 20,
      color: selectedTheme.colors.heading,
      fontFamily: 'Helvetica-Bold',
    },
    recipeHeader: {
      flexDirection: 'row',
      marginBottom: 20,
      gap: 20,
    },
    recipeImageContainer: {
      width: '50%',
      height: 200,
      backgroundColor: '#f0f0f0',
      justifyContent: 'center',
      alignItems: 'center',
    },
    recipeImage: {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
    },
    recipeInfo: {
      width: '50%',
      paddingLeft: 20,
    },
    recipeDescription: {
      fontSize: 10,
      marginBottom: 15,
      lineHeight: 1.4,
    },
    recipeDetails: {
      marginTop: 'auto',
    },
    detailRow: {
      flexDirection: 'row',
      marginBottom: 5,
    },
    detailLabel: {
      width: 80,
      fontFamily: 'Helvetica-Bold',
    },
    sectionTitle: {
      fontSize: 14,
      marginTop: 20,
      marginBottom: 10,
      color: selectedTheme.colors.heading,
      fontFamily: 'Helvetica-Bold',
    },
    ingredientsContainer: {
      flexDirection: 'row',
      marginBottom: 20,
      gap: 20,
    },
    ingredientColumn: {
      width: '50%',
    },
    ingredientItem: {
      fontSize: 10,
      marginBottom: 4,
      paddingLeft: 10,
    },
    instructionsContainer: {
      marginBottom: 20,
    },
    instructionItem: {
      flexDirection: 'row',
      marginBottom: 15,
      gap: 15,
    },
    stepNumber: {
      width: 25,
      height: 25,
      borderRadius: 12.5,
      backgroundColor: selectedTheme.colors.accent,
      color: '#ffffff',
      fontSize: 10,
      textAlign: 'center',
      lineHeight: 2.5,
      fontFamily: 'Helvetica-Bold',
    },
    instructionContent: {
      flex: 1,
    },
    instructionTitle: {
      fontSize: 11,
      marginBottom: 5,
      color: selectedTheme.colors.heading,
      fontFamily: 'Helvetica-Bold',
    },
    instructionText: {
      fontSize: 10,
      lineHeight: 1.4,
    },
    footer: {
      textAlign: 'center',
      marginTop: 30,
      fontSize: 10,
      color: selectedTheme.colors.accent,
      fontFamily: 'Helvetica-Oblique',
    },
    noImagePlaceholder: {
      fontSize: 10,
      color: '#999',
      textAlign: 'center',
    },
  });
};

// Helper function to split ingredients into two columns
const splitIngredients = (ingredients: Ingredient[]): [Ingredient[], Ingredient[]] => {
  const midpoint = Math.ceil(ingredients.length / 2);
  return [ingredients.slice(0, midpoint), ingredients.slice(midpoint)];
};

// Helper function to get step title
const getStepTitle = (index: number, instruction: string): string => {
  const stepTitles = [
    'Prepare Ingredients', 'Start Cooking', 'Mix & Combine', 'Cook & Simmer',
    'Add Seasonings', 'Final Touches', 'Plate & Serve', 'Garnish & Enjoy'
  ];
  return stepTitles[index] || `Step ${index + 1}`;
};

// Cover Page Component
const CoverPage: React.FC<{ options: BookCustomizationOptions; projectName?: string; styles: any }> = ({ 
  options, projectName, styles 
}) => {
  const title = options.coverTitle || projectName || 'Family Cookbook';
  const subtitle = options.coverSubtitle || 'Treasured Recipes';

  return (
    <Page size="A4" style={styles.coverPage}>
      <View style={styles.coverContent}>
        <Text style={styles.coverTitle}>{title}</Text>
        <Text style={styles.coverSubtitle}>{subtitle}</Text>
      </View>
    </Page>
  );
};

// Dedication Page Component
const DedicationPage: React.FC<{ options: BookCustomizationOptions; styles: any }> = ({ options, styles }) => {
  if (!options.includeDedication || !options.dedication) return null;

  return (
    <Page size="A4" style={styles.page}>
      <View style={styles.dedicationPage}>
        <Text style={styles.dedicationTitle}>Dedication</Text>
        <Text style={styles.dedicationText}>{options.dedication}</Text>
      </View>
    </Page>
  );
};

// Quote Page Component
const QuotePage: React.FC<{ quote: string; styles: any }> = ({ quote, styles }) => {
  return (
    <Page size="A4" style={styles.page}>
      <View style={styles.quotePage}>
        <Text style={styles.quoteText}>"{quote}"</Text>
      </View>
    </Page>
  );
};

// Recipe Page Component
const RecipePage: React.FC<{ recipe: Recipe; styles: any }> = ({ recipe, styles }) => {
  const [leftIngredients, rightIngredients] = splitIngredients(recipe.ingredients);
  const recipeCategory = recipe.tags && recipe.tags.length > 0
    ? `MEALS WITH ${recipe.tags[0].toUpperCase()}`
    : "MEALS WITH CHICKEN";

  return (
    <Page size="A4" style={styles.page}>
      {/* Chapter Title */}
      <Text style={styles.chapterTitle}>{recipeCategory}</Text>
      
      {/* Recipe Title */}
      <Text style={styles.recipeTitle}>{recipe.title}</Text>
      
      {/* Recipe Header with Image and Info */}
      <View style={styles.recipeHeader}>
        <View style={styles.recipeImageContainer}>
          {recipe.images && recipe.images.length > 0 ? (
            <Image style={styles.recipeImage} src={recipe.images[0]} />
          ) : (
            <Text style={styles.noImagePlaceholder}>No image available</Text>
          )}
        </View>
        
        <View style={styles.recipeInfo}>
          <Text style={styles.recipeDescription}>{recipe.description}</Text>
          
          <View style={styles.recipeDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Serves:</Text>
              <Text>{recipe.servings || 2}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Prep Time:</Text>
              <Text>{recipe.prepTime || 15} minutes</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Cook Time:</Text>
              <Text>{recipe.cookTime || 25} minutes</Text>
            </View>
          </View>
        </View>
      </View>
      
      {/* Ingredients Section */}
      <Text style={styles.sectionTitle}>Ingredients:</Text>
      <View style={styles.ingredientsContainer}>
        <View style={styles.ingredientColumn}>
          {leftIngredients.map((ingredient, idx) => (
            <Text key={idx} style={styles.ingredientItem}>
              • {ingredient.amount} {ingredient.unit} {ingredient.name}
            </Text>
          ))}
        </View>
        <View style={styles.ingredientColumn}>
          {rightIngredients.map((ingredient, idx) => (
            <Text key={idx} style={styles.ingredientItem}>
              • {ingredient.amount} {ingredient.unit} {ingredient.name}
            </Text>
          ))}
        </View>
      </View>
      
      {/* Instructions Section */}
      <Text style={styles.sectionTitle}>Instructions:</Text>
      <View style={styles.instructionsContainer}>
        {recipe.instructions.slice(0, 6).map((instruction, idx) => (
          <View key={idx} style={styles.instructionItem}>
            <Text style={styles.stepNumber}>{idx + 1}</Text>
            <View style={styles.instructionContent}>
              <Text style={styles.instructionTitle}>
                {getStepTitle(idx, instruction)}
              </Text>
              <Text style={styles.instructionText}>{instruction}</Text>
            </View>
          </View>
        ))}
      </View>
      
      {/* Footer */}
      <Text style={styles.footer}>Enjoy your healthy and delicious meal!</Text>
    </Page>
  );
};

// Main Recipe Book PDF Document
export const RecipeBookPDFSimple: React.FC<RecipeBookPDFProps> = ({ recipes, options, projectName }) => {
  const styles = createStyles(options);

  return (
    <Document>
      {/* Cover Page */}
      <CoverPage options={options} projectName={projectName} styles={styles} />
      
      {/* Dedication Page */}
      {options.includeDedication && options.dedication && (
        <DedicationPage options={options} styles={styles} />
      )}
      
      {/* Quote Pages */}
      {options.includeQuotes && options.familyQuotes && options.familyQuotes.map((quote, index) => (
        <QuotePage key={index} quote={quote} styles={styles} />
      ))}
      
      {/* Recipe Pages */}
      {recipes.map((recipe, index) => (
        <RecipePage key={recipe.id || index} recipe={recipe} styles={styles} />
      ))}
    </Document>
  );
};
