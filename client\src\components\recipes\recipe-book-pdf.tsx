import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image, Font } from '@react-pdf/renderer';
import { Recipe, Ingredient } from '@/types';
import { BookCustomizationOptions, themes, fonts, coverDesigns } from './book-customization';

// Register fonts for React PDF with all variants
Font.register({
  family: 'Open Sans',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/s/opensans/v34/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4gaVc.woff2',
      fontWeight: 400,
      fontStyle: 'normal'
    },
    {
      src: 'https://fonts.gstatic.com/s/opensans/v34/memQYaGs126MiZpBA-UFUIcVXSCEkx2cmqvXlWq8tWZ0Pw86hd0Rk5hkWVAexQ.woff2',
      fontWeight: 400,
      fontStyle: 'italic'
    },
    {
      src: 'https://fonts.gstatic.com/s/opensans/v34/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4kaVc.woff2',
      fontWeight: 600,
      fontStyle: 'normal'
    },
    {
      src: 'https://fonts.gstatic.com/s/opensans/v34/memSYaGs126MiZpBA-UvWbX2vVnXBbObj2OVZyOOSr4dVJWUgsjZ0B4jaVc.woff2',
      fontWeight: 700,
      fontStyle: 'normal'
    }
  ]
});

Font.register({
  family: 'Playfair Display',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qO0isEw.woff2',
      fontWeight: 400,
      fontStyle: 'normal'
    },
    {
      src: 'https://fonts.gstatic.com/s/playfairdisplay/v30/nuFRD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDYbtXK-F2qO0q7.woff2',
      fontWeight: 400,
      fontStyle: 'italic'
    },
    {
      src: 'https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtXK-F2qC0isEw.woff2',
      fontWeight: 700,
      fontStyle: 'normal'
    }
  ]
});

Font.register({
  family: 'Merriweather',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/s/merriweather/v30/u-440qyriQwlOrhSvowK_l5-fCZMdeX3rsHo.woff2',
      fontWeight: 400,
      fontStyle: 'normal'
    },
    {
      src: 'https://fonts.gstatic.com/s/merriweather/v30/u-4n0qyriQwlOrhSvowK_l5-eR7lXcf_hP3h.woff2',
      fontWeight: 400,
      fontStyle: 'italic'
    },
    {
      src: 'https://fonts.gstatic.com/s/merriweather/v30/u-4i0qyriQwlOrhSvowK_l5-eSZJdeP3r-DJ.woff2',
      fontWeight: 700,
      fontStyle: 'normal'
    }
  ]
});

interface RecipeBookPDFProps {
  recipes: Recipe[];
  options: BookCustomizationOptions;
  projectName?: string;
}

// Create styles based on customization options
const createStyles = (options: BookCustomizationOptions) => {
  const selectedTheme = themes.find(t => t.id === options.theme) || themes[0];
  const selectedFont = fonts.find(f => f.id === options.font) || fonts[0];
  const selectedCover = coverDesigns.find(c => c.id === options.cover) || coverDesigns[0];

  // Map font names to registered fonts with fallbacks
  const getFontFamily = (fontName: string) => {
    try {
      if (fontName.includes('Playfair')) return 'Playfair Display';
      if (fontName.includes('Merriweather')) return 'Merriweather';
      return 'Open Sans';
    } catch (error) {
      console.warn('Font loading error, using fallback:', error);
      return 'Helvetica'; // Built-in fallback font
    }
  };

  return StyleSheet.create({
    page: {
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: 40,
      fontFamily: getFontFamily(selectedFont.bodyFont),
      fontSize: 11,
      lineHeight: 1.4,
      color: selectedTheme.colors.text,
    },
    coverPage: {
      flexDirection: 'column',
      backgroundColor: selectedCover.backgroundColor || '#f8f9fa',
      padding: 0,
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100%',
    },
    coverContent: {
      textAlign: 'center',
      padding: 60,
    },
    coverTitle: {
      fontSize: 36,
      marginBottom: 20,
      color: selectedCover.textColor || selectedTheme.colors.heading,
      fontFamily: getFontFamily(selectedFont.headingFont),
    },
    coverSubtitle: {
      fontSize: 18,
      color: selectedCover.textColor || selectedTheme.colors.text,
      fontFamily: getFontFamily(selectedFont.headingFont),
    },
    dedicationPage: {
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      minHeight: '100%',
    },
    dedicationTitle: {
      fontSize: 24,
      marginBottom: 40,
      color: selectedTheme.colors.heading,
      fontFamily: getFontFamily(selectedFont.headingFont),
    },
    dedicationText: {
      fontSize: 14,
      maxWidth: 400,
      lineHeight: 1.6,
      color: selectedTheme.colors.text,
    },
    quotePage: {
      justifyContent: 'center',
      alignItems: 'center',
      textAlign: 'center',
      minHeight: '100%',
    },
    quoteText: {
      fontSize: 16,
      maxWidth: 400,
      lineHeight: 1.6,
      borderTop: `2px solid ${selectedTheme.colors.accent}`,
      borderBottom: `2px solid ${selectedTheme.colors.accent}`,
      paddingVertical: 30,
      color: selectedTheme.colors.text,
    },
    chapterTitle: {
      fontSize: 20,
      textAlign: 'center',
      marginBottom: 30,
      paddingVertical: 15,
      backgroundColor: selectedTheme.colors.accent,
      color: '#ffffff',
      fontFamily: getFontFamily(selectedFont.headingFont),
    },
    recipeTitle: {
      fontSize: 18,
      marginBottom: 20,
      textTransform: 'uppercase',
      color: selectedTheme.colors.heading,
      fontFamily: getFontFamily(selectedFont.headingFont),
    },
    recipeHeader: {
      flexDirection: 'row',
      marginBottom: 20,
      gap: 20,
    },
    recipeImageContainer: {
      width: '50%',
      height: 200,
      backgroundColor: '#f0f0f0',
      justifyContent: 'center',
      alignItems: 'center',
    },
    recipeImage: {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
    },
    recipeInfo: {
      width: '50%',
      paddingLeft: 20,
    },
    recipeDescription: {
      fontSize: 10,
      marginBottom: 15,
      lineHeight: 1.4,
    },
    recipeDetails: {
      marginTop: 'auto',
    },
    detailRow: {
      flexDirection: 'row',
      marginBottom: 5,
    },
    detailLabel: {
      width: 80,
    },
    sectionTitle: {
      fontSize: 14,
      marginTop: 20,
      marginBottom: 10,
      color: selectedTheme.colors.heading,
      fontFamily: getFontFamily(selectedFont.headingFont),
    },
    ingredientsContainer: {
      flexDirection: 'row',
      marginBottom: 20,
      gap: 20,
    },
    ingredientColumn: {
      width: '50%',
    },
    ingredientItem: {
      fontSize: 10,
      marginBottom: 4,
      paddingLeft: 10,
    },
    instructionsContainer: {
      marginBottom: 20,
    },
    instructionItem: {
      flexDirection: 'row',
      marginBottom: 15,
      gap: 15,
    },
    stepNumber: {
      width: 25,
      height: 25,
      borderRadius: 12.5,
      backgroundColor: selectedTheme.colors.accent,
      color: '#ffffff',
      fontSize: 10,
      textAlign: 'center',
      lineHeight: 2.5,
    },
    instructionContent: {
      flex: 1,
    },
    instructionTitle: {
      fontSize: 11,
      marginBottom: 5,
      color: selectedTheme.colors.heading,
    },
    instructionText: {
      fontSize: 10,
      lineHeight: 1.4,
    },
    footer: {
      textAlign: 'center',
      marginTop: 30,
      fontSize: 10,
      color: selectedTheme.colors.accent,
    },
    noImagePlaceholder: {
      fontSize: 10,
      color: '#999',
      textAlign: 'center',
    },
  });
};

// Helper function to split ingredients into two columns
const splitIngredients = (ingredients: Ingredient[]): [Ingredient[], Ingredient[]] => {
  const midpoint = Math.ceil(ingredients.length / 2);
  return [ingredients.slice(0, midpoint), ingredients.slice(midpoint)];
};

// Helper function to get step title
const getStepTitle = (index: number, instruction: string): string => {
  const stepTitles = [
    'Prepare Ingredients', 'Start Cooking', 'Mix & Combine', 'Cook & Simmer',
    'Add Seasonings', 'Final Touches', 'Plate & Serve', 'Garnish & Enjoy'
  ];
  return stepTitles[index] || `Step ${index + 1}`;
};

// Cover Page Component
const CoverPage: React.FC<{ options: BookCustomizationOptions; projectName?: string; styles: any }> = ({
  options, projectName, styles
}) => {
  const title = options.coverTitle || projectName || 'Family Cookbook';
  const subtitle = options.coverSubtitle || 'Treasured Recipes';

  return (
    <Page size="A4" style={styles.coverPage}>
      <View style={styles.coverContent}>
        <Text style={styles.coverTitle}>{title}</Text>
        <Text style={styles.coverSubtitle}>{subtitle}</Text>
      </View>
    </Page>
  );
};

// Dedication Page Component
const DedicationPage: React.FC<{ options: BookCustomizationOptions; styles: any }> = ({ options, styles }) => {
  if (!options.includeDedication || !options.dedication) return null;

  return (
    <Page size="A4" style={styles.page}>
      <View style={styles.dedicationPage}>
        <Text style={styles.dedicationTitle}>Dedication</Text>
        <Text style={styles.dedicationText}>{options.dedication}</Text>
      </View>
    </Page>
  );
};

// Quote Page Component
const QuotePage: React.FC<{ quote: string; styles: any }> = ({ quote, styles }) => {
  return (
    <Page size="A4" style={styles.page}>
      <View style={styles.quotePage}>
        <Text style={styles.quoteText}>"{quote}"</Text>
      </View>
    </Page>
  );
};

// Helper function to determine if recipe needs multiple pages
const shouldSplitRecipe = (recipe: Recipe): boolean => {
  const ingredientCount = recipe.ingredients.length;
  const instructionCount = recipe.instructions.length;
  const hasLongInstructions = recipe.instructions.some(inst => inst.length > 100);

  return ingredientCount > 12 || instructionCount > 8 || hasLongInstructions;
};

// Recipe Page Component with smart page breaks
const RecipePage: React.FC<{ recipe: Recipe; styles: any }> = ({ recipe, styles }) => {
  const [leftIngredients, rightIngredients] = splitIngredients(recipe.ingredients);
  const recipeCategory = recipe.tags && recipe.tags.length > 0
    ? `MEALS WITH ${recipe.tags[0].toUpperCase()}`
    : "MEALS WITH CHICKEN";

  const needsMultiplePages = shouldSplitRecipe(recipe);

  if (needsMultiplePages) {
    // Split into multiple pages for long recipes
    const firstPageInstructions = recipe.instructions.slice(0, 4);
    const remainingInstructions = recipe.instructions.slice(4);

    return (
      <>
        {/* First Page */}
        <Page size="A4" style={styles.page}>
          {/* Chapter Title */}
          <Text style={styles.chapterTitle}>{recipeCategory}</Text>

          {/* Recipe Title */}
          <Text style={styles.recipeTitle}>{recipe.title}</Text>

          {/* Recipe Header with Image and Info */}
          <View style={styles.recipeHeader}>
            <View style={styles.recipeImageContainer}>
              {recipe.images && recipe.images.length > 0 ? (
                <Image style={styles.recipeImage} src={recipe.images[0]} />
              ) : (
                <Text style={styles.noImagePlaceholder}>No image available</Text>
              )}
            </View>

            <View style={styles.recipeInfo}>
              <Text style={styles.recipeDescription}>{recipe.description}</Text>

              <View style={styles.recipeDetails}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Serves:</Text>
                  <Text>{recipe.servings || 2}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Prep Time:</Text>
                  <Text>{recipe.prepTime || 15} minutes</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Cook Time:</Text>
                  <Text>{recipe.cookTime || 25} minutes</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Ingredients Section */}
          <Text style={styles.sectionTitle}>Ingredients:</Text>
          <View style={styles.ingredientsContainer}>
            <View style={styles.ingredientColumn}>
              {leftIngredients.map((ingredient, idx) => (
                <Text key={idx} style={styles.ingredientItem}>
                  • {ingredient.amount} {ingredient.unit} {ingredient.name}
                </Text>
              ))}
            </View>
            <View style={styles.ingredientColumn}>
              {rightIngredients.map((ingredient, idx) => (
                <Text key={idx} style={styles.ingredientItem}>
                  • {ingredient.amount} {ingredient.unit} {ingredient.name}
                </Text>
              ))}
            </View>
          </View>

          {/* First part of Instructions */}
          <Text style={styles.sectionTitle}>Instructions:</Text>
          <View style={styles.instructionsContainer}>
            {firstPageInstructions.map((instruction, idx) => (
              <View key={idx} style={styles.instructionItem}>
                <Text style={styles.stepNumber}>{idx + 1}</Text>
                <View style={styles.instructionContent}>
                  <Text style={styles.instructionTitle}>
                    {getStepTitle(idx, instruction)}
                  </Text>
                  <Text style={styles.instructionText}>{instruction}</Text>
                </View>
              </View>
            ))}
          </View>

          {remainingInstructions.length > 0 && (
            <Text style={styles.footer}>Continued on next page...</Text>
          )}
        </Page>

        {/* Additional pages for remaining instructions */}
        {remainingInstructions.length > 0 && (
          <Page size="A4" style={styles.page}>
            <Text style={styles.recipeTitle}>{recipe.title} (Continued)</Text>

            <Text style={styles.sectionTitle}>Instructions (Continued):</Text>
            <View style={styles.instructionsContainer}>
              {remainingInstructions.map((instruction, idx) => (
                <View key={idx + firstPageInstructions.length} style={styles.instructionItem}>
                  <Text style={styles.stepNumber}>{idx + firstPageInstructions.length + 1}</Text>
                  <View style={styles.instructionContent}>
                    <Text style={styles.instructionTitle}>
                      {getStepTitle(idx + firstPageInstructions.length, instruction)}
                    </Text>
                    <Text style={styles.instructionText}>{instruction}</Text>
                  </View>
                </View>
              ))}
            </View>

            {/* Additional Images */}
            {recipe.images && recipe.images.length > 1 && (
              <View style={{ flexDirection: 'row', marginTop: 20, gap: 10 }}>
                {recipe.images.slice(1, 3).map((image, idx) => (
                  <View key={idx} style={{ width: '48%', height: 120 }}>
                    <Image style={{ width: '100%', height: '100%', objectFit: 'cover' }} src={image} />
                  </View>
                ))}
              </View>
            )}

            <Text style={styles.footer}>Enjoy your healthy and delicious meal!</Text>
          </Page>
        )}
      </>
    );
  }

  // Single page for shorter recipes
  return (
    <Page size="A4" style={styles.page}>
      {/* Chapter Title */}
      <Text style={styles.chapterTitle}>{recipeCategory}</Text>

      {/* Recipe Title */}
      <Text style={styles.recipeTitle}>{recipe.title}</Text>

      {/* Recipe Header with Image and Info */}
      <View style={styles.recipeHeader}>
        <View style={styles.recipeImageContainer}>
          {recipe.images && recipe.images.length > 0 ? (
            <Image style={styles.recipeImage} src={recipe.images[0]} />
          ) : (
            <Text style={styles.noImagePlaceholder}>No image available</Text>
          )}
        </View>

        <View style={styles.recipeInfo}>
          <Text style={styles.recipeDescription}>{recipe.description}</Text>

          <View style={styles.recipeDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Serves:</Text>
              <Text>{recipe.servings || 2}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Prep Time:</Text>
              <Text>{recipe.prepTime || 15} minutes</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Cook Time:</Text>
              <Text>{recipe.cookTime || 25} minutes</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Ingredients Section */}
      <Text style={styles.sectionTitle}>Ingredients:</Text>
      <View style={styles.ingredientsContainer}>
        <View style={styles.ingredientColumn}>
          {leftIngredients.map((ingredient, idx) => (
            <Text key={idx} style={styles.ingredientItem}>
              • {ingredient.amount} {ingredient.unit} {ingredient.name}
            </Text>
          ))}
        </View>
        <View style={styles.ingredientColumn}>
          {rightIngredients.map((ingredient, idx) => (
            <Text key={idx} style={styles.ingredientItem}>
              • {ingredient.amount} {ingredient.unit} {ingredient.name}
            </Text>
          ))}
        </View>
      </View>

      {/* Instructions Section */}
      <Text style={styles.sectionTitle}>Instructions:</Text>
      <View style={styles.instructionsContainer}>
        {recipe.instructions.map((instruction, idx) => (
          <View key={idx} style={styles.instructionItem}>
            <Text style={styles.stepNumber}>{idx + 1}</Text>
            <View style={styles.instructionContent}>
              <Text style={styles.instructionTitle}>
                {getStepTitle(idx, instruction)}
              </Text>
              <Text style={styles.instructionText}>{instruction}</Text>
            </View>
          </View>
        ))}
      </View>

      {/* Footer */}
      <Text style={styles.footer}>Enjoy your healthy and delicious meal!</Text>
    </Page>
  );
};

// Main Recipe Book PDF Document
export const RecipeBookPDF: React.FC<RecipeBookPDFProps> = ({ recipes, options, projectName }) => {
  const styles = createStyles(options);

  return (
    <Document>
      {/* Cover Page */}
      <CoverPage options={options} projectName={projectName} styles={styles} />

      {/* Dedication Page */}
      {options.includeDedication && options.dedication && (
        <DedicationPage options={options} styles={styles} />
      )}

      {/* Quote Pages */}
      {options.includeQuotes && options.familyQuotes && options.familyQuotes.map((quote, index) => (
        <QuotePage key={index} quote={quote} styles={styles} />
      ))}

      {/* Recipe Pages */}
      {recipes.map((recipe, index) => (
        <RecipePage key={recipe.id || index} recipe={recipe} styles={styles} />
      ))}
    </Document>
  );
};
