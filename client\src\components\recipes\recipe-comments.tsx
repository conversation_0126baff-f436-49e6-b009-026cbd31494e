import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { Trash2, Edit, Send, ChevronLeft, ChevronRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { API_URL } from '@/lib/constants';

interface Comment {
  id: number;
  comment: string;
  createdAt: string;
  updatedAt: string;
  recipeId: number;
  user: {
    id: number;
    name: string;
    role: string;
  };
}

interface Recipe {
  id: number;
  project?: {
    id: number;
    organizerId: number;
  };
}

interface RecipeCommentsProps {
  recipeId: number;
  hideTitle?: boolean;
}

export function RecipeComments({ recipeId, hideTitle = false }: RecipeCommentsProps) {
  const [newComment, setNewComment] = useState('');
  const [editingCommentId, setEditingCommentId] = useState<number | null>(null);
  const [editingText, setEditingText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [commentsPerPage] = useState(5);
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Fetch recipe details to get project information
  const { data: recipeData } = useQuery<{ recipe: Recipe }>({
    queryKey: ['recipe-details', recipeId],
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/recipes/${recipeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch recipe details');
      }

      return response.json();
    },
    enabled: !!recipeId && !!user?.role
  });

  // Fetch recipe comments
  const { data: commentsData, isLoading, isError } = useQuery({
    queryKey: ['recipe-comments', recipeId],
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/comments/recipe/${recipeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch comments');
      }

      const data = await response.json();
      console.log('Comments data:', data);
      return data;
    }
  });

  // Add comment mutation
  const addCommentMutation = useMutation({
    mutationFn: async (comment: string) => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/comments/recipe/${recipeId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ comment })
      });

      if (!response.ok) {
        throw new Error('Failed to add comment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recipe-comments', recipeId] });
      setNewComment('');
      toast({
        title: 'Comment added',
        description: 'Your comment has been added successfully.'
      });
      // Move to the first page when a new comment is added
      setCurrentPage(1);
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to add comment: ${error.message}`,
        variant: 'destructive'
      });
    }
  });

  // Update comment mutation
  const updateCommentMutation = useMutation({
    mutationFn: async ({ commentId, comment }: { commentId: number; comment: string }) => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ comment })
      });

      if (!response.ok) {
        throw new Error('Failed to update comment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recipe-comments', recipeId] });
      setEditingCommentId(null);
      setEditingText('');
      toast({
        title: 'Comment updated',
        description: 'Your comment has been updated successfully.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update comment: ${error.message}`,
        variant: 'destructive'
      });
    }
  });

  // Delete comment mutation
  const deleteCommentMutation = useMutation({
    mutationFn: async (commentId: number) => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete comment');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recipe-comments', recipeId] });
      toast({
        title: 'Comment deleted',
        description: 'Your comment has been deleted successfully.'
      });
      // Adjust current page if necessary after deletion
      if (comments.length <= commentsPerPage && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to delete comment: ${error.message}`,
        variant: 'destructive'
      });
    }
  });

  const handleAddComment = () => {
    if (!newComment.trim()) return;
    addCommentMutation.mutate(newComment);
  };

  const handleUpdateComment = (commentId: number) => {
    if (!editingText.trim()) return;
    updateCommentMutation.mutate({ commentId, comment: editingText });
  };

  const handleDeleteComment = (commentId: number) => {
    if (confirm('Are you sure you want to delete this comment?')) {
      deleteCommentMutation.mutate(commentId);
    }
  };

  const startEditing = (comment: Comment) => {
    setEditingCommentId(comment.id);
    setEditingText(comment.comment);
  };

  const cancelEditing = () => {
    setEditingCommentId(null);
    setEditingText('');
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  if (isLoading) {
    return (
      <div className="py-4 text-center">
        <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary mx-auto"></div>
        <p className="mt-2 text-sm text-muted-foreground">Loading comments...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="py-4 text-center text-muted-foreground">
        Failed to load comments. Please try again later.
      </div>
    );
  }

  const comments = commentsData?.comments || [];
  const totalComments = comments.length;
  const totalPages = Math.ceil(totalComments / commentsPerPage);

  // Get current page comments
  const indexOfLastComment = currentPage * commentsPerPage;
  const indexOfFirstComment = indexOfLastComment - commentsPerPage;
  const currentComments = comments.slice(indexOfFirstComment, indexOfLastComment);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  const goToNextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages));
  const goToPrevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));

  return (
    <div className="space-y-6">
      {!hideTitle && (
        <h2 className="text-2xl font-serif font-bold text-[#2E4B7A]">Story & Comments</h2>
      )}

      {/* Add comment form */}
      <div className="flex items-start gap-4">
        <Avatar className="h-10 w-10 bg-muted">
          <AvatarFallback>{user?.name ? getInitials(user.name) : 'U'}</AvatarFallback>
        </Avatar>
        <div className="flex-1 space-y-2">
          <Textarea
            placeholder="Share your story or add a comment about this recipe..."
            className="min-h-[80px] w-full"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
          />
          <Button
            className="flex items-center gap-2"
            onClick={handleAddComment}
            disabled={!newComment.trim() || addCommentMutation.isPending}
          >
            <Send className="h-4 w-4" />
            {addCommentMutation.isPending ? 'Posting...' : 'Post Comment'}
          </Button>
        </div>
      </div>

      {/* Comments list with pagination */}
      {totalComments === 0 ? (
        <p className="text-center py-4 text-muted-foreground">
          No comments yet. Be the first to share a story about this recipe!
        </p>
      ) : (
        <>
          <div className="space-y-6">
            {currentComments.map((comment: Comment) => (
              <div key={comment.id} className="flex gap-4">
                <Avatar className="h-10 w-10 mt-1 bg-muted">
                  <AvatarFallback>{getInitials(comment.user.name)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <div>
                      <span className="font-medium">{comment.user.name}</span>
                      <span className="ml-2 text-xs px-2 py-0.5 bg-muted rounded-full text-muted-foreground">
                        {comment.user.role}
                      </span>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                    </span>
                  </div>

                  {editingCommentId === comment.id ? (
                    <div className="space-y-2">
                      <Textarea
                        value={editingText}
                        onChange={(e) => setEditingText(e.target.value)}
                        className="w-full"
                      />
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" onClick={cancelEditing}>
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleUpdateComment(comment.id)}
                          disabled={!editingText.trim() || updateCommentMutation.isPending}
                        >
                          {updateCommentMutation.isPending ? 'Saving...' : 'Save'}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <>
                      <p className="text-muted-foreground">{comment.comment}</p>

                      {/* Comment actions (only for the comment owner, admins, or organizers of the book) */}
                      {(() => {
                        // Check if user can edit/delete this comment
                        const canEditComment =
                          // Comment owner can edit/delete their own comment
                          user?.id === comment.user.id ||
                          // Admin can edit/delete any comment
                          user?.role === 'admin' ||
                          // Organizer can edit/delete any comments on recipes in their books
                          (user?.role === 'organizer' && recipeData?.recipe?.project?.organizerId === user?.id);

                        return canEditComment;
                      })() && (
                        <div className="flex justify-end gap-2 mt-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => startEditing(comment)}
                          >
                            <Edit className="h-3.5 w-3.5" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-destructive hover:text-destructive/80"
                            onClick={() => handleDeleteComment(comment.id)}
                            disabled={deleteCommentMutation.isPending}
                          >
                            <Trash2 className="h-3.5 w-3.5" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Pagination controls */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {indexOfFirstComment + 1}-{Math.min(indexOfLastComment, totalComments)} of {totalComments} comments
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToPrevPage}
                  disabled={currentPage === 1}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="flex items-center gap-1">
                  {Array.from({ length: totalPages }).map((_, index) => (
                    <Button
                      key={index}
                      variant={currentPage === index + 1 ? "default" : "outline"}
                      size="sm"
                      onClick={() => paginate(index + 1)}
                      className="h-8 w-8 p-0"
                    >
                      {index + 1}
                    </Button>
                  ))}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                  className="h-8 w-8 p-0"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}