import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { useAuth } from "@/hooks/use-auth";
import { ImageUpload } from "@/components/ui/image-upload";
import { Combobox } from "@/components/ui/combobox";
import { PricingModel, PricingTier } from "@/lib/constants";

const recipeSchema = z.object({
  projectId: z.number(),
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  category: z.string().min(1, "Category is required"),
  ingredients: z.array(z.object({
    name: z.string().min(1, "Ingredient name is required"),
    amount: z.number().min(0, "Amount must be positive"),
    unit: z.string().min(1, "Unit is required")
  })),
  instructions: z.array(z.string().min(1, "Instruction step is required")),
  tags: z.array(z.string()),
  measurementSystem: z.enum(["us", "metric"]).default("us"),
  images: z.array(z.string()).default([]),
  isPartOfCollection: z.boolean().default(false),
  collectionId: z.number().optional(),
  collectionOrder: z.number().optional()
});

export type RecipeFormData = z.infer<typeof recipeSchema>;

interface Project {
  id: number;
  name: string;
  organizerId: number;
  pricingTier?: string;
  maxRecipes?: number;
  recipes?: any[];
}

export interface RecipeFormProps {
  projects: Project[];
  initialData?: RecipeFormData;
  onSubmit: (data: RecipeFormData) => Promise<any>;
  onProjectSelect?: (projectId: number) => void;
}

const categories = [
  "Main Dishes",
  "Side Dishes",
  "Soups",
  "Salads",
  "Desserts",
  "Breakfast",
  "Snacks",
  "Beverages",
  "Appetizers",
  "Breads",
  "Sauces",
  "Other"
];

const commonIngredients = [
  "Salt", "Pepper", "Sugar", "Flour", "Butter", "Oil", "Garlic", "Onion",
  "Eggs", "Milk", "Cheese", "Tomatoes", "Potatoes", "Rice", "Pasta"
];

export function RecipeForm({ projects, initialData, onSubmit, onProjectSelect }: RecipeFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [measurementSystem, setMeasurementSystem] = useState<"us" | "metric">(initialData?.measurementSystem || "us");
  const [selectedImages, setSelectedImages] = useState<string[]>(initialData?.images || []);
  const { toast } = useToast();
  const { user } = useAuth();

  const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm<RecipeFormData>({
    resolver: zodResolver(recipeSchema),
    defaultValues: {
      ingredients: [{ name: "", amount: 0, unit: "" }],
      instructions: [""],
      tags: [],
      measurementSystem: "us",
      images: [],
      ...initialData
    }
  });

  const handleFormSubmit = async (data: RecipeFormData) => {
    if (!data.projectId) {
      toast({
        title: "Validation Error",
        description: "Please select a recipe book",
        variant: "destructive",
      });
      return;
    }

    if (!data.title.trim() || !data.description.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (data.ingredients.some(ing => !ing.name.trim() || !ing.unit.trim())) {
      toast({
        title: "Validation Error",
        description: "Please fill in all ingredient details",
        variant: "destructive",
      });
      return;
    }

    if (data.instructions.some(inst => !inst.trim())) {
      toast({
        title: "Validation Error",
        description: "Please fill in all instruction steps",
        variant: "destructive",
      });
      return;
    }

    // Check if the selected project has reached its recipe limit
    const selectedProject = projects.find(p => p.id === data.projectId);
    if (selectedProject) {
      const currentRecipeCount = selectedProject.recipes?.length || 0;
      const maxRecipes = selectedProject.maxRecipes ||
        (selectedProject.pricingTier ? PricingModel[selectedProject.pricingTier as keyof typeof PricingModel]?.maxRecipes : 10);

      console.log('Recipe limit check:', {
        currentRecipeCount,
        maxRecipes,
        pricingTier: selectedProject.pricingTier,
        projectId: selectedProject.id
      });

      if (currentRecipeCount >= maxRecipes) {
        toast({
          title: "Recipe Limit Reached",
          description: "You've reached the recipe limit for this tier. Please upgrade to add more recipes.",
          variant: "destructive",
        });
        return;
      }
    }

    setIsLoading(true);
    try {
      // Call onSubmit with the form data
      await onSubmit({
        ...data,
        images: selectedImages
      });

      // Show success toast
      toast({
        title: "Success",
        description: "Recipe submitted successfully!",
      });
    } catch (error) {
      console.error("Error in RecipeForm:", error);

      // Extract error message
      let errorMessage = "Failed to save recipe";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Check if it's a recipe limit error
      if (errorMessage.includes("recipe limit") ||
          errorMessage.includes("maximum number") ||
          errorMessage.includes("reached the maximum")) {
        toast({
          title: "Recipe Limit Reached",
          description: "You've reached the recipe limit for this tier. Please upgrade to add more recipes.",
          variant: "destructive",
        });
      } else {
        // Show general error toast
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const addIngredient = () => {
    const currentIngredients = watch("ingredients");
    setValue("ingredients", [...currentIngredients, { name: "", amount: 0, unit: "" }]);
  };

  const removeIngredient = (index: number) => {
    const currentIngredients = watch("ingredients");
    if (currentIngredients.length > 1) {
      setValue("ingredients", currentIngredients.filter((_, i) => i !== index));
    } else {
      toast({
        title: "Cannot remove",
        description: "At least one ingredient is required",
        variant: "destructive",
      });
    }
  };

  const addInstruction = () => {
    const currentInstructions = watch("instructions");
    setValue("instructions", [...currentInstructions, ""]);
  };

  const removeInstruction = (index: number) => {
    const currentInstructions = watch("instructions");
    if (currentInstructions.length > 1) {
      setValue("instructions", currentInstructions.filter((_, i) => i !== index));
    } else {
      toast({
        title: "Cannot remove",
        description: "At least one instruction step is required",
        variant: "destructive",
      });
    }
  };

  const toggleMeasurementSystem = (checked: boolean) => {
    const newSystem = checked ? "metric" : "us";
    setMeasurementSystem(newSystem);
    setValue("measurementSystem", newSystem);
  };

  const handleImageUpload = (urls: string[]) => {
    setSelectedImages(urls);
    setValue("images", urls);
  };

  const handleProjectChange = (projectId: number) => {
    setValue('projectId', projectId);
    if (onProjectSelect) {
      onProjectSelect(projectId);
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <div className="space-y-4">
        <div>
          <Label htmlFor="projectId">Recipe Book</Label>
          <select
            id="projectId"
            className="w-full p-2 border rounded-md"
            {...register('projectId', { valueAsNumber: true })}
            onChange={(e) => handleProjectChange(Number(e.target.value))}
            disabled={isLoading}
          >
            <option value="">Select recipe book</option>
            {projects.map((project) => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
          {errors.projectId && (
            <p className="text-red-500 text-sm mt-1">Recipe book is required</p>
          )}
        </div>

        <div>
          <Label htmlFor="title">Recipe Title</Label>
          <Input
            id="title"
            {...register("title")}
            placeholder="Enter recipe title"
            required
          />
          {errors.title && (
            <p className="text-sm text-red-500">{errors.title.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="category">Category</Label>
          <Select
            onValueChange={(value) => setValue("category", value)}
            defaultValue={initialData?.category}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.category && (
            <p className="text-sm text-red-500">{errors.category.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            {...register("description")}
            placeholder="Describe your recipe"
            required
          />
          {errors.description && (
            <p className="text-sm text-red-500">{errors.description.message}</p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="measurement-system"
            checked={measurementSystem === "metric"}
            onCheckedChange={toggleMeasurementSystem}
          />
          <Label htmlFor="measurement-system">
            Use Metric System
          </Label>
        </div>

        <div>
          <Label>Ingredients</Label>
          <div className="space-y-2">
            {watch("ingredients").map((ingredient, index) => (
              <div key={index} className="mb-2 relative">
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Amount"
                    className="w-24"
                    {...register(`ingredients.${index}.amount`, { valueAsNumber: true })}
                  />
                  <Input
                    placeholder="Unit"
                    className="w-24"
                    {...register(`ingredients.${index}.unit`)}
                  />
                  <Combobox
                    items={commonIngredients.map(ing => ({ label: ing, value: ing }))}
                    value={ingredient.name}
                    onValueChange={(value: string) => setValue(`ingredients.${index}.name`, value)}
                    placeholder="Select or type ingredient"
                    className="flex-1"
                    allowCustomValue
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="h-10 w-10 text-destructive"
                    onClick={() => removeIngredient(index)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                  </Button>
                </div>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              onClick={addIngredient}
              className="mt-2"
            >
              Add Ingredient
            </Button>
          </div>
        </div>

        <div>
          <Label>Instructions</Label>
          {watch("instructions").map((instruction, index) => (
            <div key={index} className="mb-2 relative">
              <Textarea
                {...register(`instructions.${index}`)}
                placeholder={`Step ${index + 1}`}
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-2 top-2 h-6 w-6 text-destructive"
                onClick={() => removeInstruction(index)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-x"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
              </Button>
            </div>
          ))}
          <Button
            type="button"
            variant="outline"
            onClick={addInstruction}
            className="mt-2"
          >
            Add Step
          </Button>
        </div>

        <div>
          <Label htmlFor="tags">Tags (comma-separated)</Label>
          <Input
            id="tags"
            placeholder="e.g., vegan, family favorite, quick"
            defaultValue={initialData?.tags?.join(", ")}
            onChange={(e) => {
              const tags = e.target.value.split(',').map(tag => tag.trim()).filter(Boolean);
              setValue("tags", tags);
            }}
          />
        </div>

        <div>
          <Label>Recipe Images</Label>
          <ImageUpload
            onUpload={handleImageUpload}
            maxFiles={3}
            acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
            initialFiles={initialData?.images}
          />
        </div>
      </div>

      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? "Saving..." : "Save Recipe"}
      </Button>
    </form>
  );
}