import { useState, useEffect } from 'react';
import { API_URL } from '@/lib/constants';
import { Loader2 } from 'lucide-react';

interface RecipeImagesProps {
  images: string[];
  recipeTitle?: string;
  className?: string;
}

export function RecipeImages({ images, recipeTitle, className }: RecipeImagesProps) {
  const [presignedUrls, setPresignedUrls] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPresignedUrls = async () => {
      if (!images || images.length === 0) {
        setLoading(false);
        return;
      }

      try {
        // Prepare image keys with recipes/ prefix if needed
        const imageKeys = images.map(image =>
          image.startsWith('recipes/') ? image : `recipes/${image}`
        );

        const response = await fetch(`${API_URL}/upload/presigned-urls`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({ keys: imageKeys }),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch presigned URLs');
        }

        const data = await response.json();

        // Create a map of image key to presigned URL
        const urlMap: Record<string, string> = {};
        data.presignedUrls.forEach((item: { key: string; url: string }) => {
          // Store both with and without recipes/ prefix for easier lookup
          urlMap[item.key] = item.url;
          if (item.key.startsWith('recipes/')) {
            urlMap[item.key.replace('recipes/', '')] = item.url;
          }
        });

        setPresignedUrls(urlMap);
      } catch (err) {
        console.error('Error fetching presigned URLs:', err);
        setError(err instanceof Error ? err.message : 'Failed to load images');
      } finally {
        setLoading(false);
      }
    };

    fetchPresignedUrls();
  }, [images]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-48">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-48 text-destructive">
        Error loading images: {error}
      </div>
    );
  }

  if (Object.keys(presignedUrls).length === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-8">
      {images.map((image, index) => {
        // Try to find the URL with or without the recipes/ prefix
        const imageUrl = presignedUrls[image] ||
                         presignedUrls[`recipes/${image}`] ||
                         null;

        if (!imageUrl) {
          console.warn(`No presigned URL found for image: ${image}`);
          return null;
        }

        return (
          <img
            key={index}
            className={className || "w-full h-48 object-cover rounded-t-lg"}
            src={imageUrl}
            alt={`${recipeTitle || 'Recipe'} image ${index + 1}`}
          />
        );
      })}
    </div>
  );
}
