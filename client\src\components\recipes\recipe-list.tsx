import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { RecipeFormModal } from "./recipe-form-modal";
import { API_URL } from '@/lib/constants';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
}

export function RecipeList({ projectId }: { projectId: number }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  const { data: recipes, isLoading } = useQuery<Recipe[]>({
    queryKey: ['project-recipes', projectId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/contributor/recipes/${projectId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch recipes');
      const data = await response.json();
      return data.recipes;
    }
  });

  const getStatusBadge = (status: Recipe['status']) => {
    const styles = {
      pending: 'bg-amber-500 text-white hover:bg-amber-600',
      approved: 'bg-emerald-500 text-white hover:bg-emerald-600',
      rejected: 'bg-rose-500 text-white hover:bg-rose-600'
    };

    return (
      <Badge className={styles[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="border-t pt-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Your Recipe Submissions</h3>
          <Button size="sm" onClick={() => setIsModalOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Submit Recipe
          </Button>
        </div>

        {isLoading ? (
          <div className="text-center py-4">Loading recipes...</div>
        ) : recipes && recipes.length > 0 ? (
          <div className="space-y-4">
            {recipes.map((recipe) => (
              <Card key={recipe.id} className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium mb-1">{recipe.title}</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      {recipe.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="text-muted-foreground">
                        Submitted on {new Date(recipe.createdAt).toLocaleDateString()}
                      </span>
                      {getStatusBadge(recipe.status)}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <p className="text-center text-muted-foreground py-4">
            You haven't submitted any recipes yet.
          </p>
        )}
      </div>

      <RecipeFormModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        projectId={projectId}
      />
    </div>
  );
}
