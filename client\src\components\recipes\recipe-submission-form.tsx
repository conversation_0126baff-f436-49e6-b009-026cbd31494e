import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Plus, Scan } from "lucide-react";
import { RecipeForm } from "./recipe-form";
import { useToast } from "@/hooks/use-toast";
import { API_URL } from '@/lib/constants';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { OcrRecipeUpload } from "./ocr-recipe-upload";

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  category: string;
  createdAt: string;
  updatedAt: string;
}

interface Project {
  id: number;
  name: string;
  organizerId: number;
}

export function RecipeSubmissionForm({ projectId }: { projectId: number }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("manual");
  const { toast } = useToast();

  // Fetch project details
  const { data: project } = useQuery<Project>({
    queryKey: ['project', projectId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/contributor/projects/${projectId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch project');
      return response.json();
    }
  });

  // Fetch recipes for this project
  const { data: recipes, isLoading, refetch } = useQuery<Recipe[]>({
    queryKey: ['project-recipes', projectId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/contributor/recipes/${projectId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch recipes');
      const data = await response.json();
      return data.recipes;
    }
  });

  const getStatusBadgeVariant = (status: Recipe['status']) => {
    switch (status) {
      case 'approved':
        return 'bg-emerald-500 text-white';
      case 'rejected':
        return 'bg-rose-500 text-white';
      default:
        return 'bg-amber-500 text-white';
    }
  };

  const handleSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      const response = await fetch(`${API_URL}/contributor/recipes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ ...data, projectId })
      });

      if (!response.ok) {
        throw new Error('Failed to submit recipe');
      }

      toast({
        title: 'Success',
        description: 'Recipe submitted successfully',
      });
      setShowForm(false);
      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to submit recipe',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOcrComplete = (recipe: any) => {
    // Refresh the recipes list
    refetch();
    setShowForm(false);
  };

  if (isLoading) {
    return <div className="text-center py-4">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Your Submissions</h2>
        <Button onClick={() => setShowForm(true)} disabled={isSubmitting}>
          <Plus className="w-4 h-4 mr-2" />
          Submit New Recipe
        </Button>
      </div>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>New Recipe</CardTitle>
            <CardDescription>
              Fill out the recipe details below or scan from an image
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="manual" onValueChange={setActiveTab} value={activeTab}>
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="manual">Manual Entry</TabsTrigger>
                <TabsTrigger value="scan" className="flex items-center">
                  <Scan className="h-4 w-4 mr-2" />
                  Scan Recipe
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="manual">
                <RecipeForm 
                  projects={[{ id: projectId, name: project?.name || '', organizerId: project?.organizerId || 0 }]}
                  onSubmit={handleSubmit}
                />
              </TabsContent>
              
              <TabsContent value="scan">
                <OcrRecipeUpload 
                  projectId={projectId} 
                  onComplete={handleOcrComplete}
                />
              </TabsContent>
            </Tabs>
            
            <Button 
              variant="outline" 
              onClick={() => setShowForm(false)} 
              className="mt-4"
            >
              Cancel
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4">
        {recipes?.map((recipe) => (
          <Card key={recipe.id}>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-lg mb-1">{recipe.title}</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    {recipe.description}
                  </p>
                  <div className="flex items-center gap-2 text-sm">
                    <span className="text-muted-foreground">
                      Submitted on {new Date(recipe.createdAt).toLocaleDateString()}
                    </span>
                    <Badge className={getStatusBadgeVariant(recipe.status)}>
                      {recipe.status.charAt(0).toUpperCase() + recipe.status.slice(1)}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {recipes?.length === 0 && !showForm && (
          <Card>
            <CardContent className="py-8 text-center">
              <p className="text-muted-foreground">
                You haven't submitted any recipes yet. Click "Submit New Recipe" to get started.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
