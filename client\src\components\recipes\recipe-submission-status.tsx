import { useQuery } from "@tanstack/react-query";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import { RecipeForm, type RecipeFormData } from "./recipe-form";
import { useToast } from "@/hooks/use-toast";
import { API_URL } from '@/lib/constants';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  category: string;
  createdAt: string;
}

interface Project {
  id: number;
  name: string;
}

export function RecipeSubmissionStatus({ projectId }: { projectId: number }) {
  const [showSubmitForm, setShowSubmitForm] = useState(false);
  const { toast } = useToast();

  const { data: recipes, isLoading, refetch } = useQuery<Recipe[]>({
    queryKey: ['project-recipes', projectId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/contributor/recipes/${projectId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch recipes');
      const data = await response.json();
      return data.recipes;
    }
  });

  const { data: project } = useQuery<Project>({
    queryKey: ['project', projectId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/contributor/projects/${projectId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch project');
      return response.json();
    }
  });

  const getStatusBadgeStyle = (status: Recipe['status']) => {
    switch (status) {
      case 'approved':
        return 'bg-emerald-500 text-white hover:bg-emerald-600';
      case 'rejected':
        return 'bg-rose-500 text-white hover:bg-rose-600';
      default:
        return 'bg-amber-500 text-white hover:bg-amber-600';
    }
  };

  const handleSubmit = async (data: RecipeFormData) => {
    try {
      const response = await fetch(`${API_URL}/contributor/recipes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ ...data, projectId })
      });

      if (!response.ok) {
        throw new Error('Failed to submit recipe');
      }

      await refetch();
      setShowSubmitForm(false);
      toast({
        title: "Success",
        description: "Recipe submitted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit recipe",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return <div>Loading submissions...</div>;
  }

  return (
    <div className="space-y-4">
      {!showSubmitForm && (
        <Button 
          onClick={() => setShowSubmitForm(true)}
          className="w-full mb-4"
        >
          <Plus className="w-4 h-4 mr-2" />
          Submit New Recipe
        </Button>
      )}

      {showSubmitForm && (
        <div className="mb-6">
          <h4 className="font-medium mb-4">Submit New Recipe</h4>
          <RecipeForm
            projects={[{ id: projectId, name: project?.name || '', organizerId: 0 }]}
            onSubmit={handleSubmit}
          />
          <Button 
            variant="outline" 
            onClick={() => setShowSubmitForm(false)}
            className="mt-4"
          >
            Cancel
          </Button>
        </div>
      )}

      <div className="space-y-3">
        {recipes?.map((recipe) => (
          <Card key={recipe.id} className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium">{recipe.title}</h4>
                <p className="text-sm text-muted-foreground mb-2">{recipe.description}</p>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-muted-foreground">
                    Submitted: {new Date(recipe.createdAt).toLocaleDateString()}
                  </span>
                  <Badge className={getStatusBadgeStyle(recipe.status)}>
                    {recipe.status.charAt(0).toUpperCase() + recipe.status.slice(1)}
                  </Badge>
                </div>
              </div>
            </div>
          </Card>
        ))}

        {recipes?.length === 0 && !showSubmitForm && (
          <p className="text-center text-muted-foreground py-4">
            You haven't submitted any recipes yet.
          </p>
        )}
      </div>
    </div>
  );
}
