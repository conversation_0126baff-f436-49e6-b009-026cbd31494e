import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { API_URL } from '@/lib/constants';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
}

export function RecipeSubmissions({ projectId }: { projectId: number }) {
  const { data: recipes, isLoading } = useQuery<Recipe[]>({
    queryKey: ['project-recipes', projectId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/contributor/recipes/${projectId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (!response.ok) throw new Error('Failed to fetch recipes');
      const data = await response.json();
      return data.recipes;
    }
  });

  if (isLoading) {
    return <div className="text-center py-4">Loading recipes...</div>;
  }

  if (!recipes?.length) {
    return (
      <p className="text-center text-muted-foreground py-4">
        No recipes submitted yet.
      </p>
    );
  }

  return (
    <div className="space-y-4">
      {recipes.map((recipe) => (
        <Card key={recipe.id} className="bg-muted/50">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium mb-1">{recipe.title}</h4>
                <p className="text-sm text-muted-foreground mb-2">{recipe.description}</p>
                <div className="flex items-center gap-4 text-sm">
                  <span className="text-muted-foreground">
                    Submitted on {new Date(recipe.createdAt).toLocaleDateString()}
                  </span>
                  <Badge 
                    className={
                      recipe.status === 'approved' 
                        ? 'bg-emerald-500 text-white hover:bg-emerald-600'
                        : recipe.status === 'rejected'
                        ? 'bg-rose-500 text-white hover:bg-rose-600'
                        : 'bg-amber-500 text-white hover:bg-amber-600'
                    }
                  >
                    {recipe.status.charAt(0).toUpperCase() + recipe.status.slice(1)}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
