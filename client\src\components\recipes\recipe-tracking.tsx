import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { API_URL } from '@/lib/constants';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt: string;
  category: string;
}

interface RecipeTrackingProps {
  projectId: number;
}

export function RecipeTracking({ projectId }: RecipeTrackingProps) {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchRecipes = async () => {
      try {
        const response = await fetch(`${API_URL}/contributor/recipes/${projectId}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch recipes");
        }

        const data = await response.json();
        setRecipes(data.recipes);
      } catch (error) {
        console.error("Error fetching recipes:", error);
        toast({
          title: "Error",
          description: "Failed to load your recipes. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecipes();
  }, [projectId, toast]);
  const getStatusColor = (status: Recipe['status']) => {
    switch (status) {
      case 'approved':
        return 'bg-emerald-500 text-white';
      case 'rejected':
        return 'bg-rose-500 text-white';
      default:
        return 'bg-amber-500 text-white';
    }
  };

  if (isLoading) {
    return <div className="text-center py-4">Loading recipes...</div>;
  }

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle>Your Recipes</CardTitle>
        <CardDescription>
          Track the status of your recipe submissions
        </CardDescription>
      </CardHeader>
      <CardContent>
        {recipes.length === 0 ? (
          <p className="text-muted-foreground">You haven't submitted any recipes yet.</p>
        ) : (
          <div className="space-y-4">
            {recipes.map((recipe) => (
              <div
                key={recipe.id}
                className="p-4 border rounded-lg flex items-center justify-between"
              >
                <div className="space-y-1">
                  <h3 className="font-medium">{recipe.title}</h3>
                  <p className="text-sm text-muted-foreground">{recipe.description}</p>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>{recipe.category}</span>
                    <span>•</span>
                    <span>
                      Submitted on {new Date(recipe.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                <Badge className={getStatusColor(recipe.status)}>
                  {recipe.status.charAt(0).toUpperCase() + recipe.status.slice(1)}
                </Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
