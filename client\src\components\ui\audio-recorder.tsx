import React, { useState, useRef, useEffect } from 'react';
import { Button } from './button';
import { Mic, Square, Loader2, Upload } from 'lucide-react';
import { API_URL } from '@/lib/constants';

interface AudioRecorderProps {
  onUpload: (audioUrl: string) => void;
}

export function AudioRecorder({ onUpload }: AudioRecorderProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Clean up on component unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const startRecording = async () => {
    try {
      // Reset state
      chunksRef.current = [];
      setAudioUrl(null);
      
      // Get permission to use microphone
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Create media recorder
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      
      // Set up event handlers
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        // Create blob from recorded chunks
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/mp3' });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        
        // Stop all tracks in the stream to release the microphone
        stream.getTracks().forEach(track => track.stop());
      };
      
      // Start recording
      mediaRecorder.start();
      setIsRecording(true);
      
      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Could not access microphone. Please check your browser permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      // Stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };

  const uploadRecording = async () => {
    if (!audioUrl) return;
    
    setIsUploading(true);
    
    try {
      // Create a file from the Blob
      const audioBlob = await fetch(audioUrl).then(r => r.blob());
      const fileName = `recipe_recording_${Date.now()}.mp3`;
      
      console.log(`Preparing to upload audio file: ${fileName}`);
      
      // Get a presigned URL from the server
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_URL}/upload/audio-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          fileName,
          fileType: 'audio/mp3'
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to get upload URL');
      }
      
      const { uploadUrl, key } = await response.json();
      console.log(`Got S3 upload URL. Key: ${key}`);
      
      // Upload the file directly to S3
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: {
          'Content-Type': 'audio/mp3'
        }
      });
      
      if (!uploadResponse.ok) {
        throw new Error('Failed to upload audio file');
      }
      
      console.log(`Successfully uploaded audio file to S3`);
      
      // Pass the S3 key to the parent component
      console.log(`Passing key to parent component: ${key}`);
      
      // Force update with a small delay to ensure state changes propagate
      setTimeout(() => {
        onUpload(key);
        
        // Reset audio state after a slight delay to ensure the key is properly passed up
        setTimeout(() => {
          setAudioUrl(null);
        }, 100);
      }, 100);
    } catch (error) {
      console.error('Error uploading recording:', error);
      alert('Failed to upload recording. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Format recording time as mm:ss
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="flex flex-col items-center p-4 border rounded-md bg-gray-50">
      <div className="text-center mb-4">
        {isRecording ? (
          <div className="text-red-500 font-semibold">Recording... {formatTime(recordingTime)}</div>
        ) : audioUrl ? (
          <div className="font-semibold">Recording Complete</div>
        ) : (
          <div>Click to start recording your recipe</div>
        )}
      </div>
      
      <div className="flex space-x-2 mb-4">
        {!audioUrl ? (
          isRecording ? (
            <Button 
              onClick={stopRecording} 
              variant="destructive"
            >
              <Square className="mr-2 h-4 w-4" />
              Stop Recording
            </Button>
          ) : (
            <Button 
              onClick={startRecording}
              className="bg-[#9B7A5D] hover:bg-[#8B6A4D]"
            >
              <Mic className="mr-2 h-4 w-4" />
              Start Recording
            </Button>
          )
        ) : (
          <Button 
            onClick={uploadRecording} 
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload Recording
              </>
            )}
          </Button>
        )}
        
        {audioUrl && !isUploading && (
          <Button 
            variant="outline" 
            onClick={() => setAudioUrl(null)}
          >
            Discard
          </Button>
        )}
      </div>
      
      {audioUrl && (
        <audio src={audioUrl} controls className="w-full" />
      )}
    </div>
  );
} 