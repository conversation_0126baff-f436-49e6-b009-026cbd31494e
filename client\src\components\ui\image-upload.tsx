import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from './button';
import { X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { API_URL } from '@/lib/constants';

interface ImageUploadProps {
  onUpload: (urls: string[]) => void;
  maxFiles?: number;
  acceptedFileTypes?: string[];
  initialFiles?: string[];
}

export function ImageUpload({ onUpload, maxFiles = 3, acceptedFileTypes = ['image/jpeg', 'image/png', 'image/webp'], initialFiles = [] }: ImageUploadProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);

  // Fetch presigned URLs for initial files
  useEffect(() => {
    const fetchPresignedUrls = async () => {
      if (!initialFiles || initialFiles.length === 0) return;

      // Filter out files that are already URLs
      const filesToFetch = initialFiles.filter(file => !file.includes('http'));

      if (filesToFetch.length === 0) {
        // If all files are already URLs, just use those
        setPreviews(initialFiles);
        return;
      }

      try {
        // Prepare image keys with recipes/ prefix if needed
        const imageKeys = filesToFetch.map(file =>
          file.startsWith('recipes/') ? file : `recipes/${file}`
        );

        const response = await fetch(`${API_URL}/upload/presigned-urls`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({ keys: imageKeys }),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch presigned URLs');
        }

        const data = await response.json();

        // Create a map of image key to presigned URL
        const urlMap: Record<string, string> = {};
        data.presignedUrls.forEach((item: { key: string; url: string }) => {
          // Store both with and without recipes/ prefix for easier lookup
          urlMap[item.key] = item.url;
          if (item.key.startsWith('recipes/')) {
            urlMap[item.key.replace('recipes/', '')] = item.url;
          }
        });

        // Map initial files to their presigned URLs or keep as is if they're already URLs
        const newPreviews = initialFiles.map(file => {
          if (file.includes('http')) return file;

          // Try to find the URL with or without the recipes/ prefix
          return urlMap[file] ||
                 urlMap[`recipes/${file}`] ||
                 `https://recipe-book-images-bucket.s3.eu-north-1.amazonaws.com/recipes/${file}`;
        });

        setPreviews(newPreviews);
      } catch (error) {
        console.error('Error fetching presigned URLs:', error);
        // Fallback to direct URLs if presigned URLs fail
        const fallbackPreviews = initialFiles.map(file => {
          if (file.includes('http')) return file;
          const imagePath = file.startsWith('recipes/') ? file : `recipes/${file}`;
          return `https://recipe-book-images-bucket.s3.eu-north-1.amazonaws.com/${imagePath}`;
        });
        setPreviews(fallbackPreviews);
      }
    };

    fetchPresignedUrls();
  }, [initialFiles]);
  const [existingFiles, setExistingFiles] = useState<string[]>(initialFiles);
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();

  const uploadToS3 = async (file: File): Promise<string> => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Get signed URL from backend
      const response = await fetch(`${API_URL}/upload/signed-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          fileName: file.name,
          fileType: file.type,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to get signed URL' }));
        throw new Error(errorData.error || 'Failed to get signed URL');
      }

      const { signedUrl, key } = await response.json();

      // Upload file to S3
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload to S3');
      }

      // Return just the S3 key
      return key;
    } catch (error) {
      console.error('Error uploading to S3:', error);
      throw error;
    }
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    // Calculate remaining slots based only on existingFiles
    const remainingSlots = maxFiles - existingFiles.length;

    if (acceptedFiles.length > remainingSlots) {
      toast({
        title: "Error",
        description: `You can only upload up to ${maxFiles} files`,
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    try {
      const newFiles = [...files, ...acceptedFiles];
      setFiles(newFiles);

      // Create temporary preview URLs from local files
      const tempPreviews = acceptedFiles.map(file => URL.createObjectURL(file));
      setPreviews(prev => [...prev, ...tempPreviews]);

      // Upload files to S3 and get their keys
      const uploadPromises = acceptedFiles.map(file => uploadToS3(file));
      const uploadedKeys = await Promise.all(uploadPromises);

      // Update parent component with both existing and new files
      const updatedExistingFiles = [...existingFiles, ...uploadedKeys];
      setExistingFiles(updatedExistingFiles);
      onUpload(updatedExistingFiles);

      // Get presigned URLs for the newly uploaded files
      try {
        // Prepare image keys with recipes/ prefix
        const imageKeys = uploadedKeys.map(key =>
          key.startsWith('recipes/') ? key : `recipes/${key}`
        );

        const response = await fetch(`${API_URL}/upload/presigned-urls`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({ keys: imageKeys }),
        });

        if (response.ok) {
          const data = await response.json();

          // Replace the temporary object URLs with presigned URLs
          const presignedUrlMap: Record<string, string> = {};
          data.presignedUrls.forEach((item: { key: string; url: string }) => {
            const keyWithoutPrefix = item.key.replace('recipes/', '');
            presignedUrlMap[keyWithoutPrefix] = item.url;
          });

          // Update previews with presigned URLs
          setPreviews(prev => {
            const updatedPreviews = [...prev];
            // Replace the last N (where N is the number of uploaded files) previews
            // with presigned URLs
            for (let i = 0; i < uploadedKeys.length; i++) {
              const key = uploadedKeys[i];
              const keyWithoutPrefix = key.replace('recipes/', '');
              if (presignedUrlMap[keyWithoutPrefix]) {
                const previewIndex = updatedPreviews.length - uploadedKeys.length + i;
                if (previewIndex >= 0 && previewIndex < updatedPreviews.length) {
                  // Revoke the object URL to prevent memory leaks
                  if (updatedPreviews[previewIndex].startsWith('blob:')) {
                    URL.revokeObjectURL(updatedPreviews[previewIndex]);
                  }
                  updatedPreviews[previewIndex] = presignedUrlMap[keyWithoutPrefix];
                }
              }
            }
            return updatedPreviews;
          });
        }
      } catch (error) {
        console.error('Error getting presigned URLs for previews:', error);
        // We'll keep the object URLs as fallback
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to upload images",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [maxFiles, onUpload, existingFiles, toast, files]);

  const removeFile = (index: number) => {
    const isExistingFile = index < existingFiles.length;

    // Revoke the preview URL if it's a blob to prevent memory leaks
    const previewUrl = previews[index];
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }

    if (isExistingFile) {
      // Remove from existing files
      const newExistingFiles = existingFiles.filter((_, i) => i !== index);
      setExistingFiles(newExistingFiles);

      // Remove from previews
      const newPreviews = previews.filter((_, i) => i !== index);
      setPreviews(newPreviews);

      // Update parent with remaining files
      onUpload(newExistingFiles);
    } else {
      // Calculate the index in the new files array
      const newFileIndex = index - existingFiles.length;

      // Remove from files array
      const newFiles = files.filter((_, i) => i !== newFileIndex);
      setFiles(newFiles);

      // Remove from previews
      const newPreviews = previews.filter((_, i) => i !== index);
      setPreviews(newPreviews);

      // Update parent with remaining files
      onUpload(existingFiles);
    }
  };

  // Calculate remaining file slots
  const remainingSlots = Math.max(0, maxFiles - existingFiles.length);

  // Handler for when files are rejected (e.g., too many files)
  const onDropRejected = useCallback((rejectedFiles: any[]) => {
    // Check if rejection is due to too many files
    const tooManyFiles = rejectedFiles.some(item => item.errors?.some(error => error.code === 'too-many-files'));

    if (tooManyFiles) {
      toast({
        title: "Error",
        description: `You can only upload up to ${maxFiles} files`,
        variant: "destructive",
      });
    }
  }, [maxFiles, toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    onDropRejected,
    accept: acceptedFileTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    maxFiles: remainingSlots,
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${isDragActive ? 'border-primary bg-primary/10' : 'border-gray-300 hover:border-primary'}
          ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <input {...getInputProps()} disabled={isUploading} />
        {isUploading ? (
          <p>Uploading...</p>
        ) : isDragActive ? (
          <p>Drop the files here ...</p>
        ) : (
          <p>Drag & drop images here, or click to select files</p>
        )}
      </div>

      {previews.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {previews.map((preview, index) => (
            <div key={index} className="relative group">
              <img
                src={preview}
                alt={`Preview ${index + 1}`}
                className="w-full h-32 object-cover rounded-lg"
              />
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => removeFile(index)}
                disabled={isUploading}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}