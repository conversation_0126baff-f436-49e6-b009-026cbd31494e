@tailwind base;
@tailwind components;
@tailwind utilities;

html, body {
  overflow-x: hidden;
}

@layer base {
  :root {
    --background: 210 30% 98%;
    --foreground: 215 25% 27%;
    
    --muted: 210 25% 96%;
    --muted-foreground: 215 20% 45%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;
    
    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;
    
    --border: 210 25% 92%;
    --input: 210 25% 92%;
    
    --primary: 210 64% 27%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 32 95% 65%;
    --secondary-foreground: 215 25% 27%;
    
    --accent: 15 85% 75%;
    --accent-foreground: 215 25% 27%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    
    --ring: 210 64% 27%;
    
    --radius: 0.5rem;
  }
}
 
@layer base {
  * {
    border-color: hsl(var(--border));
  }
  
  body {
    @apply font-sans antialiased bg-[#F8F7F4] text-[#2E4B7A] transition-colors duration-300;
  }
  
  .font-serif {
    font-family: 'Playfair Display', serif;
  }
  
  .font-handwritten {
    font-family: 'Caveat', cursive;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-[#E6D5C4]/30;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-[#9B7A5D] rounded-full;
  }
}

@layer utilities {
  .card-hover {
    @apply transition-transform duration-200 hover:-translate-y-1 hover:shadow-lg;
  }
  
  /* Fade-in animation utility */
  .fade-in {
    @apply opacity-0 animate-[fadeIn_0.6s_ease-in-out_forwards];
  }
  
  /* Staggered animation delays */
  .delay-100 {
    animation-delay: 100ms;
  }
  .delay-200 {
    animation-delay: 200ms;
  }
  .delay-300 {
    animation-delay: 300ms;
  }
  .delay-400 {
    animation-delay: 400ms;
  }
  .delay-500 {
    animation-delay: 500ms;
  }
  
  /* Gradient text utils */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-[#2E4B7A] to-[#2E4B7A]/80 bg-clip-text text-transparent;
  }
  
  .text-gradient-secondary {
    @apply bg-gradient-to-r from-[#9B7A5D] to-[#9B7A5D]/80 bg-clip-text text-transparent;
  }
  
  /* Image hover effects */
  .image-zoom {
    @apply overflow-hidden;
  }
  
  .image-zoom img {
    @apply transition-transform duration-500;
  }
  
  .image-zoom:hover img {
    @apply scale-110;
  }
  
  /* Fancy border animation */
  .border-shimmer {
    @apply relative overflow-hidden;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }
  
  @keyframes shimmer {
    0% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Page transitions */
  .page-enter {
    @apply opacity-0;
  }
  
  .page-enter-active {
    @apply opacity-100 transition-opacity duration-300;
  }
  
  .page-exit {
    @apply opacity-100;
  }
  
  .page-exit-active {
    @apply opacity-0 transition-opacity duration-300;
  }
  
  /* Card effects */
  .card-lift {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .card-lift:hover {
    @apply -translate-y-2 shadow-xl;
  }
  
  /* Interactive elements */
  .interactive-icon {
    @apply transition-transform duration-200 hover:scale-110;
  }
  
  /* Glass effect */
  .glass-effect {
    @apply bg-opacity-10 backdrop-blur-md border border-white/20;
  }
}
