export const UserRole = {
  ADMIN: 'admin',
  ORGANIZER: 'organizer',
  CONTRIBUTOR: 'contributor'
} as const;

export const ProjectStatus = {
  DRAFT: 'draft',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed'
} as const;

export const RecipeStatus = {
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  APPROVED: 'approved'
} as const;

export const ContributorStatus = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected'
} as const;

// Pricing Tiers
export const PricingTier = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
} as const;

// Pricing Model
export const PricingModel = {
  [PricingTier.SMALL]: {
    name: 'Small',
    maxContributors: 1, // Limited to 1 for testing
    minRecipes: 1,
    maxRecipes: 1, // Limited to 1 for testing
    basePrice: 29.99,
    pricePerPage: 0.10,
    description: 'Perfect for small families (up to 1 contributor and 1 recipe for testing)'
  },
  [PricingTier.MEDIUM]: {
    name: 'Medium',
    maxContributors: 5, // Reduced for testing
    minRecipes: 6,
    maxRecipes: 15,
    basePrice: 49.99,
    pricePerPage: 0.08,
    description: 'Great for extended families (up to 5 contributors)'
  },
  [PricingTier.LARGE]: {
    name: 'Large',
    maxContributors: 10, // Reduced for testing
    minRecipes: 16,
    maxRecipes: 30,
    basePrice: 79.99,
    pricePerPage: 0.06,
    description: 'Ideal for large family reunions (up to 10 contributors)'
  }
};

// Page Count Estimation
export const PAGE_COUNT_PER_RECIPE = 2; // Estimated pages per recipe
export const MINIMUM_PAGE_COUNT = 20; // Minimum page count for any book

// API URLs
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL ||
  (import.meta.env.MODE === 'production'
    ? 'https://storyworth.onrender.com'
    : 'http://localhost:3001');

export const API_URL = `${API_BASE_URL}/api`;

// Client URLs
export const CLIENT_URL = process.env.NODE_ENV === 'production'
  ? 'https://storyworth.vercel.app'
  : 'http://localhost:3000';

// Vision API Key
export const VISION_API_KEY = import.meta.env.VITE_VISION_API_KEY || '';

// Other constants can go here...