import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { API_URL } from '@/lib/constants';

export default function AcceptInvite() {
  console.log('AcceptInvite component rendered');
  
  const [isLoading, setIsLoading] = useState(false);
  const [projectName, setProjectName] = useState("");
  const [organizerName, setOrganizerName] = useState("");
  const [invitationStatus, setInvitationStatus] = useState<"pending" | "accepted" | "invalid">("pending");
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const { user, isLoading: isAuthLoading } = useAuth();

  // Function to accept the invitation
  const acceptInvitation = async (projectId: string, invitationToken: string) => {
    if (!user) {
      return false;
    }

    try {
      const authToken = localStorage.getItem("token");
      if (!authToken) {
        throw new Error("Authentication token not found");
      }

      console.log('Accepting invitation with:', { projectId, invitationToken });

      const response = await fetch(`${API_URL}/contributor/projects/${projectId}/accept-invite`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({ token: invitationToken })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.message || "Failed to accept invitation");
      }

      const data = await response.json();
      console.log('Invitation accepted:', data);

      // Set invitation status to accepted regardless of who invited
      setInvitationStatus("accepted");
      toast({
        title: "Success",
        description: "You have successfully joined the project!",
      });

      // Redirect to contributor dashboard after a short delay
      setTimeout(() => {
        navigate("/recipe-books");
      }, 1500);

      return true;
    } catch (error) {
      console.error("Error accepting invitation:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to accept invitation. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  useEffect(() => {
    console.log('AcceptInvite useEffect running');
    
    // Get project and organizer info from URL params
    const params = new URLSearchParams(window.location.search);
    const projectId = params.get("projectId");
    const invitationToken = params.get("token");
    const fromEmail = params.get("fromEmail") === "true";

    console.log('URL params:', { projectId, invitationToken, fromEmail });

    // If no parameters, show invalid invitation message
    if (!projectId || !invitationToken) {
      console.log('No parameters found, showing invalid invitation message');
      setInvitationStatus("invalid");
      return;
    }

    // Store invitation token and projectId in localStorage
    localStorage.setItem("invitationToken", invitationToken);
    localStorage.setItem("invitationProjectId", projectId);

    // If auth is still loading, wait
    if (isAuthLoading) {
      console.log('Auth is still loading, waiting...');
      return;
    }

    // If user is not logged in, redirect to login with return URL
    if (!user) {
      console.log('User not logged in, redirecting to login');
      const returnUrl = `/accept-invite?projectId=${projectId}&token=${invitationToken}&fromEmail=true`;
      navigate(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
      return;
    }

    // Fetch project details and check invitation status
    const fetchProjectDetails = async () => {
      try {
        console.log('Fetching project details');
        const authToken = localStorage.getItem("token");
        if (!authToken) {
          throw new Error("Authentication token not found");
        }

        const response = await fetch(`${API_URL}/contributor/projects/${projectId}/invite-status?token=${invitationToken}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: "Failed to fetch project details" }));
          console.error('Error fetching project details:', errorData);
          throw new Error(errorData.message || "Failed to fetch project details");
        }

        const data = await response.json();
        console.log('Project details:', data);
        
        setProjectName(data.projectName);
        setOrganizerName(data.organizerName);
        setInvitationStatus(data.status);

        // If user came from email and invitation is still pending, automatically accept it
        if (fromEmail && data.status === "pending") {
          setIsLoading(true);
          await acceptInvitation(projectId, invitationToken);
          setIsLoading(false);
        } else if (data.status === "accepted") {
          // If invitation is already accepted, just show a success message without trying to accept again
          toast({
            title: "Already Joined",
            description: "You have already joined this project.",
          });
          
          // Redirect to contributor dashboard after a short delay
          setTimeout(() => {
            navigate("/recipe-books");
          }, 1500);
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
        toast({
          title: "Error",
          description: "Failed to load project details. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchProjectDetails();
  }, [toast, navigate, user, isAuthLoading]);

  const handleAcceptInvite = async () => {
    if (!user) {
      const params = new URLSearchParams(window.location.search);
      const projectId = params.get("projectId");
      const invitationToken = params.get("token");
      const returnUrl = `/accept-invite?projectId=${projectId}&token=${invitationToken}`;
      navigate(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
      return;
    }

    // Don't try to accept if already accepted
    if (invitationStatus === "accepted") {
      toast({
        title: "Already Joined",
        description: "You have already joined this project.",
      });
      
      // Redirect to contributor dashboard after a short delay
      setTimeout(() => {
        navigate("/recipe-books");
      }, 1500);
      return;
    }

    setIsLoading(true);
    const params = new URLSearchParams(window.location.search);
    const projectId = params.get("projectId");
    const invitationToken = params.get("token");

    if (!projectId || !invitationToken) {
      toast({
        title: "Error",
        description: "Missing project ID or token",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    await acceptInvitation(projectId, invitationToken);
    setIsLoading(false);
  };

  if (invitationStatus === "invalid") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invalid Invitation</CardTitle>
            <CardDescription>
              This invitation link is invalid or has expired.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              className="w-full"
              onClick={() => navigate("/recipe-books")}
            >
              Go to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Project Invitation</CardTitle>
          <CardDescription>
            {organizerName} has invited you to contribute to their recipe book "{projectName}"
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            By accepting this invitation, you'll be able to submit recipes to this project.
          </p>
          
          {isLoading ? (
            <div className="text-center">
              <p>Processing your invitation...</p>
            </div>
          ) : invitationStatus === "pending" ? (
            <div className="space-y-4">
              <Button
                className="w-full bg-green-600 hover:bg-green-700 text-white"
                onClick={handleAcceptInvite}
                disabled={isLoading}
              >
                Accept Invitation
              </Button>
              <p className="text-xs text-center text-muted-foreground">
                If the invitation wasn't automatically accepted, please click the button above.
              </p>
            </div>
          ) : invitationStatus === "accepted" ? (
            <div className="text-center text-green-600">
              You have successfully joined the project!
            </div>
          ) : null}
        </CardContent>
      </Card>
    </div>
  );
} 