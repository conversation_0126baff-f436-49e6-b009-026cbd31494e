import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { API_URL } from '@/lib/constants';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  contributor: {
    name: string;
    email: string;
  };
  project: {
    name: string;
  };
}

export default function AdminDashboard() {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingRecipeId, setLoadingRecipeId] = useState<number | null>(null);
  const [rejectionDialogOpen, setRejectionDialogOpen] = useState(false);
  const [selectedRecipeId, setSelectedRecipeId] = useState<number | null>(null);
  const [rejectionMessage, setRejectionMessage] = useState("");
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchPendingRecipes = async () => {
      try {
        const response = await fetch(`${API_URL}/admin/pending-recipes`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch pending recipes");
        }

        const data = await response.json();
        setRecipes(data.recipes);
      } catch (error) {
        console.error("Error fetching pending recipes:", error);
        toast({
          title: "Error",
          description: "Failed to load pending recipes. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPendingRecipes();
  }, [toast]);

  const handleStatusUpdate = async (recipeId: number, status: 'approved' | 'rejected', message?: string) => {
    try {
      setLoadingRecipeId(recipeId);
      const response = await fetch(`${API_URL}/admin/recipes/${recipeId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({ status, rejectionMessage: message }),
      });

      if (!response.ok) {
        throw new Error("Failed to update recipe status");
      }

      // Update local state
      setRecipes(recipes?.filter(recipe => recipe.id !== recipeId));

      toast({
        title: "Success",
        description: `Recipe ${status} successfully.`,
      });

      // Close rejection dialog if it was open
      if (status === 'rejected') {
        setRejectionDialogOpen(false);
        setRejectionMessage("");
      }
    } catch (error) {
      console.error("Error updating recipe status:", error);
      toast({
        title: "Error",
        description: "Failed to update recipe status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingRecipeId(null);
    }
  };

  const handleRejectClick = (recipeId: number) => {
    setSelectedRecipeId(recipeId);
    setRejectionDialogOpen(true);
  };

  const handleRejectConfirm = () => {
    if (selectedRecipeId) {
      handleStatusUpdate(selectedRecipeId, 'rejected', rejectionMessage);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Welcome, {user?.name}</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Pending Recipe Approvals</CardTitle>
          <CardDescription>
            Review and approve or reject submitted recipes
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recipes?.length === 0 ? (
            <p className="text-muted-foreground">No pending recipes to review.</p>
          ) : (
            <div className="grid gap-4">
              {recipes?.map((recipe) => (
                <Card key={recipe.id}>
                  <CardHeader>
                    <CardTitle>{recipe.title}</CardTitle>
                    <CardDescription>{recipe.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Contributor: {recipe.contributor.name} ({recipe.contributor.email})
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Project: {recipe.project.name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Submitted: {new Date(recipe.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="default"
                            onClick={() => handleStatusUpdate(recipe.id, 'approved')}
                            disabled={loadingRecipeId === recipe.id}
                          >
                            {loadingRecipeId === recipe.id && recipe.status === 'approved' ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Approving...
                              </>
                            ) : (
                              'Approve'
                            )}
                          </Button>
                          <Button
                            variant="destructive"
                            onClick={() => handleRejectClick(recipe.id)}
                            disabled={loadingRecipeId === recipe.id}
                          >
                            {loadingRecipeId === recipe.id && recipe.status === 'rejected' ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Rejecting...
                              </>
                            ) : (
                              'Reject'
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={rejectionDialogOpen} onOpenChange={setRejectionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Recipe</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this recipe. This message will be sent to the contributor.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Enter rejection reason..."
              value={rejectionMessage}
              onChange={(e) => setRejectionMessage(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setRejectionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleRejectConfirm}
              disabled={!rejectionMessage.trim()}
            >
              Confirm Rejection
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 