import { InviteForm } from "@/components/collaborate/invite-form";
import { ContributorsList } from "@/components/collaborate/contributors-list";
import { ProgressTracker } from "@/components/collaborate/progress-tracker";

export default function Collaborate() {
  return (
    <section className="py-12 bg-muted/50">
      <div className="container mx-auto px-4">
        <h1 className="font-serif text-4xl md:text-5xl font-bold mb-2">Collaborate</h1>
        <p className="text-lg text-muted-foreground mb-8">Invite family members to contribute their recipes</p>
        
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
          <div className="lg:col-span-3">
            <InviteForm />
          </div>
          
          <div className="lg:col-span-2">
            <ContributorsList />
            <ProgressTracker />
          </div>
        </div>
      </div>
    </section>
  );
}
