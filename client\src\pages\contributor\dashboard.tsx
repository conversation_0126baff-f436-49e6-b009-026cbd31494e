import { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { API_URL } from '@/lib/constants';
import { Button } from "@/components/ui/button";
import { Link } from "wouter";

interface Project {
  id: number;
  name: string;
  description: string;
  status: string;
  createdAt: string;
}

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  projectId: number;
  project?: {
    id: number;
    name: string;
    description: string;
  };
}

export default function ContributorDashboard() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Starting to fetch data...');
        
        // Fetch projects
        console.log('Fetching projects...');
        const projectsResponse = await fetch(`${API_URL}/contributor/all-projects`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        });

        if (!projectsResponse.ok) {
          throw new Error("Failed to fetch projects");
        }

        const projectsData = await projectsResponse.json();
        console.log('Projects response:', projectsData);
        setProjects(projectsData.projects || []);

        // Fetch recipes
        console.log('Fetching recipes...');
        const recipesResponse = await fetch(`${API_URL}/contributor/recipes`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        });

        if (!recipesResponse.ok) {
          throw new Error("Failed to fetch recipes");
        }

        const recipesData = await recipesResponse.json();
        console.log('Recipes response:', recipesData);
        // Handle both array and object with recipes property
        const recipesArray = Array.isArray(recipesData) ? recipesData : (recipesData.recipes || []);
        console.log('Setting recipes array:', recipesArray);
        setRecipes(recipesArray);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load your data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Add effect to log state changes
  useEffect(() => {
    console.log('Projects state updated:', projects);
  }, [projects]);

  useEffect(() => {
    console.log('Recipes state updated:', recipes);
  }, [recipes]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Welcome, {user?.name}</h1>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Your Projects</CardTitle>
            <CardDescription>
              View and manage your recipe book contributions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {projects?.length === 0 ? (
              <p className="text-muted-foreground">You haven't joined any projects yet.</p>
            ) : (
              <div className="grid gap-4">
                {projects.map((project) => (
                  <Card key={project.id}>
                    <CardHeader>
                      <CardTitle>{project.name}</CardTitle>
                      <CardDescription>{project.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Status: {project.status}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          Joined: {new Date(project.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Your Recipe Submissions</CardTitle>
                <CardDescription>
                  Track the status of your submitted recipes
                </CardDescription>
              </div>
              <Link href="/recipes/create">
                <Button>Add New Recipe</Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            {!recipes || recipes.length === 0 ? (
              <p className="text-muted-foreground">You haven't submitted any recipes yet.</p>
            ) : (
              <div className="grid gap-4">
                {recipes.map((recipe) => (
                  <Card key={recipe.id}>
                    <CardHeader>
                      <CardTitle>{recipe.title}</CardTitle>
                      <CardDescription>{recipe.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex justify-between items-center">
                        <div>
                          <span className={`text-sm ${
                            recipe.status === 'approved' ? 'text-green-600' :
                            recipe.status === 'rejected' ? 'text-red-600' :
                            'text-yellow-600'
                          }`}>
                            Status: {recipe.status.charAt(0).toUpperCase() + recipe.status.slice(1)}
                          </span>
                          <p className="text-sm text-muted-foreground">
                            Project: {recipe.project?.name || 'Unknown Project'}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Submitted: {new Date(recipe.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <Link href={`/recipes/${recipe.id}/edit`}>
                          <Button variant="outline">Edit</Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 