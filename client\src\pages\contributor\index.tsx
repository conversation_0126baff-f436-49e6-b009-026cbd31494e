import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { DirectRecipeForm } from "@/components/recipes/direct-recipe-form";
import { ProjectList } from "@/components/projects/project-list";
import { useLocation } from "wouter";
import { Loader2 } from "lucide-react";
import { API_URL, PricingModel, PricingTier } from '@/lib/constants';

interface Project {
  id: number;
  name: string;
  description: string;
  status: string;
  createdAt: string;
  pricingTier?: string;
  maxRecipes?: number;
  recipes?: any[];
}

export default function ContributorDashboard() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();
  const [, navigate] = useLocation();

  useEffect(() => {
    if (!user) {
      navigate("/login");
      return;
    }

    const fetchProjects = async () => {
      try {
        const token = localStorage.getItem("token");
        const response = await fetch(`${API_URL}/contributor/all-projects`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch projects");
        }

        const data = await response.json();
        const projectsData = data.projects || [];

        // Fetch recipes for each project
        const projectsWithRecipes = await Promise.all(
          projectsData.map(async (project: any) => {
            try {
              const recipesResponse = await fetch(`${API_URL}/organizer/projects/${project.id}/recipes`, {
                headers: {
                  'Authorization': `Bearer ${token}`
                }
              });

              if (recipesResponse.ok) {
                const recipesData = await recipesResponse.json();
                return {
                  ...project,
                  recipes: recipesData.recipes || []
                };
              }
              return project;
            } catch (error) {
              console.error(`Error fetching recipes for project ${project.id}:`, error);
              return project;
            }
          })
        );

        console.log('Projects with recipes:', projectsWithRecipes);
        setProjects(projectsWithRecipes);
      } catch (error) {
        console.error("Error fetching projects:", error);
        toast({
          title: "Error",
          description: "Failed to load your projects. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjects();
  }, [toast, navigate, user]);

  if (!user) {
    return null;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Contributor Dashboard</h1>
        <div className="text-sm text-muted-foreground">
          Welcome, {user.name}
        </div>
      </div>

      <Tabs defaultValue="projects" className="space-y-4">
        <TabsList>
          <TabsTrigger value="projects">My Projects</TabsTrigger>
          <TabsTrigger value="submit">Submit Recipe</TabsTrigger>
        </TabsList>

        <TabsContent value="projects">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : projects.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <p className="text-muted-foreground">
                  You haven't been invited to any recipe books yet, or your invitations haven't been accepted.
                </p>
              </CardContent>
            </Card>
          ) : (
            <ProjectList projects={projects} />
          )}
        </TabsContent>

        <TabsContent value="submit">
          {projects.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <p className="text-muted-foreground">
                  You need to be invited to a recipe book and accept the invitation before you can submit recipes.
                </p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Submit a Recipe</CardTitle>
                <CardDescription>
                  Choose a project and fill out the recipe details below.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DirectRecipeForm
                  projects={projects}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}