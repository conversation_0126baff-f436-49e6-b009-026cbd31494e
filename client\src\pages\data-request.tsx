import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";

const formSchema = z.object({
  requestType: z.enum([
    "access",
    "rectification",
    "erasure",
    "restrict",
    "portability",
    "object",
  ]),
  email: z.string().email(),
  details: z.string().min(10, "Please provide more details about your request"),
});

type FormValues = z.infer<typeof formSchema>;

export function DataRequestPage() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: user?.email || "",
      details: "",
      requestType: "access",
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      // Create email body with formatted content
      const emailBody = `
Request Type: ${data.requestType}
User Email: ${data.email}
Details: ${data.details}
Timestamp: ${new Date().toISOString()}
      `.trim();

      // Create mailto URL with encoded subject and body
      const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(`Data Protection Request: ${data.requestType}`)}&body=${encodeURIComponent(emailBody)}`;

      // Open default email client
      window.location.href = mailtoUrl;

      toast({
        title: "Email Client Opened",
        description: "Your default email client has been opened with the request details. Please send the email to submit your request.",
      });

      form.reset();
    } catch (error) {
      console.error('Error opening email client:', error);
      toast({
        title: "Error",
        description: "Failed to open email client. Please try again or contact us <NAME_EMAIL>",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <div className="space-y-6">
        <div>
          <h1 className="font-serif text-4xl font-bold mb-4">Data Protection Request</h1>
          <p className="text-muted-foreground">
            Submit a request regarding your personal data. We'll process your request according to applicable data protection laws.
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="requestType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Request Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a request type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="access">Access My Data</SelectItem>
                      <SelectItem value="rectification">Correct My Data</SelectItem>
                      <SelectItem value="erasure">Delete My Data</SelectItem>
                      <SelectItem value="restrict">Restrict Processing</SelectItem>
                      <SelectItem value="portability">Data Portability</SelectItem>
                      <SelectItem value="object">Object to Processing</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Select the type of request you'd like to make regarding your personal data.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email Address</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>
                    We'll use this email to respond to your request.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="details"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Request Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Please provide any additional details about your request..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The more details you provide, the better we can assist you.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit">
              Submit Data Protection Request
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}
