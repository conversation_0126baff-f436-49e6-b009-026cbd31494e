import { ColorPalette } from "@/components/design-elements/color-palette";
import { Typography } from "@/components/design-elements/typography";
import { Buttons } from "@/components/design-elements/buttons";
import { FormElements } from "@/components/design-elements/form-elements";
import { Cards } from "@/components/design-elements/cards";

export default function DesignElements() {
  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <h1 className="font-serif text-4xl md:text-5xl font-bold mb-8">Design Elements</h1>
        
        {/* COLOR PALETTE */}
        <ColorPalette />
        
        {/* TYPOGRAPHY */}
        <Typography />
        
        {/* BUTTONS */}
        <Buttons />
        
        {/* FORM ELEMENTS */}
        <FormElements />
        
        {/* CARDS */}
        <Cards />
      </div>
    </section>
  );
}
