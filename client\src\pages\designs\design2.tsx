import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Star, Clock, Tag, Camera, Users, Plus, ChevronRight, PenTool, Heart, Book } from "lucide-react";
import { mockImages } from "@/lib/utils";

export default function Design2() {
  const [activeTab, setActiveTab] = useState("light");
  
  return (
    <div className="min-h-screen bg-[#F8F7F4]">
      {/* Hero Section - Storyworth Style */}
      <section className="relative bg-gradient-to-b from-[#F8F7F4] to-white pt-24 pb-20">
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 left-20 w-64 h-64 bg-blue-100/35 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-64 h-64 bg-[#E6D5C4]/35 rounded-full blur-3xl"></div>
        </div>
        <div className="container mx-auto px-4 relative">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-5xl md:text-7xl font-serif font-bold text-[#2E4B7A] mb-6">
              Everyone has a recipe worth sharing
            </h1>
            <p className="text-xl text-[#9B7A5D] mb-8 font-medium">
              Preserve your family's culinary heritage through meaningful stories and beautiful books
            </p>
            <Button className="bg-[#9B7A5D] hover:bg-[#8B6A4D] text-white text-lg px-8 py-6 rounded-full shadow-lg hover:shadow-xl transition-all">
              Start Your Family's Story
            </Button>
          </div>
        </div>
      </section>

      {/* Featured By Section */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <p className="text-center text-[#9B7A5D] mb-6 font-medium">Featured in...</p>
          <div className="flex justify-center items-center space-x-12 opacity-70">
            <div className="h-8 w-32 bg-gray-200 rounded"></div>
            <div className="h-8 w-32 bg-gray-200 rounded"></div>
            <div className="h-8 w-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </section>

      {/* Our Books Section */}
      <section className="py-20 bg-[#2E4B7A]">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl font-serif font-bold text-white text-center mb-4">Our Recipe Books</h2>
            <p className="text-blue-100 text-center mb-12 max-w-2xl mx-auto">
              Beautiful, hardcover books filled with your family's recipes and stories
            </p>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-8">
              {[1, 2, 3, 4, 5].map((book) => (
                <div key={book} className="aspect-[3/4] bg-white rounded-lg shadow-lg transform hover:-translate-y-1 transition-transform">
                  <div className="w-full h-full bg-[#E6D5C4]/25 rounded-lg"></div>
                </div>
              ))}
            </div>
            <div className="text-center mt-12">
              <Button className="bg-[#9B7A5D] hover:bg-[#8B6A4D] text-white px-8 py-3 rounded-full">
                See Sample Books
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-serif font-bold text-[#2E4B7A] text-center mb-16">
            How It Works
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto">
            {[
              {
                number: "1",
                title: "Share Your Recipes",
                description: "Add your cherished family recipes and the stories behind them"
              },
              {
                number: "2",
                title: "Invite Family",
                description: "Let family members contribute their favorite recipes"
              },
              {
                number: "3",
                title: "Create Your Book",
                description: "Get a beautiful printed cookbook of your family's recipes"
              }
            ].map((step, index) => (
              <div key={index} className="text-center relative">
                <div className="w-20 h-20 bg-[#F8F7F4] rounded-full flex items-center justify-center mx-auto mb-6 relative">
                  <span className="text-3xl font-serif font-bold text-[#2E4B7A]">{step.number}</span>
                  <div className="absolute inset-0 bg-[#E6D5C4]/25 rounded-full transform rotate-45"></div>
                </div>
                <h3 className="text-2xl font-serif font-bold text-[#2E4B7A] mb-3">{step.title}</h3>
                <p className="text-[#9B7A5D] text-lg">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-[#F8F7F4]">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  icon: <Heart className="w-8 h-8" />,
                  title: "Made with Love",
                  description: "Create a lasting legacy of family recipes"
                },
                {
                  icon: <Book className="w-8 h-8" />,
                  title: "Beautiful Books",
                  description: "Professional quality, hardcover cookbooks"
                },
                {
                  icon: <Users className="w-8 h-8" />,
                  title: "Family Collaboration",
                  description: "Invite everyone to contribute their stories"
                }
              ].map((feature, index) => (
                <Card key={index} className="bg-white border-none shadow-md hover:shadow-xl transition-shadow">
                  <CardContent className="p-8">
                    <div className="w-16 h-16 bg-[#2E4B7A]/8 rounded-full flex items-center justify-center mb-6 text-[#2E4B7A]">
                      {feature.icon}
                    </div>
                    <h3 className="text-2xl font-serif font-bold text-[#2E4B7A] mb-3">{feature.title}</h3>
                    <p className="text-[#9B7A5D] text-lg">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-serif font-bold text-[#2E4B7A] text-center mb-16">
            Family Stories
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {[
              {
                quote: "We created a beautiful cookbook with recipes from three generations. It's now our family's most treasured possession.",
                author: "The Johnson Family"
              },
              {
                quote: "Every recipe has a story, and now we've captured them all. Our children will have these memories forever.",
                author: "The Martinez Family"
              }
            ].map((testimonial, index) => (
              <Card key={index} className="bg-[#F8F7F4] border-none shadow-md">
                <CardContent className="p-8">
                  <div className="text-[#9B7A5D] mb-2">
                    <Star className="w-6 h-6 fill-current" />
                  </div>
                  <p className="text-xl text-[#2E4B7A] italic mb-6">{testimonial.quote}</p>
                  <p className="text-[#9B7A5D] font-serif font-medium">{testimonial.author}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#2E4B7A]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-serif font-bold text-white mb-6">
            Your family's story starts here
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Begin preserving your family's recipes and stories today
          </p>
          <Button className="bg-[#9B7A5D] hover:bg-[#8B6A4D] text-white text-lg px-8 py-6 rounded-full shadow-lg hover:shadow-xl transition-all group">
            Start Your Collection
            <ChevronRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </section>
    </div>
  );
}