import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Package, Truck, CreditCard, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function OrderTracking() {
  const [userOrders, setUserOrders] = useState([]);
  const [selectedOrderTracking, setSelectedOrderTracking] = useState(null);
  const [trackingDetails, setTrackingDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Get user orders
  const handleGetOrders = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/blurb/orders', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get orders');
      }

      const result = await response.json();
      setUserOrders(result.orders);

      toast({
        title: "Orders Retrieved",
        description: `Found ${result.orders.length} orders.`,
      });

      console.log('User orders:', result.orders);

    } catch (error) {
      console.error('Error getting orders:', error);
      toast({
        title: "Get Orders Failed",
        description: error instanceof Error ? error.message : "Failed to get orders.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get detailed tracking for an order
  const handleGetTracking = async (orderId: number) => {
    try {
      const response = await fetch(`/api/blurb/order/${orderId}/tracking`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get tracking details');
      }

      const result = await response.json();
      setTrackingDetails(result);
      setSelectedOrderTracking(orderId);

      toast({
        title: "Tracking Details Retrieved",
        description: `Status: ${result.status}. ${result.trackingNumber ? `Tracking: ${result.trackingNumber}` : 'No tracking number yet.'}`,
      });

      console.log('Tracking details:', result);

    } catch (error) {
      console.error('Error getting tracking:', error);
      toast({
        title: "Tracking Failed",
        description: error instanceof Error ? error.message : "Failed to get tracking details.",
        variant: "destructive",
      });
    }
  };

  // Test Blurb API configuration
  const handleTestBlurbAPI = async () => {
    try {
      const response = await fetch('/api/blurb/test', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to test Blurb API');
      }

      const result = await response.json();

      toast({
        title: "Blurb API Test",
        description: `API configured: ${result.configured}. Environment: ${result.environment}`,
        variant: result.configured ? "default" : "destructive"
      });

    } catch (error) {
      console.error('Error testing Blurb API:', error);
      toast({
        title: "Blurb API Test Failed",
        description: error instanceof Error ? error.message : "Failed to test Blurb API.",
        variant: "destructive",
      });
    }
  };

  // Load orders on component mount
  useEffect(() => {
    handleGetOrders();
  }, []);

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Order Tracking</h1>
            <p className="text-gray-600 mt-2">Track your recipe book print orders</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleTestBlurbAPI}
              className="flex items-center gap-2"
            >
              <Package className="h-4 w-4" />
              Test API
            </Button>
            <Button
              onClick={handleGetOrders}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh Orders
            </Button>
          </div>
        </div>

        {userOrders.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Orders Found</h3>
              <p className="text-gray-600 mb-4">You haven't placed any print orders yet.</p>
              <Button onClick={() => window.location.href = '/recipe-books'}>
                Create Recipe Book
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Your Orders ({userOrders.length})</h3>
            
            {userOrders.map((order: any) => (
              <Card key={order.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>Order #{order.id}</CardTitle>
                      <p className="text-sm text-gray-600">Blurb Order: {order.blurbOrderId}</p>
                      <p className="text-sm text-gray-600">Status: <span className="font-medium capitalize">{order.status}</span></p>
                      <p className="text-sm text-gray-600">Created: {new Date(order.createdAt).toLocaleDateString()}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleGetTracking(order.id)}
                      className="flex items-center gap-1"
                    >
                      <Package className="h-4 w-4" />
                      Track Order
                    </Button>
                  </div>
                </CardHeader>
                
                {selectedOrderTracking === order.id && trackingDetails && (
                  <CardContent>
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <h5 className="font-semibold mb-3">Tracking Details</h5>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <p><strong>Status:</strong> {trackingDetails.status}</p>
                          <p><strong>Order Date:</strong> {new Date(trackingDetails.orderDate).toLocaleDateString()}</p>
                          {trackingDetails.trackingNumber && (
                            <p><strong>Tracking Number:</strong> {trackingDetails.trackingNumber}</p>
                          )}
                          {trackingDetails.carrier && (
                            <p><strong>Carrier:</strong> {trackingDetails.carrier}</p>
                          )}
                        </div>
                        <div>
                          {trackingDetails.estimatedDelivery && (
                            <p><strong>Estimated Delivery:</strong> {new Date(trackingDetails.estimatedDelivery).toLocaleDateString()}</p>
                          )}
                          <p><strong>Shipping To:</strong></p>
                          <div className="text-sm text-gray-600">
                            <p>{trackingDetails.shippingAddress.name}</p>
                            <p>{trackingDetails.shippingAddress.street}</p>
                            <p>{trackingDetails.shippingAddress.city}, {trackingDetails.shippingAddress.state} {trackingDetails.shippingAddress.zipCode}</p>
                          </div>
                        </div>
                      </div>
                      
                      {trackingDetails.trackingHistory && trackingDetails.trackingHistory.length > 0 && (
                        <div>
                          <h6 className="font-semibold mb-2">Tracking History</h6>
                          <div className="space-y-2">
                            {trackingDetails.trackingHistory.map((event: any, idx: number) => (
                              <div key={idx} className="flex gap-3 p-2 bg-white rounded border-l-4 border-blue-500">
                                <div className="flex-shrink-0 w-20 text-sm text-gray-500">
                                  {new Date(event.date).toLocaleDateString()}
                                </div>
                                <div className="flex-1">
                                  <p className="font-medium">{event.status}</p>
                                  <p className="text-sm text-gray-600">{event.description}</p>
                                  {event.location && (
                                    <p className="text-xs text-gray-500">📍 {event.location}</p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
