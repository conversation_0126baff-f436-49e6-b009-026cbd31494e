import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { User<PERSON><PERSON>, PricingTier, PricingModel } from "@/lib/constants";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { ProtectedRoute } from "@/components/auth/protected-route";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialog<PERSON>eader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { API_URL } from '@/lib/constants';
import { PricingCalculator } from "@/components/pricing/pricing-calculator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Project {
  id: number;
  name: string;
  description: string;
  status: string;
  pricingTier?: string;
  maxContributors?: number;
  createdAt: string;
  contributors: Array<{
    id: number;
    name: string;
    email: string;
    status: 'pending' | 'accepted' | 'rejected';
  }>;
}

export default function OrganizerDashboard() {
  const { user, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteDeadline, setInviteDeadline] = useState('');
  const [inviteReminderFrequency, setInviteReminderFrequency] = useState('weekly');
  const [isLoading, setIsLoading] = useState(false);
  const [queryVersion, setQueryVersion] = useState(0);

  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    pricingTier: PricingTier.SMALL,
    maxContributors: PricingModel[PricingTier.SMALL].maxContributors
  });

  const [pricingInfo, setPricingInfo] = useState({
    tier: PricingTier.SMALL,
    basePrice: PricingModel[PricingTier.SMALL].basePrice,
    pagePrice: 0,
    totalPrice: PricingModel[PricingTier.SMALL].basePrice,
    estimatedPages: 20
  });

  // Fetch projects based on view mode
  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ["recipeBooks", user?.role],
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const endpoint = `${API_URL}/organizer/my-projects`;

      const response = await fetch(endpoint, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe books');
      }

      const data = await response.json();
      return data.projects || [];
    },
    enabled: !!user && user.role === 'organizer'
  });

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newProject.name.trim() || !newProject.description.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/organizer/projects`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: newProject.name,
          description: newProject.description,
          pricingTier: newProject.pricingTier,
          maxContributors: newProject.maxContributors
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create recipe book');
      }

      // Invalidate the query to force a refresh
      queryClient.invalidateQueries({ queryKey: ["recipeBooks", user?.role] });
      setIsCreateDialogOpen(false);
      setNewProject({
        name: '',
        description: '',
        pricingTier: PricingTier.SMALL,
        maxContributors: PricingModel[PricingTier.SMALL].maxContributors
      });
      toast({
        title: "Success",
        description: "Recipe book created successfully",
      });
    } catch (error) {
      console.error('Error creating recipe book:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create recipe book",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteContributor = async (project: Project) => {
    // Check if we've reached the maximum number of contributors for this tier
    const currentContributorCount = project.contributors?.length || 0;
    const maxContributors = project.maxContributors ||
      (project.pricingTier ? PricingModel[project.pricingTier as keyof typeof PricingModel]?.maxContributors : 10);

    if (currentContributorCount >= maxContributors) {
      toast({
        title: "Maximum Contributors Reached",
        description: `You have reached the maximum of ${maxContributors} contributors for the ${project.pricingTier || 'current'} tier. Please upgrade to a higher tier to add more contributors.`,
        variant: "destructive",
      });
      return;
    }

    setSelectedProject(project);
    setIsInviteDialogOpen(true);
  };

  const handleSendInvite = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedProject) return;

    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/organizer/projects/${selectedProject.id}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          email: inviteEmail,
          deadline: inviteDeadline ? new Date(inviteDeadline).toISOString() : undefined,
          reminderFrequency: inviteReminderFrequency
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to invite contributor');
      }

      // Invalidate the query to force a refresh
      queryClient.invalidateQueries({ queryKey: ["recipeBooks", user?.role] });
      setIsInviteDialogOpen(false);
      setInviteEmail('');
      setInviteDeadline('');
      setInviteReminderFrequency('weekly');
      toast({
        title: "Success",
        description: "Invitation sent successfully",
      });
    } catch (error) {
      console.error('Error inviting contributor:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteProject = async (projectId: number) => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/organizer/projects/${projectId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete project');
      }

      // Invalidate the query to force a refresh
      queryClient.invalidateQueries({ queryKey: ["recipeBooks", user?.role] });
      toast({
        title: "Success",
        description: "Project deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting project:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete project",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <div>Please log in to access the organizer dashboard.</div>;
  }

  return (
    <ProtectedRoute allowedRoles={[UserRole.ORGANIZER]}>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Organizer Dashboard</h1>
          <div className="flex items-center gap-4">

            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>Create New Recipe Book</Button>
              </DialogTrigger>
              <DialogContent className="max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Create New Recipe Book</DialogTitle>
                  <DialogDescription>
                    Create a new recipe book and invite contributors to share their recipes.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleCreateProject} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Recipe Book Name</Label>
                    <Input
                      id="name"
                      value={newProject.name}
                      onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}
                      placeholder="Enter recipe book name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={newProject.description}
                      onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
                      placeholder="Describe your recipe book"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="pricingTier">Cookbook Size</Label>
                    <Select
                      value={newProject.pricingTier}
                      onValueChange={(value) => {
                        const tierData = PricingModel[value as keyof typeof PricingModel];
                        setNewProject({
                          ...newProject,
                          pricingTier: value,
                          maxContributors: tierData.maxContributors
                        });

                        // Update pricing info when tier changes
                        setPricingInfo(prevInfo => ({
                          ...prevInfo,
                          tier: value,
                          basePrice: tierData.basePrice,
                          totalPrice: tierData.basePrice + prevInfo.pagePrice
                        }));
                      }}
                    >
                      <SelectTrigger id="pricingTier">
                        <SelectValue placeholder="Select cookbook size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={PricingTier.SMALL}>
                          {PricingModel[PricingTier.SMALL].name} - {PricingModel[PricingTier.SMALL].description}
                        </SelectItem>
                        <SelectItem value={PricingTier.MEDIUM}>
                          {PricingModel[PricingTier.MEDIUM].name} - {PricingModel[PricingTier.MEDIUM].description}
                        </SelectItem>
                        <SelectItem value={PricingTier.LARGE}>
                          {PricingModel[PricingTier.LARGE].name} - {PricingModel[PricingTier.LARGE].description}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <PricingCalculator
                    key={newProject.pricingTier} // Add key to force re-render when tier changes
                    initialTier={newProject.pricingTier}
                    onPricingChange={setPricingInfo}
                    compact
                  />

                  <div className="pt-4 sticky bottom-0 bg-background">
                    <Button type="submit" disabled={isLoading} className="w-full">
                      {isLoading ? "Creating..." : "Create Recipe Book"}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {projectsLoading ? (
          <div className="text-center py-8">Loading recipe books...</div>
        ) : projects && projects.length > 0 ? (
          <div className="space-y-4">
            {projects.map((project) => (
              <Card key={project.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle>{project.name}</CardTitle>
                      <CardDescription>
                        Created on {new Date(project.createdAt).toLocaleDateString()}
                      </CardDescription>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => handleInviteContributor(project)}
                      >
                        Invite Contributor
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setLocation(`/recipe-books/${project.id}`)}
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{project.description}</p>
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">
                        Contributors: {project.contributors?.length || 0}
                        {project.maxContributors && <span> / {project.maxContributors}</span>}
                      </p>
                      {project.pricingTier && (
                        <p className="text-sm text-gray-500">
                          Tier: {PricingModel[project.pricingTier as keyof typeof PricingModel]?.name || project.pricingTier}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        Status: {project.status}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No recipe books found. Create your first recipe book!
          </div>
        )}

        {/* Invite Contributor Dialog */}
        <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Invite Contributor</DialogTitle>
              <DialogDescription>
                Invite a contributor to join your project.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSendInvite} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="Enter contributor's email"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="deadline">Submission Deadline (Optional)</Label>
                <Input
                  id="deadline"
                  type="datetime-local"
                  value={inviteDeadline}
                  onChange={(e) => setInviteDeadline(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="reminderFrequency">Reminder Frequency</Label>
                <Select value={inviteReminderFrequency} onValueChange={setInviteReminderFrequency}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select reminder frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1min">Every 1 minute (Test)</SelectItem>
                    <SelectItem value="2min">Every 2 minutes (Test)</SelectItem>
                    <SelectItem value="5min">Every 5 minutes (Test)</SelectItem>
                    <SelectItem value="15min">Every 15 minutes (Test)</SelectItem>
                    <SelectItem value="30min">Every 30 minutes (Test)</SelectItem>
                    <SelectItem value="1hour">Every hour (Test)</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="biweekly">Bi-weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Sending..." : "Send Invitation"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </ProtectedRoute>
  );
}