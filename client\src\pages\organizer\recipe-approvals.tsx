import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useLocation } from "wouter";
import { CheckCircle, XCircle } from "lucide-react";
import { API_URL } from '@/lib/constants';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  contributor: {
    name: string;
  };
  createdAt: string;
  projectId: number;
}

interface Project {
  id: number;
  name: string;
  description: string;
}

export default function RecipeApprovals() {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();
  const [, setLocation] = useLocation();

  useEffect(() => {
    if (user && user.role !== 'organizer' && user.role !== 'admin') {
      setLocation("/");
      toast({
        title: "Access Denied",
        description: "Only organizers can access this page",
        variant: "destructive",
      });
      return;
    }

    const fetchData = async () => {
      try {
        // Fetch projects
        const projectResponse = await fetch(`${API_URL}/organizer/my-projects`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`
          }
        });

        if (!projectResponse.ok) throw new Error("Failed to fetch projects");
        const projectData = await projectResponse.json();
        setProjects(projectData.projects);

        // Fetch pending recipes for each project
        const pendingRecipes: Recipe[] = [];
        for (const project of projectData.projects) {
          const recipeResponse = await fetch(`${API_URL}/organizer/projects/${project.id}/pending-recipes`, {
            headers: {
              Authorization: `Bearer ${localStorage.getItem("token")}`
            }
          });
          if (recipeResponse.ok) {
            const recipeData = await recipeResponse.json();
            pendingRecipes.push(...recipeData);
          }
        }
        setRecipes(pendingRecipes);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load recipes. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.role === 'organizer' || user?.role === 'admin') {
      fetchData();
    }
  }, [toast, user, setLocation]);

  const handleApproval = async (recipeId: number, status: 'approved' | 'rejected') => {
    try {
      const response = await fetch(`${API_URL}/organizer/recipes/${recipeId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem("token")}`
        },
        body: JSON.stringify({ status })
      });

      if (!response.ok) throw new Error('Failed to update recipe status');

      // Update local state
      setRecipes(recipes.filter(r => r.id !== recipeId));
      
      toast({
        title: "Success",
        description: `Recipe ${status} successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update recipe status",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Recipe Approvals</h1>

      {recipes.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No recipes pending approval.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {recipes.map((recipe) => (
            <Card key={recipe.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-xl font-semibold">{recipe.title}</h3>
                      <Badge variant="outline">Pending</Badge>
                    </div>
                    <p className="text-muted-foreground mb-4">{recipe.description}</p>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>By: {recipe.contributor.name}</span>
                      <span>Submitted: {new Date(recipe.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => handleApproval(recipe.id, 'approved')}
                      className="bg-emerald-500 hover:bg-emerald-600"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Approve
                    </Button>
                    <Button
                      onClick={() => handleApproval(recipe.id, 'rejected')}
                      variant="destructive"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Reject
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
