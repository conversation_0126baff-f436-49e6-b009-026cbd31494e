import React, { useState } from 'react';
import { PricingCalculator } from '@/components/pricing/pricing-calculator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PricingTier, PricingModel, PAGE_COUNT_PER_RECIPE } from '@/lib/constants';

export function PricingCalculatorPage() {
  const [pricingInfo, setPricingInfo] = useState({
    tier: PricingTier.SMALL,
    basePrice: PricingModel[PricingTier.SMALL].basePrice,
    pagePrice: 0,
    totalPrice: PricingModel[PricingTier.SMALL].basePrice,
    estimatedPages: 20
  });

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-2">Pricing Calculator</h1>
        <p className="text-muted-foreground mb-8">
          Estimate the cost of your cookbook based on size and number of recipes
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2">
            <PricingCalculator
              initialTier={PricingTier.SMALL}
              onPricingChange={setPricingInfo}
            />
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>How Pricing Works</CardTitle>
                <CardDescription>
                  Understanding our pricing model
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  Our pricing is based on two factors:
                </p>
                <ul className="list-disc pl-5 space-y-2">
                  <li>
                    <strong>Base Price:</strong> Determined by the tier you select, which limits the maximum number of contributors
                  </li>
                  <li>
                    <strong>Page Price:</strong> Based on the estimated number of pages in your final cookbook
                  </li>
                </ul>
                <p className="text-sm text-muted-foreground">
                  Each recipe typically takes about {PAGE_COUNT_PER_RECIPE} pages in the final cookbook.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tier Comparison</CardTitle>
                <CardDescription>
                  Choose the right size for your cookbook
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(PricingModel).map(([tier, data]) => (
                    <div key={tier} className="p-3 border rounded-md">
                      <div className="font-medium">{data.name}</div>
                      <div className="text-sm text-muted-foreground">{data.description}</div>
                      <div className="mt-2 flex justify-between">
                        <span>Contributors:</span>
                        <span className="font-medium">Max {data.maxContributors}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Recipes:</span>
                        <span className="font-medium">{data.minRecipes}-{data.maxRecipes}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Base Price:</span>
                        <span className="font-medium">${data.basePrice.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Per Page:</span>
                        <span className="font-medium">${data.pricePerPage.toFixed(2)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
