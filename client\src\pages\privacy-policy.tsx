import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { <PERSON>, <PERSON><PERSON>, FileText, MessageSquare, Loader2 } from "lucide-react";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

export function PrivacyPolicyPage() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <div className="space-y-8">
        <div>
          <h1 className="font-serif text-4xl font-bold mb-4">Privacy Policy</h1>
          <p className="text-muted-foreground">Last updated: {new Date().toLocaleDateString()}</p>
        </div>

        <section className="space-y-4">
          <h2 className="font-serif text-2xl font-semibold">1. Introduction</h2>
          <p>
            This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our RecipeBook service. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the application.
          </p>
        </section>

        <section className="space-y-4">
          <h2 className="font-serif text-2xl font-semibold">2. Information We Collect</h2>
          <div className="space-y-2">
            <h3 className="font-semibold">Personal Data</h3>
            <ul className="list-disc pl-6 space-y-2">
              <li>Name and contact information</li>
              <li>Account credentials</li>
              <li>Recipe content and images you upload</li>
              <li>User preferences and settings</li>
            </ul>
          </div>
        </section>

        <section className="space-y-4">
          <h2 className="font-serif text-2xl font-semibold">3. How We Use Your Information</h2>
          <ul className="list-disc pl-6 space-y-2">
            <li>To provide and maintain our Service</li>
            <li>To notify you about changes to our Service</li>
            <li>To provide customer support</li>
            <li>To gather analysis or valuable information to improve our Service</li>
            <li>To detect, prevent and address technical issues</li>
          </ul>
        </section>

        <section className="space-y-4">
          <h2 className="font-serif text-2xl font-semibold">4. Your Data Protection Rights</h2>
          <p>Under GDPR and CCPA, you have the following rights:</p>
          <ul className="list-disc pl-6 space-y-2">
            <li>The right to access – You can request copies of your personal data.</li>
            <li>The right to rectification – You can request that we correct any information you believe is inaccurate.</li>
            <li>The right to erasure – You can request that we erase your personal data.</li>
            <li>The right to restrict processing – You can request that we restrict the processing of your personal data.</li>
            <li>The right to data portability – You can request that we transfer your data to another organization.</li>
            <li>The right to object – You can object to our processing of your personal data.</li>
          </ul>
        </section>

        <section className="space-y-4">
          <h2 className="font-serif text-2xl font-semibold">5. Cookies</h2>
          <p>
            We use cookies and similar tracking technologies to track activity on our Service and hold certain information. You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent.
          </p>
          <div className="flex gap-4">
            <Button asChild variant="outline">
              <Link href="/cookie-preferences" className="flex items-center gap-2">
                <Cookie className="h-4 w-4" />
                <span>Cookie Preferences</span>
              </Link>
            </Button>
          </div>
        </section>

        <section className="space-y-4">
          <h2 className="font-serif text-2xl font-semibold">6. Data Security</h2>
          <p>
            The security of your data is important to us. We implement appropriate technical and organizational measures to protect your personal information. However, no method of transmission over the Internet or electronic storage is 100% secure.
          </p>
        </section>

        <section className="space-y-4">
          <h2 className="font-serif text-2xl font-semibold">7. Contact Us</h2>
          <p>
            If you have any questions about this Privacy Policy, please contact us:
          </p>
          <ul className="list-disc pl-6">
            <li>By email: <EMAIL></li>
            <li>By visiting our data protection request page: <Link href="/data-request" className="text-primary hover:underline">Submit a Request</Link></li>
          </ul>
        </section>
      </div>
    </div>
  );
}

export function HelpCenterPage() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <div className="space-y-8">
        <div>
          <h1 className="font-serif text-4xl font-bold mb-4">Help Center</h1>
          <p className="text-muted-foreground">Find answers to common questions and get help with using RecipeBook</p>
        </div>

        {/* Quick Actions */}
        <section className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 border rounded-lg">
            <div className="flex items-center gap-3 mb-4">
              <MessageSquare className="h-6 w-6 text-primary" />
              <h3 className="font-semibold text-lg">Contact Support</h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Need personalized help? Our support team is here to assist you.
            </p>
            <Button asChild>
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>

          <div className="p-6 border rounded-lg">
            <div className="flex items-center gap-3 mb-4">
              <FileText className="h-6 w-6 text-primary" />
              <h3 className="font-semibold text-lg">Documentation</h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Browse our comprehensive guides and tutorials.
            </p>
            <Button variant="outline" asChild>
              <Link href="#getting-started">View Guides</Link>
            </Button>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="space-y-6" id="getting-started">
          <h2 className="font-serif text-2xl font-semibold">Frequently Asked Questions</h2>

          <div className="space-y-4">
            <details className="group border rounded-lg">
              <summary className="flex justify-between items-center cursor-pointer p-4 font-medium">
                How do I create a new recipe book?
                <span className="transition group-open:rotate-180">▼</span>
              </summary>
              <div className="px-4 pb-4 text-muted-foreground">
                <p>To create a new recipe book:</p>
                <ol className="list-decimal list-inside mt-2 space-y-1">
                  <li>Navigate to the Recipe Books page</li>
                  <li>Click the "Create New Book" button</li>
                  <li>Fill in your book details (name, description, theme)</li>
                  <li>Set the maximum number of contributors</li>
                  <li>Click "Create Book" to finish</li>
                </ol>
              </div>
            </details>

            <details className="group border rounded-lg">
              <summary className="flex justify-between items-center cursor-pointer p-4 font-medium">
                How do I invite contributors to my recipe book?
                <span className="transition group-open:rotate-180">▼</span>
              </summary>
              <div className="px-4 pb-4 text-muted-foreground">
                <p>To invite contributors:</p>
                <ol className="list-decimal list-inside mt-2 space-y-1">
                  <li>Open your recipe book</li>
                  <li>Go to the "Collaborate" section</li>
                  <li>Enter the contributor's email address</li>
                  <li>Set an optional deadline and reminder frequency</li>
                  <li>Click "Send Invitation"</li>
                </ol>
                <p className="mt-2">Contributors will receive an email with instructions to join your book.</p>
              </div>
            </details>

            <details className="group border rounded-lg">
              <summary className="flex justify-between items-center cursor-pointer p-4 font-medium">
                What submission methods are available for recipes?
                <span className="transition group-open:rotate-180">▼</span>
              </summary>
              <div className="px-4 pb-4 text-muted-foreground">
                <p>RecipeBook supports multiple submission methods:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li><strong>Manual Entry:</strong> Type recipes directly into the form</li>
                  <li><strong>Voice Recording:</strong> Record yourself reading the recipe aloud</li>
                  <li><strong>Photo Upload:</strong> Take pictures of handwritten or printed recipes</li>
                  <li><strong>OCR Scanning:</strong> Automatic text extraction from recipe images</li>
                </ul>
              </div>
            </details>

            <details className="group border rounded-lg">
              <summary className="flex justify-between items-center cursor-pointer p-4 font-medium">
                How does the recipe approval process work?
                <span className="transition group-open:rotate-180">▼</span>
              </summary>
              <div className="px-4 pb-4 text-muted-foreground">
                <p>The approval process ensures quality:</p>
                <ol className="list-decimal list-inside mt-2 space-y-1">
                  <li>Contributors submit recipes in "pending" status</li>
                  <li>Organizers review submissions in the approval dashboard</li>
                  <li>Organizers can approve, request changes, or reject recipes</li>
                  <li>Approved recipes appear in the final book</li>
                  <li>Contributors are notified of status changes</li>
                </ol>
              </div>
            </details>

            <details className="group border rounded-lg">
              <summary className="flex justify-between items-center cursor-pointer p-4 font-medium">
                How do I customize my recipe book's appearance?
                <span className="transition group-open:rotate-180">▼</span>
              </summary>
              <div className="px-4 pb-4 text-muted-foreground">
                <p>Customize your book's look:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li><strong>Themes:</strong> Choose from classic, modern, rustic, and elegant styles</li>
                  <li><strong>Fonts:</strong> Select from elegant, modern, classic, and playful typography</li>
                  <li><strong>Cover:</strong> Add custom titles, subtitles, and cover images</li>
                  <li><strong>Dedication:</strong> Include personal dedications and family quotes</li>
                </ul>
                <p className="mt-2">Access customization options from your recipe book's settings.</p>
              </div>
            </details>

            <details className="group border rounded-lg">
              <summary className="flex justify-between items-center cursor-pointer p-4 font-medium">
                How does print-on-demand work?
                <span className="transition group-open:rotate-180">▼</span>
              </summary>
              <div className="px-4 pb-4 text-muted-foreground">
                <p>Professional printing made easy:</p>
                <ol className="list-decimal list-inside mt-2 space-y-1">
                  <li>Complete your recipe book with all recipes approved</li>
                  <li>Preview your book to ensure everything looks perfect</li>
                  <li>Click "Order Print" to start the printing process</li>
                  <li>Your book is sent to our printing partner (Blurb)</li>
                  <li>Track your order status and shipping progress</li>
                  <li>Receive your professionally printed cookbook</li>
                </ol>
              </div>
            </details>

            <details className="group border rounded-lg">
              <summary className="flex justify-between items-center cursor-pointer p-4 font-medium">
                What are the pricing tiers and limits?
                <span className="transition group-open:rotate-180">▼</span>
              </summary>
              <div className="px-4 pb-4 text-muted-foreground">
                <p>Our pricing is based on book size:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li><strong>Small Family:</strong> Up to 5 contributors, 50 recipes - $29</li>
                  <li><strong>Extended Family:</strong> Up to 15 contributors, 150 recipes - $49</li>
                  <li><strong>Large Gathering:</strong> Up to 30 contributors, 300 recipes - $79</li>
                </ul>
                <p className="mt-2">All tiers include unlimited revisions and professional printing options.</p>
              </div>
            </details>
          </div>
        </section>

        {/* Contact Section */}
        <section className="bg-muted/50 p-6 rounded-lg">
          <h3 className="font-semibold text-lg mb-4">Still need help?</h3>
          <p className="text-muted-foreground mb-4">
            Can't find what you're looking for? Our support team is ready to help you with any questions.
          </p>
          <div className="flex gap-4">
            <Button asChild>
              <Link href="/contact">Contact Support</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/data-request">Data Protection Request</Link>
            </Button>
          </div>
        </section>
      </div>
    </div>
  );
}

function SupportTicketForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [subject, setSubject] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState('normal');
  const [category, setCategory] = useState('');
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!subject.trim() || !description.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const token = localStorage.getItem('token');

      if (!token) {
        toast({
          title: "Error",
          description: "Please log in to submit a support ticket",
          variant: "destructive",
        });
        return;
      }

      const response = await fetch('/api/support/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          subject: subject.trim(),
          description: description.trim(),
          priority,
          category: category || undefined
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Support Ticket Created",
          description: `Your support ticket has been created successfully. Ticket ID: ${data.ticket.id}`,
        });

        // Reset form
        setSubject('');
        setDescription('');
        setPriority('normal');
        setCategory('');
      } else {
        const error = await response.json();
        throw new Error(error.details || 'Failed to create support ticket');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create support ticket",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="subject">Subject *</Label>
          <Input
            id="subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            placeholder="Brief description of your issue"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="technical">Technical Issue</SelectItem>
              <SelectItem value="billing">Billing</SelectItem>
              <SelectItem value="general">General Question</SelectItem>
              <SelectItem value="feature_request">Feature Request</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="priority">Priority</Label>
        <Select value={priority} onValueChange={setPriority}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="low">Low</SelectItem>
            <SelectItem value="normal">Normal</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="urgent">Urgent</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Please provide detailed information about your issue or question"
          rows={5}
          required
        />
      </div>

      <Button type="submit" disabled={isSubmitting} className="w-full">
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Creating Ticket...
          </>
        ) : (
          'Submit Support Ticket'
        )}
      </Button>
    </form>
  );
}

export function ContactPage() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <div className="space-y-6">
        <div>
          <h1 className="font-serif text-4xl font-bold mb-4">Contact Support</h1>
          <p className="text-muted-foreground">
            Get in touch with our support team. We're here to help you with any questions or issues.
          </p>
        </div>

        {/* Contact Methods */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 border rounded-lg">
            <div className="flex items-center gap-3 mb-4">
              <MessageSquare className="h-6 w-6 text-primary" />
              <h3 className="font-semibold">Email Support</h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Send us an email and we'll get back to you within 24 hours.
            </p>
            <Button asChild className="w-full">
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </Button>
          </div>

          <div className="p-6 border rounded-lg">
            <div className="flex items-center gap-3 mb-4">
              <FileText className="h-6 w-6 text-primary" />
              <h3 className="font-semibold">Help Center</h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Browse our FAQ and documentation for quick answers.
            </p>
            <Button variant="outline" asChild className="w-full">
              <Link href="/help">Browse Help Center</Link>
            </Button>
          </div>
        </div>

        {/* Support Form */}
        <div className="p-6 border rounded-lg bg-muted/50">
          <h3 className="font-semibold mb-4">Submit a Support Request</h3>
          <p className="text-muted-foreground mb-4">
            Fill out the form below to create a support ticket. Our team will respond via email.
          </p>
          <SupportTicketForm />
        </div>

        {/* Emergency Contact */}
        <div className="p-4 border-l-4 border-orange-500 bg-orange-50">
          <h4 className="font-semibold text-orange-800">Urgent Issues</h4>
          <p className="text-orange-700 text-sm mt-1">
            For urgent technical issues or account problems, please email us <NAME_EMAIL> with "URGENT" in the subject line.
          </p>
        </div>
      </div>
    </div>
  );
}