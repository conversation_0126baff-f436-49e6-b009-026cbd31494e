import { ProjectCard, NewProjectCard } from "@/components/projects/project-card";

const projects = [
  {
    title: "Family Favorites",
    recipeCount: 12,
    lastUpdated: "2 days ago",
    status: "In Progress" as const,
    contributors: [
      { initials: "J<PERSON>", color: "bg-secondary" },
      { initials: "TS", color: "bg-primary" },
      { initials: "AM", color: "bg-muted" },
    ],
    imageIndex: 0,
    bgColor: "bg-primary",
  },
  {
    title: "Holiday Collection",
    recipeCount: 8,
    lastUpdated: "1 week ago",
    status: "Draft" as const,
    contributors: [
      { initials: "JD", color: "bg-secondary" },
      { initials: "GR", color: "bg-muted" },
    ],
    imageIndex: 1,
    bgColor: "bg-secondary",
  },
];

export default function Projects() {
  return (
    <section className="py-12 bg-muted/50">
      <div className="container mx-auto px-4">
        <h1 className="font-serif text-4xl md:text-5xl font-bold mb-2">Projects</h1>
        <p className="text-lg text-muted-foreground mb-8">Create and manage your cookbook projects</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project, index) => (
            <ProjectCard
              key={index}
              title={project.title}
              recipeCount={project.recipeCount}
              lastUpdated={project.lastUpdated}
              status={project.status}
              contributors={project.contributors}
              imageIndex={project.imageIndex}
              bgColor={project.bgColor}
            />
          ))}
          
          <NewProjectCard />
        </div>
      </div>
    </section>
  );
}
