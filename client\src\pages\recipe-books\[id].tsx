import { useEffect, useState, useMemo } from "react";
import { useLocation } from "wouter";
import { useParams } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { Loader2, Clock, Users, ChefHat, Tag, User, List, Utensils, ChevronDown, Pencil, Trash2, Crown, Shield, UserCircle2, Circle, Search, BookOpen } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { API_URL } from '@/lib/constants';
import { Input } from "@/components/ui/input";
import { RecipeComments } from "@/components/recipes/recipe-comments";
import { RecipeImages } from "@/components/recipes/recipe-images";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { MessageSquare, Info, ScrollText } from "lucide-react";
import { BookPreview } from "@/components/recipes/book-preview";
import { BookCustomizationOptions } from "@/components/recipes/book-customization";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Custom scrollbar styles
const customScrollbarStyles = `
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
  }
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
`;

// Add this new style for dropdown scrollbars
const dropdownScrollbarStyles = `
  .dropdown-scrollbar::-webkit-scrollbar {
    width: 8px;
    display: block;
  }
  .dropdown-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  .dropdown-scrollbar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
  .dropdown-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
`;

interface Contributor {
  id: number;
  name: string;
  email: string;
  status: string;
}

interface Recipe {
  id: number;
  title: string;
  description: string;
  images: string[];
  role: string;
  tags: string[];
  ingredients: { name: string; amount: number; unit: string }[];
  instructions: string[];
  createdAt: string;
  status: string;
  contributor: {
    id: number;
    name: string;
  };
  project: {
    id: number;
    organizerId: number;
  };
}

interface Project {
  organizer: any;
  id: number;
  name: string;
  description: string;
  status: string;
  role: string;
  createdAt: string;
  contributors: Contributor[];
}

export default function ProjectDetails() {
  const params = useParams();
  const [, navigate] = useLocation();
  const [project, setProject] = useState<Project | null>(null);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recipeToDelete, setRecipeToDelete] = useState<Recipe | null>(null);
  const [hoveredRecipe, setHoveredRecipe] = useState<number | null>(null);
  const [hoveredSection, setHoveredSection] = useState<'ingredients' | 'instructions' | null>(null);
  const [expandedRecipes, setExpandedRecipes] = useState<Record<number, { ingredients: boolean, instructions: boolean }>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [bookCustomization, setBookCustomization] = useState<BookCustomizationOptions>({
    theme: 'classic',
    font: 'elegant',
    chapterStyle: 'simple',
    cover: 'classic',
    coverTitle: 'Family Cookbook',
    coverSubtitle: 'Treasured Recipes',
    coverImage: '',
    useCustomCoverImage: false,
    dedication: 'To my family, who have always supported my culinary adventures.',
    familyQuotes: [
      'Cooking is like love. It should be entered into with abandon or not at all. - Harriet Van Horne',
      'The secret ingredient is always love. - Grandma'
    ],
    includeDedication: true,
    includeQuotes: true
  });
  const { toast } = useToast();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<Record<number, string>>({});

  // Load saved book customization from server and localStorage on component mount
  useEffect(() => {
    const loadBookCustomization = async () => {
      try {
        // First try to load from localStorage for immediate display
        const savedCustomization = localStorage.getItem('bookCustomization');
        if (savedCustomization) {
          try {
            const parsedCustomization = JSON.parse(savedCustomization);
            setBookCustomization(prev => ({ ...prev, ...parsedCustomization }));
          } catch (error) {
            console.error('Error parsing saved book customization:', error);
            // If there's an error parsing, we'll keep the default settings
          }
        }

        // Then try to load from server (more authoritative)
        const token = localStorage.getItem('token');
        if (!token) return;

        const response = await fetch(`${API_URL}/book-customization/projects/${params.id}/customization`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const serverCustomization = await response.json();

          // Log the received data for debugging
          console.log('Received book customization from server:', serverCustomization);

          // Ensure familyQuotes is an array
          if (serverCustomization.familyQuotes && !Array.isArray(serverCustomization.familyQuotes)) {
            try {
              serverCustomization.familyQuotes = JSON.parse(serverCustomization.familyQuotes);
            } catch (e) {
              console.error('Error parsing familyQuotes:', e);
              serverCustomization.familyQuotes = [];
            }
          }

          // Log the processed data
          console.log('Processed book customization:', serverCustomization);

          setBookCustomization(prev => ({ ...prev, ...serverCustomization }));
          // Update localStorage with server data
          localStorage.setItem('bookCustomization', JSON.stringify(serverCustomization));
        }
      } catch (error) {
        console.error('Error loading book customization from server:', error);
      }
    };

    loadBookCustomization();
  }, [params.id]);

  // Save current customization to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('bookCustomization', JSON.stringify(bookCustomization));
  }, [bookCustomization]);

  // Function to save book customization to server
  const saveBookCustomization = async (options: BookCustomizationOptions) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Make a copy of the options to avoid modifying the original
      const optionsToSave = { ...options };

      // Ensure familyQuotes is properly formatted
      if (optionsToSave.familyQuotes && Array.isArray(optionsToSave.familyQuotes)) {
      }

      await fetch(`${API_URL}/book-customization/projects/${params.id}/customization`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(optionsToSave)
      });

      toast({
        title: 'Success',
        description: 'Book customization saved successfully',
      });
    } catch (error) {
      console.error('Error saving book customization:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to save book customization',
      });
    }
  };

  // Add custom scrollbar styles to the document
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = customScrollbarStyles + dropdownScrollbarStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  useEffect(() => {
    if (!user) {
      navigate("/login");
      return;
    }

    const fetchProjectDetails = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) {
          throw new Error("No authentication token found");
        }

        let endpoint = '';
        // Use different endpoints based on user role
        if (user.role === 'organizer') {
          endpoint = `${API_URL}/organizer/all-projects`;
        } else if (user.role === 'contributor') {
          endpoint = `${API_URL}/contributor/projects/${params.id}`;
        } else if (user.role === 'admin') {
          // Use the new endpoint for admin users to get a single project
          endpoint = `${API_URL}/admin/projects/${params.id}`;
        } else {
          throw new Error("User role not supported");
        }

        const response = await fetch(endpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const errorData = await response.json();
            console.error('Error response:', errorData);
            throw new Error(errorData.message || "Failed to fetch project details");
          } else {
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();

        // Handle different response formats based on endpoint
        let foundProject;
        if (user.role === 'organizer') {
          // This endpoint returns a list of projects
          const projectId = params.id ? parseInt(params.id) : 0;
          foundProject = data.projects.find((p: Project) => p.id === projectId);
        } else if (user.role === 'contributor' || user.role === 'admin') {
          // These endpoints return a single project
          foundProject = data.project;
        }

        if (foundProject) {
          setProject(foundProject);
        } else {
          throw new Error("Project not found");
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load project details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    const fetchRecipes = async () => {
      try {
        console.log('Fetching recipes for project:', params.id);
        const token = localStorage.getItem("token");
        if (!token) {
          throw new Error("No authentication token found");
        }

        let endpoint = '';
        // Use different endpoints based on user role
        if (user.role === 'organizer') {
          endpoint = `${API_URL}/organizer/projects/${params.id}/recipes`;
        } else if (user.role === 'contributor') {
          endpoint = `${API_URL}/contributor/projects/${params.id}/recipes`;
        } else if (user.role === 'admin') {
          endpoint = `${API_URL}/admin/projects/${params.id}/recipes`;
        } else {
          throw new Error("User role not supported");
        }

        console.log('Making request to:', endpoint);
        const response = await fetch(endpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const errorData = await response.json();
            console.error('Error response:', errorData);
            throw new Error(errorData.message || "Failed to fetch recipes");
          } else {
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();
        console.log('Received recipes data:', data);
        console.log('First recipe:', data.recipes[0]);
        if (!data.recipes) {
          console.error('No recipes array in response:', data);
          throw new Error('Invalid response format: missing recipes array');
        }
        // Add debug logging for recipe statuses
        console.log('All recipe statuses:', data.recipes.map((r: Recipe) => ({ id: r.id, status: r.status })));
        // Filter recipes to only show approved ones
        const approvedRecipes = data.recipes.filter((recipe: Recipe) => {
          console.log(`Recipe ${recipe.id} status:`, recipe.status);
          return recipe.status === 'approved';
        });
        console.log('Filtered approved recipes:', approvedRecipes);
        setRecipes(approvedRecipes);
      } catch (error) {
        console.error("Error fetching recipes:", error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load recipes. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchProjectDetails();
    fetchRecipes();
  }, [toast, navigate, user, params.id]);

  const handleEditRecipe = (recipe: Recipe) => {
    navigate(`/recipes/${recipe.id}/edit`);
  };

  const handleDeleteRecipe = async (recipe: Recipe) => {
    try {
      const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to delete recipe");
        } else {
          const text = await response.text();
          console.error('Non-JSON error response:', text);
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }

      // Only update the UI if the deletion was successful
      setRecipes(recipes.filter(r => r.id !== recipe.id));
      toast({
        title: "Success",
        description: "Recipe deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting recipe:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete recipe. Please try again.",
        variant: "destructive",
      });
    }
  };

  const canEditRecipe = (recipe: Recipe) => {
    if (!project || !user) return false;
    // Check if user is creator, organizer, or admin
    return (
      recipe.contributor.id === user.id ||
      project.organizer.id === user.id ||
      user.role === 'admin'
    );
  };

  // Add filtering logic
  const filteredRecipes = useMemo(() => {
    if (!searchQuery) return recipes;

    const searchLower = searchQuery.toLowerCase();
    return recipes.filter(recipe =>
      recipe.title.toLowerCase().includes(searchLower) ||
      recipe.description.toLowerCase().includes(searchLower) ||
      recipe.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
      recipe.contributor.name.toLowerCase().includes(searchLower)
    );
  }, [recipes, searchQuery]);

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">
              Project not found.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">{project.name}</h1>
        <div className="flex gap-2">
          {user?.role === 'admin' && (
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={async () => {
                try {
                  console.log('Making cleanup request...');
                  const response = await fetch(`${API_URL}/notifications/cleanup`, {  // Removed /api/ prefix
                    method: 'POST',
                    headers: {
                      Authorization: `Bearer ${localStorage.getItem("token")}`,
                      'Content-Type': 'application/json'
                    },
                  });

                  if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response:', text);
                    throw new Error(`Server error: ${response.status} ${response.statusText}`);
                  }

                  const data = await response.json();
                  console.log('Cleanup response:', data);
                  toast({
                    title: "Cleanup Complete",
                    description: `Deleted ${data.stats.deleted} notifications`,
                  });
                } catch (error) {
                  console.error('Cleanup error:', error);
                  toast({
                    title: "Error",
                    description: error instanceof Error ? error.message : "Failed to cleanup notifications",
                    variant: "destructive",
                  });
                }
              }}
            >
              <Trash2 className="h-4 w-4" />
              Cleanup Notifications
            </Button>
          )}
          {recipes.length > 0 && (
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => setPreviewOpen(true)}
            >
              <BookOpen className="h-4 w-4" />
              Book Preview
            </Button>
          )}
          <Button variant="outline" onClick={() => navigate("/recipe-books")}>
            Back to Recipe Books
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder="Search recipes by name, description, tags, or contributor..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Project Details</CardTitle>
            <CardDescription>
              Created on {new Date(project.createdAt).toLocaleDateString()}
              <span className="text-sm text-muted-foreground"> by {project.organizer?.name}</span>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">{project.description}</p>
            <div className="flex items-center gap-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Status:</span>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  project.status === 'active' ? 'bg-green-100 text-green-800' :
                  project.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {project.status}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Role:</span>
                <span className={`px-2 py-1 rounded-full text-xs flex items-center gap-1 ${
                  project.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                  project.role === 'organizer' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {project.role === 'admin' ? <Crown className="h-3 w-3" /> :
                   project.role === 'organizer' ? <Shield className="h-3 w-3" /> :
                   <UserCircle2 className="h-3 w-3" />}
                  <span className="capitalize">{project.role}</span>
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Contributors</CardTitle>
            <CardDescription>
              {project?.contributors?.length || 0} contributors
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {project?.contributors?.map((contributor) => (
                <div key={contributor.id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{contributor.name}</p>
                    <p className="text-sm text-muted-foreground">{contributor.email}</p>
                  </div>

                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-primary/5 to-primary/10">
            <CardTitle className="flex items-center gap-2">
              <ChefHat className="h-5 w-5 text-primary" />
              Recipes
            </CardTitle>
            <CardDescription>
              {recipes.length} recipes
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6">
            {filteredRecipes.length > 0 ? (
              <div className="grid gap-8 md:grid-cols-1 lg:grid-cols-2 max-w-5xl mx-auto">
                {filteredRecipes.map((recipe, index) => (
                  <motion.div
                    key={recipe.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="group relative bg-card rounded-lg border shadow-sm hover:shadow-md transition-all duration-300 max-w-xl mx-auto w-full"
                  >
                    <div className="p-6 min-h-[400px] flex flex-col">
                      <div className="flex items-start justify-between mb-4">
                        <div className="space-y-1">
                          <h3 className="text-lg font-semibold leading-none">
                            {recipe.title}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {recipe.description}
                          </p>
                          {recipe.images && recipe.images.length > 0 && (
                            <RecipeImages images={recipe.images} recipeTitle={recipe.title} />
                          )}
                          {
                            recipe.role === 'admin' ? (
                              <div className="flex flex-col items-start gap-2">
                                <span className="inline-flex items-center gap-1.5 px-2 py-1 mt-2 bg-primary/10 rounded-md text-xs font-medium text-primary">
                                  <Crown className="h-3.5 w-3.5" />
                                  Admin
                                </span>
                              </div>
                            ) : recipe.role === "organizer" ? (
                              <div className="flex flex-col items-start gap-2">
                                <span className="inline-flex items-center gap-1.5 px-2 py-1 mt-2 bg-blue-100 rounded-md text-xs font-medium text-blue-800">
                                  <Shield className="h-3.5 w-3.5" />
                                  Organizer
                                </span>
                              </div>
                            ) : (
                              <div className="flex flex-col items-start gap-2">
                                <span className="inline-flex items-center gap-1.5 px-2 py-1 mt-2 bg-gray-100 rounded-md text-xs font-medium text-gray-800">
                                  <Users className="h-3.5 w-3.5" />
                                  Contributor
                                </span>
                              </div>
                            )
                          }
                        </div>
                        <div className="flex flex-col items-end gap-2">
                          {canEditRecipe(recipe) && (
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => handleEditRecipe(recipe)}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-destructive hover:text-destructive"
                                onClick={() => {
                                  setRecipeToDelete(recipe);
                                  setDeleteDialogOpen(true);
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {recipe.tags?.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center gap-1 px-2 py-1 bg-muted/50 rounded-full text-xs text-muted-foreground hover:bg-muted transition-colors"
                          >
                            <Tag className="h-3 w-3" />
                            {tag}
                          </span>
                        ))}
                      </div>

                      <div className="flex items-center justify-end text-sm text-muted-foreground mb-4">
                        <span className="flex items-center gap-1">
                          <User className="h-4 w-4" />
                          {recipe.contributor.name}
                        </span>
                      </div>

                      {/* Tabbed interface for recipe content */}
                      <Tabs
                        defaultValue="recipe"
                        className="w-full mt-2"
                        value={activeTab[recipe.id] || 'recipe'}
                        onValueChange={(value) => setActiveTab(prev => ({ ...prev, [recipe.id]: value }))}
                      >
                        <TabsList className="w-full">
                          <TabsTrigger value="recipe" className="flex items-center gap-1.5 flex-1">
                            <Info className="h-4 w-4" />
                            <span>Recipe</span>
                          </TabsTrigger>
                          <TabsTrigger value="comments" className="flex items-center gap-1.5 flex-1">
                            <MessageSquare className="h-4 w-4" />
                            <span>Comments</span>
                          </TabsTrigger>
                        </TabsList>

                        {/* Recipe Content Tab */}
                        <TabsContent value="recipe" className="pt-4">
                          {/* Ingredients Section */}
                          <div
                            className="relative"
                            onMouseEnter={() => {
                              setHoveredRecipe(recipe.id);
                              setHoveredSection('ingredients');
                            }}
                            onMouseLeave={() => {
                              setHoveredRecipe(null);
                              setHoveredSection(null);
                            }}
                          >
                            <div className="w-full flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 transition-colors">
                              <div className="flex items-center gap-2">
                                <Utensils className="h-4 w-4 text-primary" />
                                <span className="text-sm font-medium">
                                  {recipe.ingredients.length} Ingredients
                                </span>
                              </div>
                              <ChevronDown
                                className={`h-4 w-4 text-muted-foreground transition-transform ${
                                  hoveredRecipe === recipe.id && hoveredSection === 'ingredients' ? 'rotate-180' : ''
                                }`}
                              />
                            </div>

                            {/* Hover-Triggered Ingredients Section */}
                            {hoveredRecipe === recipe.id && hoveredSection === 'ingredients' && (
                              <div className="mt-2 p-3 bg-muted/30 rounded-lg">
                                <ul className="space-y-2 max-h-[200px] overflow-y-auto">
                                  {recipe.ingredients.map((ingredient, idx) => (
                                    <li key={idx} className="text-sm flex items-center gap-2">
                                      <span className="w-1.5 h-1.5 rounded-full bg-primary/50 flex-shrink-0"></span>
                                      <span>{ingredient.amount} {ingredient.unit} {ingredient.name}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>

                          {/* Instructions Section */}
                          <div
                            className="relative mt-2"
                            onMouseEnter={() => {
                              setHoveredRecipe(recipe.id);
                              setHoveredSection('instructions');
                            }}
                            onMouseLeave={() => {
                              setHoveredRecipe(null);
                              setHoveredSection(null);
                            }}
                          >
                            <div className="w-full flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 transition-colors">
                              <div className="flex items-center gap-2">
                                <List className="h-4 w-4 text-primary" />
                                <span className="text-sm font-medium">
                                  {recipe.instructions.length} Steps
                                </span>
                              </div>
                              <ChevronDown
                                className={`h-4 w-4 text-muted-foreground transition-transform ${
                                  hoveredRecipe === recipe.id && hoveredSection === 'instructions' ? 'rotate-180' : ''
                                }`}
                              />
                            </div>

                            {/* Hover-Triggered Instructions Section */}
                            {hoveredRecipe === recipe.id && hoveredSection === 'instructions' && (
                              <div className="mt-2 p-3 bg-muted/30 rounded-lg">
                                <ol className="space-y-3 max-h-[200px] overflow-y-auto">
                                  {recipe.instructions.map((instruction, idx) => (
                                    <li key={idx} className="text-sm flex gap-2">
                                      <span className="flex-shrink-0 w-5 h-5 rounded-full bg-primary/10 text-primary text-xs flex items-center justify-center">
                                        {idx + 1}
                                      </span>
                                      <span className="flex-1">{instruction}</span>
                                    </li>
                                  ))}
                                </ol>
                              </div>
                            )}
                          </div>
                        </TabsContent>

                        {/* Comments Tab */}
                        <TabsContent value="comments" className="pt-4">
                          <RecipeComments recipeId={recipe.id} hideTitle={true} />
                        </TabsContent>


                      </Tabs>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <ChefHat className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" />
                <p className="text-muted-foreground">
                  No recipes added yet.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the recipe.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (recipeToDelete) {
                  handleDeleteRecipe(recipeToDelete);
                  setDeleteDialogOpen(false);
                  setRecipeToDelete(null);
                }
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Book Preview Component */}
      <BookPreview
        recipes={recipes}
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        customizationOptions={bookCustomization}
        onCustomizationChange={(options) => {
          setBookCustomization(options);
          // Save to localStorage for persistence
          localStorage.setItem('bookCustomization', JSON.stringify(options));
          // Save to server
          saveBookCustomization(options);
        }}
        showCustomization={true}
      />
    </div>
  );
}