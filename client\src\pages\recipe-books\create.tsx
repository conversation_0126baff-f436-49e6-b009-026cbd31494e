import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { API_URL, PricingTier, PricingModel } from '@/lib/constants';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { PricingCalculator } from "@/components/pricing/pricing-calculator";

export default function CreateRecipeBook() {
  const [, setLocation] = useLocation();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [pricingTier, setPricingTier] = useState(PricingTier.SMALL);
  const [maxContributors, setMaxContributors] = useState(PricingModel[PricingTier.SMALL].maxContributors);
  const [pricingInfo, setPricingInfo] = useState({
    tier: PricingTier.SMALL,
    basePrice: PricingModel[PricingTier.SMALL].basePrice,
    pagePrice: 0,
    totalPrice: PricingModel[PricingTier.SMALL].basePrice,
    estimatedPages: 20
  });
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Redirect contributors away from this page
  useEffect(() => {
    if (user?.role === 'contributor') {
      setLocation("/recipe-books");
      toast({
        variant: "destructive",
        title: "Access Denied",
        description: "Only organizers and admins can create recipe books.",
      });
    }
  }, [user, setLocation, toast]);

  // If user is a contributor, don't render the form
  if (user?.role === 'contributor') {
    return null;
  }

  const { mutate: createRecipeBook, isPending } = useMutation({
    mutationFn: async (data: {
      title: string;
      description: string;
      pricingTier: string;
      maxContributors: number;
    }) => {
      console.log('Starting recipe book creation with data:', data);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Only allow organizers and admins to create recipe books
      if (user?.role !== 'organizer' && user?.role !== 'admin') {
        throw new Error('Only organizers and admins can create recipe books');
      }

      const endpoint = `${API_URL}/organizer/projects`;

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: data.title,
          description: data.description,
          pricingTier: data.pricingTier,
          maxContributors: data.maxContributors,
        }),
      });

      console.log('Recipe book creation response status:', response.status);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create recipe book");
      }

      const result = await response.json();
      console.log('Recipe book creation response:', result);
      return result;
    },
    onSuccess: () => {
      console.log('Recipe book created successfully, redirecting...');
      // Invalidate the recipe books query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["recipeBooks"] });
      toast({
        title: "Success",
        description: "Recipe book created successfully",
      });
      setLocation("/recipe-books");
    },
    onError: (error: Error) => {
      console.error('Error creating recipe book:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createRecipeBook({
      title,
      description,
      pricingTier,
      maxContributors
    });
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">Create New Recipe Book</h1>

      <form onSubmit={handleSubmit} className="max-w-2xl">
        <div className="space-y-6">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
              placeholder="Enter recipe book title"
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              required
              placeholder="Enter recipe book description"
              rows={4}
            />
          </div>

          <div>
            <Label htmlFor="pricingTier">Cookbook Size</Label>
            <Select
              value={pricingTier}
              onValueChange={(value) => {
                setPricingTier(value);
                const tierData = PricingModel[value as keyof typeof PricingModel];
                setMaxContributors(tierData.maxContributors);

                // Update pricing info when tier changes
                setPricingInfo(prevInfo => ({
                  ...prevInfo,
                  tier: value,
                  basePrice: tierData.basePrice,
                  totalPrice: tierData.basePrice + prevInfo.pagePrice
                }));
              }}
            >
              <SelectTrigger id="pricingTier">
                <SelectValue placeholder="Select cookbook size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={PricingTier.SMALL}>
                  {PricingModel[PricingTier.SMALL].name} - {PricingModel[PricingTier.SMALL].description}
                </SelectItem>
                <SelectItem value={PricingTier.MEDIUM}>
                  {PricingModel[PricingTier.MEDIUM].name} - {PricingModel[PricingTier.MEDIUM].description}
                </SelectItem>
                <SelectItem value={PricingTier.LARGE}>
                  {PricingModel[PricingTier.LARGE].name} - {PricingModel[PricingTier.LARGE].description}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="border rounded-lg p-4 bg-card">
            <PricingCalculator
              key={pricingTier} // Add key to force re-render when tier changes
              initialTier={pricingTier}
              onPricingChange={setPricingInfo}
              compact
            />
          </div>

          <div className="flex gap-4">
            <Button type="submit" disabled={isPending}>
              {isPending ? "Creating..." : "Create Recipe Book"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setLocation("/recipe-books")}
            >
              Cancel
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}