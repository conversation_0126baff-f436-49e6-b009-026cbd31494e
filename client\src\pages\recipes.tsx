// import { RecipeCard, AddRecipeCard } from "@/components/recipes/recipe-card";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Search, Filter } from "lucide-react";

// const recipes = [
//   {
//     title: "Mediterranean Salad",
//     description: "A refreshing salad with cucumbers, tomatoes, olives, and feta cheese.",
//     tags: [
//       { label: "Vegetarian", variant: "primary" as const },
//       { label: "30 min", variant: "secondary" as const },
//       { label: "Salad", variant: "outline" as const },
//     ],
//     contributor: "Aunt Maria",
//     imageIndex: 0,
//     isSaved: false,
//   },
//   {
//     title: "Homemade Fettuccine",
//     description: "Classic pasta made from scratch with just flour and eggs, the way <PERSON> used to make it.",
//     tags: [
//       { label: "Vegetarian", variant: "primary" as const },
//       { label: "60 min", variant: "secondary" as const },
//       { label: "Pasta", variant: "outline" as const },
//     ],
//     contributor: "Grandma Rose",
//     imageIndex: 1,
//     isSaved: false,
//   },
//   {
//     title: "Classic Apple Pie",
//     description: "A traditional apple pie with a flaky crust and cinnamon-spiced filling.",
//     tags: [
//       { label: "Vegetarian", variant: "primary" as const },
//       { label: "90 min", variant: "secondary" as const },
//       { label: "Dessert", variant: "outline" as const },
//     ],
//     contributor: "Mom",
//     imageIndex: 2,
//     isSaved: true,
//   },
// ];

// const categories = ["All", "Main Dishes", "Desserts", "Vegetarian"];

// export default function Recipes() {
//   return (
//     <section className="py-12">
//       <div className="container mx-auto px-4">
//         <h1 className="font-serif text-4xl md:text-5xl font-bold mb-2">Recipes</h1>
//         <p className="text-lg text-muted-foreground mb-8">Browse and manage your recipe collection</p>
        
//         <div className="flex flex-wrap items-center mb-8 gap-4">
//           <div className="relative flex-grow max-w-md">
//             <Input 
//               type="text" 
//               placeholder="Search recipes..." 
//               className="pl-10" 
//             />
//             <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
//           </div>
          
//           <div className="flex items-center gap-2 flex-wrap">
//             {categories.map((category, index) => (
//               <Button
//                 key={index}
//                 variant={index === 0 ? "default" : "outline"}
//                 className={index === 0 ? "bg-primary/20 hover:bg-primary/30 text-primary" : ""}
//                 size="sm"
//               >
//                 {category}
//               </Button>
//             ))}
//             <Button variant="outline" size="sm">
//               <Filter className="h-4 w-4 mr-1" />
//               <span>More</span>
//             </Button>
//           </div>
//         </div>
        
//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
//           {recipes.map((recipe, index) => (
//             <RecipeCard
//               key={index}
//               title={recipe.title}
//               description={recipe.description}
//               tags={recipe.tags}
//               contributor={recipe.contributor}
//               imageIndex={recipe.imageIndex}
//               isSaved={recipe.isSaved}
//             />
//           ))}
          
//           <AddRecipeCard />
//         </div>
//       </div>
//     </section>
//   );
// }
