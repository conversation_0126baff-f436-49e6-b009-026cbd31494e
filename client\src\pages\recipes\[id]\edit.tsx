import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { useParams } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { Loader2, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ImageUpload } from "@/components/ui/image-upload";
import { API_URL } from '@/lib/constants';

interface Recipe {
  id: number;
  title: string;
  description: string;
  tags: string[];
  ingredients: { name: string; amount: number; unit: string }[];
  instructions: string[];
  contributor: {
    id: number;
    name: string;
  };
  project: {
    id: number;
    organizerId: number;
  };
  images: string[];
}

export default function EditRecipe() {
  const params = useParams();
  const [, navigate] = useLocation();
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    if (!user) {
      navigate("/login");
      return;
    }

    const fetchRecipe = async () => {
      try {
        const response = await fetch(`${API_URL}/organizer/recipes/${params.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        });

        if (!response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to fetch recipe");
          } else {
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();
        setRecipe(data.recipe);
      } catch (error) {
        console.error("Error fetching recipe:", error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load recipe. Please try again.",
          variant: "destructive",
        });
        navigate("/recipe-books");
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecipe();
  }, [toast, navigate, user, params.id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!recipe) return;

    setIsSaving(true);
    try {
      // The images array should already contain just the keys from the ImageUpload component
      console.log('Current recipe images:', recipe.images);

      const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          ...recipe,
          // Process images to ensure consistent format - server expects images WITHOUT recipes/ prefix
          images: recipe.images.map((url: string) => {
            // If it's already just a key (not a full URL), return as is
            if (!url.includes('http')) {
              // Remove recipes/ prefix if it exists (server will store without prefix)
              return url.replace('recipes/', '');
            }

            // Extract the path after the bucket name
            const bucketNameIndex = url.lastIndexOf('recipe-book-images-bucket');
            if (bucketNameIndex === -1) return url;

            const pathStart = url.indexOf('/', bucketNameIndex);
            // Remove recipes/ prefix from the extracted path
            const path = pathStart !== -1 ? url.substring(pathStart + 1) : url;
            return path.replace('recipes/', '');
          }),
        }),
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to update recipe");
        } else {
          const text = await response.text();
          console.error('Non-JSON error response:', text);
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }

      const result = await response.json();
      console.log('Recipe update response:', result);

      toast({
        title: "Success",
        description: "Recipe updated successfully",
      });
      navigate(`/recipe-books/${recipe.project.id}`);
    } catch (error) {
      console.error('Error updating recipe:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update recipe. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const removeIngredient = (index: number) => {
    if (!recipe) return;
    if (recipe.ingredients.length > 1) {
      const newIngredients = [...recipe.ingredients];
      newIngredients.splice(index, 1);
      setRecipe({ ...recipe, ingredients: newIngredients });
    } else {
      toast({
        title: "Cannot remove",
        description: "At least one ingredient is required",
        variant: "destructive",
      });
    }
  };

  const removeInstruction = (index: number) => {
    if (!recipe) return;
    if (recipe.instructions.length > 1) {
      const newInstructions = [...recipe.instructions];
      newInstructions.splice(index, 1);
      setRecipe({ ...recipe, instructions: newInstructions });
    } else {
      toast({
        title: "Cannot remove",
        description: "At least one instruction step is required",
        variant: "destructive",
      });
    }
  };

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  if (!recipe) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">
              Recipe not found.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check if user is authorized to edit
  const canEdit = user.id === recipe.contributor.id || user.id === recipe.project.organizerId;
  if (!canEdit) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">
              You are not authorized to edit this recipe.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Edit Recipe</h1>
        <Button variant="outline" onClick={() => navigate(`/recipe-books/${recipe.project.id}`)}>
          Back to Recipe Book
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Update the basic details of your recipe
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={recipe.title}
                onChange={(e) => setRecipe({ ...recipe, title: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={recipe.description}
                onChange={(e) => setRecipe({ ...recipe, description: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label>Recipe Images</Label>
              <ImageUpload
                onUpload={(urls) => {
                  console.log('New image URLs:', urls);
                  setRecipe({ ...recipe, images: urls });
                }}
                maxFiles={3}
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                initialFiles={recipe.images}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Ingredients</CardTitle>
            <CardDescription>
              List all ingredients needed for the recipe
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              {recipe.ingredients.map((ingredient, index) => (
                <div key={index} className="mb-2 relative">
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Amount"
                      className="w-24"
                      value={ingredient.amount}
                      onChange={(e) => {
                        const newIngredients = [...recipe.ingredients];
                        newIngredients[index] = {
                          ...newIngredients[index],
                          amount: parseFloat(e.target.value) || 0
                        };
                        setRecipe({ ...recipe, ingredients: newIngredients });
                      }}
                    />
                    <Input
                      placeholder="Unit"
                      className="w-24"
                      value={ingredient.unit}
                      onChange={(e) => {
                        const newIngredients = [...recipe.ingredients];
                        newIngredients[index] = {
                          ...newIngredients[index],
                          unit: e.target.value
                        };
                        setRecipe({ ...recipe, ingredients: newIngredients });
                      }}
                    />
                    <Input
                      placeholder="Ingredient name"
                      className="flex-1"
                      value={ingredient.name}
                      onChange={(e) => {
                        const newIngredients = [...recipe.ingredients];
                        newIngredients[index] = {
                          ...newIngredients[index],
                          name: e.target.value
                        };
                        setRecipe({ ...recipe, ingredients: newIngredients });
                      }}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="h-10 w-10 text-destructive"
                      onClick={() => removeIngredient(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setRecipe({
                    ...recipe,
                    ingredients: [...recipe.ingredients, { amount: 0, unit: "", name: "" }]
                  });
                }}
                className="mt-2"
              >
                Add Ingredient
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
            <CardDescription>
              Add step-by-step instructions for preparing the recipe
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {recipe.instructions.map((instruction, index) => (
              <div key={index} className="space-y-2 relative">
                <Label>Step {index + 1}</Label>
                <Textarea
                  value={instruction}
                  onChange={(e) => {
                    const newInstructions = [...recipe.instructions];
                    newInstructions[index] = e.target.value;
                    setRecipe({ ...recipe, instructions: newInstructions });
                  }}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-8 h-6 w-6 text-destructive"
                  onClick={() => removeInstruction(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setRecipe({
                  ...recipe,
                  instructions: [...recipe.instructions, '']
                });
              }}
            >
              Add Step
            </Button>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate(`/recipe-books/${recipe.project.id}`)}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}