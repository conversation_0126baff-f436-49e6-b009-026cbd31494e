import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { RecipeForm, type RecipeFormData } from "@/components/recipes/recipe-form";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Trash2, Edit, Scan, Loader2, Mic, HelpCircle, MessageSquare } from "lucide-react";
import { Link } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { API_URL } from '@/lib/constants';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ImageUpload } from "@/components/ui/image-upload";
import { AudioRecorder } from "@/components/ui/audio-recorder";

interface Recipe {
  id: number;
  projectId: number;
  title: string;
  description: string;
  ingredients: Array<{
    name: string;
    amount: number;
    unit: string;
  }>;
  instructions: string[];
  prepTime: number;
  cookTime: number;
  servings: number;
  difficulty: string;
  tags: string[];
  status: string;
  createdAt: string;
  updatedAt: string;
}

interface Project {
  id: number;
  name: string;
  organizerId: number;
}

export default function CreateRecipe() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreating, setIsCreating] = useState(false);
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string>("manual");
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [ocrLoading, setOcrLoading] = useState(false);
  const [ocrData, setOcrData] = useState<any>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [selectedAudio, setSelectedAudio] = useState<string>("");
  const [transcriptionLoading, setTranscriptionLoading] = useState(false);

  // Fetch recipe books
  const { data: projects, isLoading: isLoadingProjects } = useQuery<Project[]>({
    queryKey: ['projects', user?.role],
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Use different endpoints based on user role
      let endpoint = '';
      if (user?.role === 'organizer') {
        endpoint = `${API_URL}/organizer/my-projects`;
      } else if (user?.role === 'contributor') {
        endpoint = `${API_URL}/contributor/projects`;
      } else if (user?.role === 'admin') {
        endpoint = `${API_URL}/organizer/all-projects`;
      } else {
        throw new Error('User role not supported');
      }

      const response = await fetch(endpoint, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe books');
      }

      const data = await response.json();
      console.log('Fetched recipe books:', data.projects?.length || 0);
      return data.projects || [];
    },
    enabled: !!user?.role
  });

  // Log when projects data changes
  useEffect(() => {
    if (projects) {
      console.log('Current recipe books in state:', projects.length);
    }
  }, [projects]);

  // Fetch existing recipes
  const { data: recipes, isLoading: isLoadingRecipes } = useQuery<Recipe[]>({
    queryKey: ['recipes'],
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/contributor/recipes`, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch recipes');
      }

      const data = await response.json();
      return data.recipes || [];
    },
  });

  // Delete recipe mutation
  const deleteRecipeMutation = useMutation({
    mutationFn: async (recipeId: number) => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/organizer/recipes/${recipeId}`, {
        method: 'DELETE',
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to delete recipe");
        } else {
          const text = await response.text();
          console.error('Non-JSON error response:', text);
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recipes'] });
      toast({
        title: "Success",
        description: "Recipe deleted successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete recipe",
        variant: "destructive",
      });
    },
  });

  const isLoading = isLoadingProjects || isLoadingRecipes;

  // Handle OCR image upload
  const handleImagesUpload = (urls: string[]) => {
    setSelectedImages(urls);
  };

  // Add debugging for state changes
  useEffect(() => {
    console.log('selectedAudio changed:', selectedAudio);
    console.log('selectedProjectId:', selectedProjectId);
    console.log('Transcribe button should be enabled:', selectedAudio && selectedProjectId);
  }, [selectedAudio, selectedProjectId]);

  // Process OCR
  const processOcr = async () => {
    if (!selectedProjectId) {
      toast({
        title: "Error",
        description: "Please select a recipe book first",
        variant: "destructive",
      });
      return;
    }

    if (selectedImages.length === 0) {
      toast({
        title: "Error",
        description: "Please upload an image of a recipe first",
        variant: "destructive",
      });
      return;
    }

    setOcrLoading(true);
    try {
      const response = await fetch(`${API_URL}/contributor/recipes/ocr`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          imageKey: selectedImages[0],
          projectId: selectedProjectId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || "Failed to process image");
      }

      const data = await response.json();

      toast({
        title: "Success",
        description: "Recipe scanned and processed successfully!",
      });

      // Reset the form and refresh recipes
      setIsCreating(false);
      setSelectedImages([]);
      setOcrLoading(false);
      setOcrData(null);
      queryClient.invalidateQueries({ queryKey: ['recipes'] });
    } catch (error) {
      toast({
        title: "OCR Processing Failed",
        description: error instanceof Error ? error.message : "Failed to process recipe image",
        variant: "destructive",
      });
      setOcrLoading(false);
    }
  };

  // Handle audio upload
  const handleAudioUpload = (audioKey: string) => {
    console.log(`Setting selectedAudio state with key: ${audioKey}`);
    setSelectedAudio(audioKey);
    console.log(`Transcribe button should be enabled now if project is selected`);
  };

  // Process voice transcription
  const processTranscription = async () => {
    if (!selectedProjectId) {
      toast({
        title: "Error",
        description: "Please select a recipe book first",
        variant: "destructive",
      });
      return;
    }

    if (!selectedAudio) {
      toast({
        title: "Error",
        description: "Please record and upload your recipe first",
        variant: "destructive",
      });
      return;
    }

    setTranscriptionLoading(true);
    try {
      console.log(`Starting transcription process for audio: ${selectedAudio}`);

      // Show a processing toast to indicate work is happening
      toast({
        title: "Processing",
        description: "Your recipe is being transcribed. This may take a minute...",
      });

      const response = await fetch(`${API_URL}/contributor/recipes/transcribe`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          audioKey: selectedAudio,
          projectId: selectedProjectId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || "Failed to process audio");
      }

      const data = await response.json();
      console.log("Transcription response data:", data);

      // Build a more detailed success message
      let successDescription = "Recipe transcribed successfully!";

      if (data.recipe) {
        const { title, ingredients, instructions } = data.recipe;

        // Create a more informative message about what was transcribed
        const titleInfo = title ? `"${title}"` : "Untitled Recipe";
        const ingredientsCount = ingredients?.length || 0;
        const instructionsCount = instructions?.length || 0;

        // Show first few ingredients and steps if available
        let ingredientsList = "";
        if (ingredientsCount > 0 && ingredients) {
          const firstIngredients = ingredients.slice(0, 2).map((i: {name?: string}) => i.name || "").filter(Boolean);
          if (firstIngredients.length > 0) {
            ingredientsList = `\nIngredients: ${firstIngredients.join(", ")}${ingredientsCount > 2 ? ", ..." : ""}`;
          }
        }

        // Format the success message
        successDescription = `Successfully transcribed ${titleInfo} with ${ingredientsCount} ingredients and ${instructionsCount} steps.${ingredientsList}`;
      }

      toast({
        title: "Success",
        description: successDescription,
      });

      // Reset the form and refresh recipes
      setIsCreating(false);
      setSelectedAudio("");
      setTranscriptionLoading(false);
      queryClient.invalidateQueries({ queryKey: ['recipes'] });

      // Navigate to the project page to see the new recipe
      if (selectedProjectId) {
        setLocation(`/contributor/projects/${selectedProjectId}`);
      }
    } catch (error) {
      console.error("Error during transcription:", error);
      setTranscriptionLoading(false);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to transcribe recipe",
        variant: "destructive",
      });
    }
  };

  // Existing recipe submission function
  const submitRecipe = async (data: RecipeFormData) => {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('Not authenticated');
    }

    // Extract just the S3 key (path after bucket name) from the image URLs
    // Server expects images WITHOUT recipes/ prefix
    const processedImages = data.images.map((url: string) => {
      // If it's already just a key (not a full URL), return as is but remove recipes/ prefix
      if (!url.includes('http')) {
        return url.replace('recipes/', '');
      }

      // Extract the path after the bucket name (everything after the last occurrence of the bucket name)
      const bucketNameIndex = url.lastIndexOf('recipe-book-images-bucket');
      if (bucketNameIndex === -1) return url;

      const pathStart = url.indexOf('/', bucketNameIndex);
      // Remove recipes/ prefix from the extracted path
      const path = pathStart !== -1 ? url.substring(pathStart + 1) : url;
      return path.replace('recipes/', '');
    });

    const response = await fetch(`${API_URL}/contributor/recipes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        ...data,
        images: processedImages,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to submit recipe');
    }

    const result = await response.json();
    queryClient.invalidateQueries({ queryKey: ['recipes'] });
    setIsCreating(false);
    return result;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F8F7F4] py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-serif font-bold text-[#2E4B7A] mb-8 text-center">
              Recipe Management
            </h1>
            <div className="text-center">Loading...</div>
          </div>
        </div>
      </div>
    );
  }

  if (!projects || projects.length === 0) {
    return (
      <div className="min-h-screen bg-[#F8F7F4] py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-serif font-bold text-[#2E4B7A] mb-8 text-center">
              Recipe Management
            </h1>
            <div className="text-center">
              {user?.role === 'contributor' ? (
                <p className="mb-4">You haven't been invited to contribute to any recipe books yet.</p>
              ) : (
                <>
                  <p className="mb-4">You need to create a recipe book first before adding recipes.</p>
                  <button
                    onClick={() => setLocation("/recipe-books/create")}
                    className="bg-[#9B7A5D] hover:bg-[#8B6A4D] text-white px-4 py-2 rounded"
                  >
                    Create Recipe Book
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#F8F7F4] py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-serif font-bold text-[#2E4B7A] mb-8 text-center">
            Recipe Management
          </h1>

          <div className="flex justify-end mb-6">
            <Button
              onClick={() => setIsCreating(true)}
              className="bg-[#9B7A5D] hover:bg-[#8B6A4D] text-white"
              disabled={isCreating}
            >
              <Plus className="mr-2 h-4 w-4" /> Add New Recipe
            </Button>
          </div>

          {isCreating && (
            <Card className="mb-8">
              <CardContent className="pt-6">
                <Tabs defaultValue="manual" onValueChange={setActiveTab} value={activeTab}>
                  <TabsList className="grid w-full grid-cols-3 mb-6">
                    <TabsTrigger value="manual">Manual Entry</TabsTrigger>
                    <TabsTrigger value="scan" className="flex items-center">
                      <Scan className="h-4 w-4 mr-2" />
                      Scan Recipe
                    </TabsTrigger>
                    <TabsTrigger value="voice" className="flex items-center">
                      <Mic className="h-4 w-4 mr-2" />
                      Voice Record
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="manual">
                    <RecipeForm
                      projects={projects || []}
                      onSubmit={submitRecipe}
                      onProjectSelect={(projectId) => setSelectedProjectId(projectId)}
                    />
                  </TabsContent>

                  <TabsContent value="scan">
                    <div className="space-y-6">
                      <div className="mb-4">
                        <h3 className="text-lg font-medium mb-2">Select Recipe Book</h3>
                        <select
                          className="w-full border border-gray-300 rounded-md p-2"
                          onChange={(e) => setSelectedProjectId(Number(e.target.value))}
                          value={selectedProjectId || ""}
                        >
                          <option value="">Select a recipe book</option>
                          {projects?.map((project) => (
                            <option key={project.id} value={project.id}>
                              {project.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="mb-4">
                        <h3 className="text-lg font-medium mb-2">Upload Recipe Image</h3>
                        <ImageUpload
                          onUpload={handleImagesUpload}
                          maxFiles={1}
                          acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                          initialFiles={selectedImages}
                        />
                      </div>

                      <Button
                        onClick={processOcr}
                        disabled={selectedImages.length === 0 || ocrLoading || !selectedProjectId}
                        className="w-full"
                      >
                        {ocrLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Scan className="mr-2 h-4 w-4" />
                            Scan Recipe
                          </>
                        )}
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="voice">
                    <div className="space-y-6">
                      <div className="mb-4">
                        <h3 className="text-lg font-medium mb-2">Select Recipe Book</h3>
                        <select
                          className="w-full border border-gray-300 rounded-md p-2"
                          onChange={(e) => {
                            const id = Number(e.target.value);
                            console.log('Setting project ID to:', id);
                            setSelectedProjectId(id);
                          }}
                          value={selectedProjectId || ""}
                        >
                          <option value="">Select a recipe book</option>
                          {projects?.map((project) => (
                            <option key={project.id} value={project.id}>
                              {project.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="mb-4">
                        <h3 className="text-lg font-medium mb-2">Record Your Recipe</h3>
                        <p className="text-sm text-gray-500 mb-4">
                          Speak clearly and include the recipe title, description, ingredients, and step-by-step instructions.
                        </p>
                        <AudioRecorder onUpload={handleAudioUpload} />
                      </div>

                      {/* Debug info */}
                      <div className="text-xs text-gray-500 mb-2">
                        Audio: {selectedAudio ? `Uploaded ✓ (${selectedAudio.substring(0, 20)}...)` : 'Not uploaded'} |
                        Recipe Book: {selectedProjectId ? `Selected ✓ (ID: ${selectedProjectId})` : 'Not selected'}
                      </div>

                      <Button
                        onClick={processTranscription}
                        disabled={!selectedAudio || transcriptionLoading || !selectedProjectId}
                        className="w-full"
                      >
                        {transcriptionLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Transcribing...
                          </>
                        ) : (
                          <>
                            <Mic className="mr-2 h-4 w-4" />
                            Transcribe Recipe {selectedAudio && selectedProjectId && !transcriptionLoading ? '(Ready)' : ''}
                          </>
                        )}
                      </Button>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex justify-end mt-6">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsCreating(false);
                      setSelectedImages([]);
                      setSelectedAudio("");
                      setOcrData(null);
                    }}
                    className="mr-2"
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Display existing recipes */}
          {recipes && recipes.length > 0 && (
            <Card>
              <CardContent className="pt-6">
                <h2 className="text-2xl font-bold text-[#2E4B7A] mb-4">
                  Your Recipes
                </h2>
                <div className="space-y-4">
                  {recipes.map((recipe) => (
                    <div
                      key={recipe.id}
                      className="border rounded-lg p-4 flex justify-between items-start"
                    >
                      <div>
                        <h3 className="font-medium text-lg">{recipe.title}</h3>
                        <p className="text-gray-600">
                          {recipe.description.length > 100
                            ? recipe.description.substring(0, 100) + "..."
                            : recipe.description}
                        </p>
                        <div className="mt-2 flex items-center space-x-4">
                          <span
                            className={`px-2 py-1 rounded text-xs ${
                              recipe.status === "approved"
                                ? "bg-green-200 text-green-800"
                                : recipe.status === "rejected"
                                ? "bg-red-200 text-red-800"
                                : "bg-yellow-200 text-yellow-800"
                            }`}
                          >
                            {recipe.status.charAt(0).toUpperCase() +
                              recipe.status.slice(1)}
                          </span>
                          <span className="text-sm text-gray-500">
                            Added on{" "}
                            {new Date(recipe.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setLocation(`/recipes/${recipe.id}/edit`)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => {
                            if (
                              window.confirm(
                                "Are you sure you want to delete this recipe?"
                              )
                            ) {
                              deleteRecipeMutation.mutate(recipe.id);
                            }
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {recipes && recipes.length === 0 && !isCreating && (
            <Card className="text-center p-8">
              <p className="text-[#2E4B7A] mb-4">No recipes found. Click "Add New Recipe" to get started.</p>
            </Card>
          )}

          {/* Recipe FAQ Section */}
          <div className="mt-12 pt-8 border-t">
            <div className="mb-6">
              <h2 className="text-2xl font-serif font-bold text-[#2E4B7A] mb-2">Recipe Submission Help</h2>
              <p className="text-gray-600">Common questions about adding recipes to your cookbook</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-[#2E4B7A] mb-3">What information should I include in my recipe?</h3>
                    <p className="text-sm text-gray-600">
                      Include the recipe title, complete ingredient list with measurements, step-by-step instructions,
                      cooking time, and serving size. Adding photos and family stories makes your recipe even more special!
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-[#2E4B7A] mb-3">How do I upload photos with my recipe?</h3>
                    <p className="text-sm text-gray-600">
                      You can upload up to 3 photos per recipe. Click the image upload area in the form and select photos
                      from your device. We recommend including photos of the finished dish and any preparation steps.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-[#2E4B7A] mb-3">How does the voice recording feature work?</h3>
                    <p className="text-sm text-gray-600">
                      Click the "Voice Record" tab, select your recipe book, then record yourself reading the recipe aloud.
                      Our system will automatically transcribe it into a formatted recipe for you to review and edit.
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-[#2E4B7A] mb-3">Can I edit my recipe after submitting?</h3>
                    <p className="text-sm text-gray-600">
                      Yes! You can edit your own recipes anytime by going to "My Recipes" and clicking the edit button.
                      Organizers can also edit any recipe in their books.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-[#2E4B7A] mb-3">What is the recipe scanning feature?</h3>
                    <p className="text-sm text-gray-600">
                      Take a photo of a handwritten or printed recipe, and our OCR technology will automatically
                      extract the text and create a digital recipe for you. Perfect for preserving old family recipes!
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-3">
                      <MessageSquare className="h-5 w-5 text-[#9B7A5D]" />
                      <h3 className="text-lg font-semibold text-[#2E4B7A]">Having technical issues?</h3>
                    </div>
                    <p className="text-sm text-gray-600 mb-4">
                      If you're experiencing problems with photo uploads, voice recording, or recipe scanning,
                      our support team can help troubleshoot the issue.
                    </p>
                    <Link href="/support">
                      <Button variant="outline" className="w-full border-[#9B7A5D] text-[#9B7A5D] hover:bg-[#9B7A5D] hover:text-white">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Contact Support
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}