<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Storyworth - Collaborative Recipe Book Platform</title>
    <meta name="description" content="Create beautiful, custom recipe books with family and friends. Collaborative platform for preserving culinary memories through structured forms, voice recordings, and OCR scanning.">
    <meta name="keywords" content="recipe book, cookbook, family recipes, collaborative cooking, print on demand, recipe sharing">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://storyworth.com/">
    <meta property="og:title" content="Storyworth - Collaborative Recipe Book Platform">
    <meta property="og:description" content="Create beautiful, custom recipe books with family and friends. Collaborative platform for preserving culinary memories.">
    <meta property="og:image" content="https://storyworth.com/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://storyworth.com/">
    <meta property="twitter:title" content="Storyworth - Collaborative Recipe Book Platform">
    <meta property="twitter:description" content="Create beautiful, custom recipe books with family and friends. Collaborative platform for preserving culinary memories.">
    <meta property="twitter:image" content="https://storyworth.com/twitter-image.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Caveat:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            /* Brand Colors - Extracted from project */
            --primary: #2E4B7A;
            --primary-light: #4A6B9A;
            --secondary: #E6D5C4;
            --accent: #9B7A5D;
            --background: #F8F7F4;
            --card: #FFFFFF;
            --text: #2E4B7A;
            --text-muted: #9B7A5D;
            --border: #E6D5C4;
            --success: #16A34A;
            --warning: #D97706;
            --error: #DC2626;

            /* Typography */
            --font-serif: 'Playfair Display', serif;
            --font-sans: 'Inter', sans-serif;
            --font-handwritten: 'Caveat', cursive;

            /* Spacing */
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;
            --spacing-2xl: 4rem;

            /* Border Radius */
            --radius: 0.5rem;
            --radius-lg: 1rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-sans);
            line-height: 1.6;
            color: var(--text);
            background-color: var(--background);
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }

        .container-wide {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-serif);
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: var(--spacing-sm);
        }

        h1 {
            font-size: 3.5rem;
            font-weight: 700;
        }

        h2 {
            font-size: 2.5rem;
        }

        h3 {
            font-size: 2rem;
        }

        h4 {
            font-size: 1.5rem;
        }

        p {
            margin-bottom: var(--spacing-sm);
            color: var(--text-muted);
        }

        .text-large {
            font-size: 1.25rem;
        }

        .text-center {
            text-align: center;
        }

        .text-primary {
            color: var(--primary);
        }

        .text-accent {
            color: var(--accent);
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--radius);
            font-family: var(--font-sans);
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background-color: var(--secondary);
            color: var(--text);
        }

        .btn-secondary:hover {
            background-color: var(--accent);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-outline {
            background-color: transparent;
            color: var(--primary);
            border: 2px solid var(--primary);
        }

        .btn-outline:hover {
            background-color: var(--primary);
            color: white;
        }

        .btn-lg {
            padding: var(--spacing-md) var(--spacing-xl);
            font-size: 1.125rem;
        }

        /* Cards */
        .card {
            background: var(--card);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 1px solid var(--border);
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        /* Grid */
        .grid {
            display: grid;
            gap: var(--spacing-lg);
        }

        .grid-2 {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .grid-3 {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .grid-4 {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        /* Sections */
        .section {
            padding: var(--spacing-2xl) 0;
        }

        .section-alt {
            background-color: var(--card);
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(248, 247, 244, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-sm) 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-family: var(--font-serif);
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
        }

        .nav {
            display: flex;
            gap: var(--spacing-lg);
            list-style: none;
        }

        .nav a {
            color: var(--text);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav a:hover {
            color: var(--primary);
        }

        /* Hero Section */
        .hero {
            padding: calc(80px + var(--spacing-2xl)) 0 var(--spacing-2xl);
            background: linear-gradient(135deg, var(--background) 0%, var(--secondary) 100%);
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23E6D5C4" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%239B7A5D" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%232E4B7A" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            margin-bottom: var(--spacing-md);
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            margin-bottom: var(--spacing-xl);
            color: var(--text-muted);
        }

        .hero-cta {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Feature Icons */
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: var(--spacing-md);
        }

        /* Stats */
        .stats {
            background: var(--primary);
            color: white;
            padding: var(--spacing-xl) 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            text-align: center;
        }

        .stat-number {
            font-family: var(--font-serif);
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            font-size: 1.125rem;
            opacity: 0.9;
        }

        /* Technology Stack */
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            justify-content: center;
            margin-top: var(--spacing-lg);
        }

        .tech-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-md);
            background: var(--card);
            border: 1px solid var(--border);
            border-radius: var(--radius);
            font-weight: 500;
            color: var(--text);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Responsive */
        @media (max-width: 768px) {
            h1 {
                font-size: 2.5rem;
            }

            h2 {
                font-size: 2rem;
            }

            .hero-cta {
                flex-direction: column;
                align-items: center;
            }

            .nav {
                display: none;
            }

            .container {
                padding: 0 var(--spacing-sm);
            }

            .grid-2,
            .grid-3,
            .grid-4 {
                grid-template-columns: 1fr;
            }
        }

        /* Utility Classes */
        .mb-0 { margin-bottom: 0; }
        .mb-1 { margin-bottom: var(--spacing-xs); }
        .mb-2 { margin-bottom: var(--spacing-sm); }
        .mb-3 { margin-bottom: var(--spacing-md); }
        .mb-4 { margin-bottom: var(--spacing-lg); }
        .mb-5 { margin-bottom: var(--spacing-xl); }

        .mt-0 { margin-top: 0; }
        .mt-1 { margin-top: var(--spacing-xs); }
        .mt-2 { margin-top: var(--spacing-sm); }
        .mt-3 { margin-top: var(--spacing-md); }
        .mt-4 { margin-top: var(--spacing-lg); }
        .mt-5 { margin-top: var(--spacing-xl); }

        /* Additional Styles */
        .badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: var(--success);
            color: white;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .badge-warning {
            background: var(--warning);
        }

        .badge-secondary {
            background: var(--secondary);
            color: var(--text);
        }

        .pricing-card {
            position: relative;
            overflow: hidden;
        }

        .pricing-card.featured {
            border: 2px solid var(--primary);
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: 'Most Popular';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: var(--primary);
            color: white;
            text-align: center;
            padding: 0.5rem;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .pricing-card.featured .card {
            padding-top: calc(var(--spacing-lg) + 2rem);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-xs);
            color: var(--text-muted);
        }

        .feature-list li i {
            color: var(--success);
            width: 16px;
        }

        .timeline {
            position: relative;
            padding-left: 2rem;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 0.75rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: var(--border);
        }

        .timeline-item {
            position: relative;
            margin-bottom: var(--spacing-lg);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -1.25rem;
            top: 0.5rem;
            width: 12px;
            height: 12px;
            background: var(--primary);
            border-radius: 50%;
            border: 3px solid var(--background);
        }

        .footer {
            background: var(--primary);
            color: white;
            padding: var(--spacing-xl) 0 var(--spacing-lg);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .footer h4 {
            color: white;
            margin-bottom: var(--spacing-md);
        }

        .footer a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: var(--spacing-lg);
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <a href="#" class="logo">
                    <i class="fas fa-book-open"></i>
                    Storyworth
                </a>
                <nav>
                    <ul class="nav">
                        <li><a href="#features">Features</a></li>
                        <li><a href="#how-it-works">How It Works</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#technology">Technology</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
                <div class="hero-cta">
                    <a href="#demo" class="btn btn-primary">Request Demo</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content animate-fade-in-up">
                <h1>Preserve Family Recipes, Create Lasting Memories</h1>
                <p class="hero-subtitle">
                    A collaborative platform where families come together to create beautiful, custom recipe books.
                    Submit recipes through forms, voice recordings, or photo scans, then compile them into professionally printed cookbooks.
                </p>
                <div class="hero-cta">
                    <a href="#demo" class="btn btn-primary btn-lg">
                        <i class="fas fa-play"></i>
                        Watch Demo
                    </a>
                    <a href="#features" class="btn btn-secondary btn-lg">
                        <i class="fas fa-utensils"></i>
                        Explore Features
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Submission Methods</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">Recipe Book Themes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">∞</div>
                    <div class="stat-label">Contributors per Book</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Print-Ready Quality</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="section">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Comprehensive Recipe Management Platform</h2>
                <p class="text-large">Everything you need to create, collaborate, and compile beautiful recipe books</p>
            </div>

            <div class="grid grid-3 mb-5">
                <!-- User Management -->
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h4>Role-Based Access Control</h4>
                    <p>Three distinct user roles with tailored permissions:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> <strong>Organizers:</strong> Create projects, invite contributors, manage layout</li>
                        <li><i class="fas fa-check"></i> <strong>Contributors:</strong> Submit recipes via multiple methods</li>
                        <li><i class="fas fa-check"></i> <strong>Admins:</strong> Platform management and content moderation</li>
                    </ul>
                </div>

                <!-- Recipe Submission -->
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h4>Multiple Submission Methods</h4>
                    <p>Flexible ways to capture family recipes:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> <strong>Structured Forms:</strong> Guided recipe entry with auto-complete</li>
                        <li><i class="fas fa-check"></i> <strong>Voice-to-Text:</strong> Record recipes verbally using Whisper AI</li>
                        <li><i class="fas fa-check"></i> <strong>OCR Scanning:</strong> Upload photos of handwritten recipes</li>
                    </ul>
                </div>

                <!-- Recipe Management -->
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-list-alt"></i>
                    </div>
                    <h4>Advanced Recipe Features</h4>
                    <p>Rich recipe management capabilities:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Ingredients with US/Metric measurements</li>
                        <li><i class="fas fa-check"></i> Multi-step instructions with formatting</li>
                        <li><i class="fas fa-check"></i> Multiple image uploads per recipe</li>
                        <li><i class="fas fa-check"></i> Categorization and tagging system</li>
                        <li><i class="fas fa-check"></i> Recipe approval workflow</li>
                    </ul>
                </div>

                <!-- Collaboration -->
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h4>Collaborative Features</h4>
                    <p>Work together seamlessly:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Email invitations with custom messages</li>
                        <li><i class="fas fa-check"></i> Real-time submission tracking</li>
                        <li><i class="fas fa-check"></i> Automated reminder system</li>
                        <li><i class="fas fa-check"></i> Comments and story sharing per recipe</li>
                        <li><i class="fas fa-check"></i> Deadline management</li>
                    </ul>
                </div>

                <!-- Book Customization -->
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h4>Book Customization</h4>
                    <p>Create beautiful, personalized cookbooks:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> 15+ professional themes (Adobe Color inspired)</li>
                        <li><i class="fas fa-check"></i> Multiple font options (Elegant, Modern, Handwritten)</li>
                        <li><i class="fas fa-check"></i> Custom cover design with images</li>
                        <li><i class="fas fa-check"></i> Dedication pages and family quotes</li>
                        <li><i class="fas fa-check"></i> Photo collage sections</li>
                        <li><i class="fas fa-check"></i> Auto-pagination and smart formatting</li>
                    </ul>
                </div>

                <!-- Print Integration -->
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-print"></i>
                    </div>
                    <h4>Print-on-Demand Integration</h4>
                    <p>Professional printing and delivery:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Blurb API integration for printing</li>
                        <li><i class="fas fa-check"></i> Upload-ready PDF generation</li>
                        <li><i class="fas fa-check"></i> Real-time preview of final book</li>
                        <li><i class="fas fa-check"></i> Pricing calculator by page count</li>
                        <li><i class="fas fa-check"></i> Address collection and validation</li>
                        <li><i class="fas fa-check"></i> Shipping status tracking</li>
                    </ul>
                </div>
            </div>

            <!-- Additional Features -->
            <div class="grid grid-2">
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4>Cross-Platform Access</h4>
                    <p>Access your recipes anywhere:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Responsive web application</li>
                        <li><i class="fas fa-check"></i> Native iOS and Android apps (React Native)</li>
                        <li><i class="fas fa-check"></i> Offline support with sync</li>
                        <li><i class="fas fa-check"></i> Push notifications</li>
                        <li><i class="fas fa-check"></i> Camera integration for mobile scanning</li>
                    </ul>
                </div>

                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4>Security & Privacy</h4>
                    <p>Your data is safe and secure:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> GDPR & CCPA compliance</li>
                        <li><i class="fas fa-check"></i> End-to-end encryption</li>
                        <li><i class="fas fa-check"></i> Secure cloud storage (AWS S3)</li>
                        <li><i class="fas fa-check"></i> Auto-backup and data recovery</li>
                        <li><i class="fas fa-check"></i> Cookie consent management</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="section section-alt">
        <div class="container">
            <div class="text-center mb-5">
                <h2>How It Works</h2>
                <p class="text-large">Simple steps to create your family cookbook</p>
            </div>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="card">
                        <h4><i class="fas fa-user-plus text-primary"></i> Step 1: Setup & Invite</h4>
                        <p>Organizer creates a recipe book project, selects pricing tier based on number of contributors, and sends email invitations to family members with custom welcome messages.</p>
                        <div class="mt-3">
                            <span class="badge">Project Creation</span>
                            <span class="badge">Email Invitations</span>
                            <span class="badge">Tier Selection</span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="card">
                        <h4><i class="fas fa-edit text-primary"></i> Step 2: Recipe Submission</h4>
                        <p>Contributors submit recipes using their preferred method - structured forms with auto-complete, voice recordings that get transcribed, or photo uploads with OCR processing.</p>
                        <div class="mt-3">
                            <span class="badge">Form Entry</span>
                            <span class="badge">Voice Recording</span>
                            <span class="badge">Photo Scanning</span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="card">
                        <h4><i class="fas fa-comments text-primary"></i> Step 3: Collaboration & Review</h4>
                        <p>Family members can add stories and comments to recipes. Organizers track submissions, send reminders, and review/approve content before final compilation.</p>
                        <div class="mt-3">
                            <span class="badge">Story Sharing</span>
                            <span class="badge">Content Review</span>
                            <span class="badge">Progress Tracking</span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="card">
                        <h4><i class="fas fa-palette text-primary"></i> Step 4: Customize Design</h4>
                        <p>Choose from 15+ professional themes, select fonts, arrange chapters, add dedication pages, family quotes, and photo collages to create a personalized cookbook.</p>
                        <div class="mt-3">
                            <span class="badge">Theme Selection</span>
                            <span class="badge">Layout Design</span>
                            <span class="badge">Personal Touches</span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="card">
                        <h4><i class="fas fa-eye text-primary"></i> Step 5: Preview & Finalize</h4>
                        <p>Real-time preview shows exactly how the final book will look. Auto-pagination ensures perfect formatting. Pricing calculator shows costs based on page count.</p>
                        <div class="mt-3">
                            <span class="badge">Live Preview</span>
                            <span class="badge">Auto-Formatting</span>
                            <span class="badge">Cost Calculator</span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="card">
                        <h4><i class="fas fa-print text-primary"></i> Step 6: Print & Deliver</h4>
                        <p>Upload-ready PDF is generated and sent to Blurb for professional printing. Track printing status and shipping progress until your beautiful cookbook arrives.</p>
                        <div class="mt-3">
                            <span class="badge">PDF Generation</span>
                            <span class="badge">Professional Printing</span>
                            <span class="badge">Delivery Tracking</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Admin Features Section -->
    <section class="section">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Comprehensive Admin & Support Features</h2>
                <p class="text-large">Complete platform management and customer support tools</p>
            </div>

            <div class="grid grid-2 mb-5">
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h4>Admin Dashboard</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> View and manage all projects and users</li>
                        <li><i class="fas fa-check"></i> Content moderation with edit/delete capabilities</li>
                        <li><i class="fas fa-check"></i> Submission activity logs and analytics</li>
                        <li><i class="fas fa-check"></i> Print order history and status tracking</li>
                        <li><i class="fas fa-check"></i> Template and theme management system</li>
                        <li><i class="fas fa-check"></i> Pricing tier and site content control</li>
                    </ul>
                </div>

                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h4>Customer Support Tools</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Integrated ticketing system (Zendesk/Intercom)</li>
                        <li><i class="fas fa-check"></i> In-app help center and FAQ system</li>
                        <li><i class="fas fa-check"></i> Live chat support (optional)</li>
                        <li><i class="fas fa-check"></i> Admin notes and internal flags</li>
                        <li><i class="fas fa-check"></i> Support ticket assignment and routing</li>
                        <li><i class="fas fa-check"></i> Customer communication history</li>
                    </ul>
                </div>
            </div>

            <div class="grid grid-3">
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-flag"></i>
                    </div>
                    <h4>Content Moderation</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Advanced flagging system</li>
                        <li><i class="fas fa-check"></i> Moderation queues for review</li>
                        <li><i class="fas fa-check"></i> Automated content filtering</li>
                        <li><i class="fas fa-check"></i> Manual review workflows</li>
                    </ul>
                </div>

                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4>Analytics & Reporting</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> User engagement metrics</li>
                        <li><i class="fas fa-check"></i> Recipe submission analytics</li>
                        <li><i class="fas fa-check"></i> Print order statistics</li>
                        <li><i class="fas fa-check"></i> Revenue tracking</li>
                    </ul>
                </div>

                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h4>Notification System</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Automated reminder emails</li>
                        <li><i class="fas fa-check"></i> Push notifications (mobile)</li>
                        <li><i class="fas fa-check"></i> Deadline alerts</li>
                        <li><i class="fas fa-check"></i> Status update notifications</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="section section-alt">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Flexible Pricing Tiers</h2>
                <p class="text-large">Choose the plan that fits your family size and needs</p>
            </div>

            <div class="grid grid-3">
                <div class="pricing-card">
                    <div class="card">
                        <h4>Small Family</h4>
                        <div class="stat-number text-primary">$29</div>
                        <p class="text-center mb-3">Perfect for small families</p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Up to 5 contributors</li>
                            <li><i class="fas fa-check"></i> Up to 50 recipes</li>
                            <li><i class="fas fa-check"></i> All submission methods</li>
                            <li><i class="fas fa-check"></i> Basic themes</li>
                            <li><i class="fas fa-check"></i> Email support</li>
                        </ul>
                        <a href="#contact" class="btn btn-outline" style="width: 100%; margin-top: 1rem;">Get Started</a>
                    </div>
                </div>

                <div class="pricing-card featured">
                    <div class="card">
                        <h4>Extended Family</h4>
                        <div class="stat-number text-primary">$59</div>
                        <p class="text-center mb-3">Most popular choice</p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Up to 15 contributors</li>
                            <li><i class="fas fa-check"></i> Up to 150 recipes</li>
                            <li><i class="fas fa-check"></i> All submission methods</li>
                            <li><i class="fas fa-check"></i> Premium themes</li>
                            <li><i class="fas fa-check"></i> Priority support</li>
                            <li><i class="fas fa-check"></i> Advanced customization</li>
                        </ul>
                        <a href="#contact" class="btn btn-primary" style="width: 100%; margin-top: 1rem;">Get Started</a>
                    </div>
                </div>

                <div class="pricing-card">
                    <div class="card">
                        <h4>Large Community</h4>
                        <div class="stat-number text-primary">$99</div>
                        <p class="text-center mb-3">For large families & communities</p>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Unlimited contributors</li>
                            <li><i class="fas fa-check"></i> Unlimited recipes</li>
                            <li><i class="fas fa-check"></i> All submission methods</li>
                            <li><i class="fas fa-check"></i> All themes & fonts</li>
                            <li><i class="fas fa-check"></i> White-glove support</li>
                            <li><i class="fas fa-check"></i> Custom branding</li>
                        </ul>
                        <a href="#contact" class="btn btn-outline" style="width: 100%; margin-top: 1rem;">Get Started</a>
                    </div>
                </div>
            </div>

            <div class="text-center mt-5">
                <p class="text-large mb-3">All plans include:</p>
                <div class="grid grid-4">
                    <div><i class="fas fa-check text-success"></i> Unlimited revisions</div>
                    <div><i class="fas fa-check text-success"></i> Real-time preview</div>
                    <div><i class="fas fa-check text-success"></i> PDF generation</div>
                    <div><i class="fas fa-check text-success"></i> Print integration</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Stack Section -->
    <section id="technology" class="section">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Built with Modern Technology</h2>
                <p class="text-large">Scalable, secure, and cost-efficient technology stack</p>
            </div>

            <div class="grid grid-2 mb-5">
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <h4>Frontend Technologies</h4>
                    <div class="tech-stack">
                        <div class="tech-item">
                            <i class="fab fa-react"></i>
                            React
                        </div>
                        <div class="tech-item">
                            <i class="fab fa-js"></i>
                            TypeScript
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-paint-brush"></i>
                            Tailwind CSS
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-mobile-alt"></i>
                            React Native
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-bolt"></i>
                            Vite
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h4>Backend Technologies</h4>
                    <div class="tech-stack">
                        <div class="tech-item">
                            <i class="fab fa-node-js"></i>
                            Node.js
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-express"></i>
                            Express.js
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-database"></i>
                            PostgreSQL
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-layer-group"></i>
                            Drizzle ORM
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-shield-alt"></i>
                            JWT Auth
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-3">
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <h4>Cloud & Storage</h4>
                    <div class="tech-stack">
                        <div class="tech-item">
                            <i class="fab fa-aws"></i>
                            AWS S3
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-lock"></i>
                            Encryption
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-backup"></i>
                            Auto-Backup
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h4>AI & Integrations</h4>
                    <div class="tech-stack">
                        <div class="tech-item">
                            <i class="fas fa-microphone"></i>
                            Whisper API
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-eye"></i>
                            Google Vision
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-envelope"></i>
                            SendGrid
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-print"></i>
                            Blurb API
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h4>Development Tools</h4>
                    <div class="tech-stack">
                        <div class="tech-item">
                            <i class="fab fa-git-alt"></i>
                            Git
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-code-branch"></i>
                            CI/CD
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-bug"></i>
                            Testing
                        </div>
                        <div class="tech-item">
                            <i class="fas fa-chart-line"></i>
                            Monitoring
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Milestones & Development Status -->
    <section class="section section-alt">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Development Progress & Roadmap</h2>
                <p class="text-large">Track our progress and upcoming features</p>
            </div>

            <div class="grid grid-2">
                <div class="card">
                    <h4><i class="fas fa-check-circle text-success"></i> Completed Features</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check text-success"></i> Full-stack project setup (frontend + backend)</li>
                        <li><i class="fas fa-check text-success"></i> User authentication & role-based access control</li>
                        <li><i class="fas fa-check text-success"></i> Organizer dashboard with project creation</li>
                        <li><i class="fas fa-check text-success"></i> Email integration for invites and notifications</li>
                        <li><i class="fas fa-check text-success"></i> Structured recipe forms with auto-complete</li>
                        <li><i class="fas fa-check text-success"></i> Voice-to-text transcription (Whisper API)</li>
                        <li><i class="fas fa-check text-success"></i> Multi-recipe submission interface</li>
                        <li><i class="fas fa-check text-success"></i> Admin panel with user/project management</li>
                        <li><i class="fas fa-check text-success"></i> Cloud storage integration (AWS S3)</li>
                        <li><i class="fas fa-check text-success"></i> Contributor dashboard with tracking</li>
                        <li><i class="fas fa-check text-success"></i> Recipe book filters and organization</li>
                        <li><i class="fas fa-check text-success"></i> Collaborative comments system</li>
                        <li><i class="fas fa-check text-success"></i> Automated reminder system</li>
                        <li><i class="fas fa-check text-success"></i> Book customization (themes, fonts, styles)</li>
                        <li><i class="fas fa-check text-success"></i> Real-time preview and pagination</li>
                        <li><i class="fas fa-check text-success"></i> Tier-based pricing model</li>
                        <li><i class="fas fa-check text-success"></i> Mobile app foundation (React Native)</li>
                    </ul>
                </div>

                <div class="card">
                    <h4><i class="fas fa-clock text-warning"></i> In Development</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-spinner text-warning"></i> OCR scan uploads (Google Cloud Vision)</li>
                        <li><i class="fas fa-spinner text-warning"></i> Photo collage section tools</li>
                        <li><i class="fas fa-spinner text-warning"></i> Upload-ready PDF generation</li>
                        <li><i class="fas fa-spinner text-warning"></i> Blurb API integration for printing</li>
                        <li><i class="fas fa-spinner text-warning"></i> Address collection and validation</li>
                        <li><i class="fas fa-spinner text-warning"></i> Print order routing and tracking</li>
                        <li><i class="fas fa-spinner text-warning"></i> Invoice summary and billing</li>
                        <li><i class="fas fa-spinner text-warning"></i> Full admin analytics dashboard</li>
                        <li><i class="fas fa-spinner text-warning"></i> Advanced content moderation</li>
                        <li><i class="fas fa-spinner text-warning"></i> Customer support tools integration</li>
                        <li><i class="fas fa-spinner text-warning"></i> Mobile app completion (iOS & Android)</li>
                        <li><i class="fas fa-spinner text-warning"></i> End-to-end encryption</li>
                        <li><i class="fas fa-spinner text-warning"></i> Penetration testing & security audit</li>
                        <li><i class="fas fa-spinner text-warning"></i> Final GDPR/CCPA compliance</li>
                    </ul>
                </div>
            </div>

            <div class="text-center mt-5">
                <h4>Future Enhancements (Phase 2+)</h4>
                <div class="grid grid-4 mt-3">
                    <div class="card">
                        <i class="fas fa-magic text-accent" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <h5>AI Enhancement</h5>
                        <p>Image cleanup and recipe summarization</p>
                    </div>
                    <div class="card">
                        <i class="fas fa-globe text-accent" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <h5>Cultural Tagging</h5>
                        <p>Regional and cultural recipe categorization</p>
                    </div>
                    <div class="card">
                        <i class="fas fa-tablet-alt text-accent" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <h5>Digital E-books</h5>
                        <p>Built-in digital cookbook versions</p>
                    </div>
                    <div class="card">
                        <i class="fas fa-family text-accent" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                        <h5>Timeline Layout</h5>
                        <p>"Recipe Through Generations" feature</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact/Demo Section -->
    <section id="contact" class="section">
        <div class="container">
            <div class="text-center mb-5">
                <h2>Ready to Get Started?</h2>
                <p class="text-large">Contact us for a demo or to discuss your family cookbook project</p>
            </div>

            <div class="grid grid-2">
                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h4>Schedule a Demo</h4>
                    <p>See the platform in action with a personalized demonstration of all features and capabilities.</p>
                    <a href="mailto:<EMAIL>?subject=Demo Request" class="btn btn-primary">
                        <i class="fas fa-envelope"></i>
                        Request Demo
                    </a>
                </div>

                <div class="card">
                    <div class="feature-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h4>Discuss Your Project</h4>
                    <p>Have questions about pricing, features, or implementation? Let's talk about your specific needs.</p>
                    <a href="mailto:<EMAIL>?subject=Project Inquiry" class="btn btn-secondary">
                        <i class="fas fa-phone"></i>
                        Contact Sales
                    </a>
                </div>
            </div>

            <div class="text-center mt-5">
                <div class="card" style="max-width: 600px; margin: 0 auto;">
                    <h4>Key Benefits for Your Organization</h4>
                    <div class="grid grid-2 mt-3">
                        <div>
                            <i class="fas fa-users text-primary"></i>
                            <strong>Collaborative</strong><br>
                            Multiple contributors working together
                        </div>
                        <div>
                            <i class="fas fa-mobile-alt text-primary"></i>
                            <strong>Cross-Platform</strong><br>
                            Web and mobile applications
                        </div>
                        <div>
                            <i class="fas fa-shield-alt text-primary"></i>
                            <strong>Secure</strong><br>
                            Enterprise-grade security and privacy
                        </div>
                        <div>
                            <i class="fas fa-print text-primary"></i>
                            <strong>Print-Ready</strong><br>
                            Professional cookbook production
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <h4>Storyworth</h4>
                    <p>Preserving family recipes and creating lasting memories through collaborative cookbook creation.</p>
                    <div style="margin-top: 1rem;">
                        <a href="mailto:<EMAIL>" style="margin-right: 1rem;">
                            <i class="fas fa-envelope"></i>
                        </a>
                        <a href="#" style="margin-right: 1rem;">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" style="margin-right: 1rem;">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h4>Platform</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem;"><a href="#features">Features</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#pricing">Pricing</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#technology">Technology</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#how-it-works">How It Works</a></li>
                    </ul>
                </div>

                <div>
                    <h4>Support</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem;"><a href="mailto:<EMAIL>">Help Center</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="mailto:<EMAIL>">Contact Support</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#demo">Request Demo</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="mailto:<EMAIL>">Sales Inquiry</a></li>
                    </ul>
                </div>

                <div>
                    <h4>Legal</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 0.5rem;"><a href="#">Privacy Policy</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#">Terms of Service</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#">Cookie Policy</a></li>
                        <li style="margin-bottom: 0.5rem;"><a href="#">GDPR Compliance</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 Storyworth. All rights reserved. | Built with modern technology for preserving family memories.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(248, 247, 244, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(248, 247, 244, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all cards and sections
        document.querySelectorAll('.card, .timeline-item').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Add loading animation to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (this.href && this.href.includes('mailto:')) {
                    // Don't prevent default for mailto links
                    return;
                }

                // Add loading state for other buttons
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
                this.style.pointerEvents = 'none';

                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.style.pointerEvents = 'auto';
                }, 2000);
            });
        });

        // Stats counter animation
        function animateStats() {
            const stats = document.querySelectorAll('.stat-number');
            stats.forEach(stat => {
                const target = stat.textContent;
                if (target === '∞' || target === '100%') return;

                const number = parseInt(target.replace('+', ''));
                let current = 0;
                const increment = number / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= number) {
                        stat.textContent = target;
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(current) + (target.includes('+') ? '+' : '');
                    }
                }, 50);
            });
        }

        // Trigger stats animation when stats section is visible
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateStats();
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        const statsSection = document.querySelector('.stats');
        if (statsSection) {
            statsObserver.observe(statsSection);
        }

        // Add hover effects to feature icons
        document.querySelectorAll('.feature-icon').forEach(icon => {
            icon.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.1) rotate(5deg)';
            });

            icon.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) rotate(0deg)';
            });
        });

        // Mobile menu toggle (if needed)
        function toggleMobileMenu() {
            const nav = document.querySelector('.nav');
            nav.style.display = nav.style.display === 'flex' ? 'none' : 'flex';
        }

        // Add mobile menu button for small screens
        if (window.innerWidth <= 768) {
            const headerContent = document.querySelector('.header-content');
            const mobileMenuBtn = document.createElement('button');
            mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
            mobileMenuBtn.className = 'btn btn-primary';
            mobileMenuBtn.style.display = 'block';
            mobileMenuBtn.onclick = toggleMobileMenu;

            // Replace the demo button with menu button on mobile
            const demoBtn = headerContent.querySelector('.hero-cta');
            if (demoBtn) {
                demoBtn.style.display = 'none';
                headerContent.appendChild(mobileMenuBtn);
            }
        }

        // Add parallax effect to hero section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add fade-in animation to hero content
            const heroContent = document.querySelector('.hero-content');
            if (heroContent) {
                setTimeout(() => {
                    heroContent.style.opacity = '1';
                    heroContent.style.transform = 'translateY(0)';
                }, 300);
            }
        });
    </script>
</body>
</html>