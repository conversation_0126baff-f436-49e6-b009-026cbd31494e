import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { QueryClientProvider } from '@tanstack/react-query';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { AuthProvider } from './src/hooks/use-auth';
import { queryClient } from './src/lib/queryClient';
import { Router, Route, Switch, useLocation } from './src/lib/router';
import { Colors } from './src/lib/constants';

// Import all pages exactly like web client
import NotFound from './src/pages/not-found';
import AuthForm from './src/components/auth/auth-form';
import { ProtectedRoute } from './src/components/auth/protected-route';
import { UserRole } from './src/lib/constants';
import RecipeBooks from './src/pages/recipe-books';
import CreateRecipeBook from './src/pages/recipe-books/create';
import CreateRecipe from './src/pages/recipes/create';
import AdminPanel from './src/pages/admin';
import AdminDashboard from './src/pages/admin/dashboard';
import OrganizerDashboard from './src/pages/organizer';
import AcceptInvite from './src/pages/accept-invite';
import ContributorDashboard from './src/pages/contributor/dashboard';
import ProjectDetails from './src/pages/recipe-books/[id]';
import EditRecipe from './src/pages/recipes/[id]/edit';
import RecipeApprovals from './src/pages/organizer/recipe-approvals';
import Settings from './src/pages/settings';
import { PrivacyPolicyPage, HelpCenterPage, ContactPage } from './src/pages/privacy-policy';
import { DataRequestPage } from './src/pages/data-request';
import { TermsPage } from './src/pages/terms';
import { CookiePreferencesPage } from './src/pages/cookie-preferences';
import { PricingCalculatorPage } from './src/pages/pricing-calculator';
import OrderTracking from './src/pages/order-tracking';
import ErrorBoundary from './src/components/error-boundary';
import { Header } from './src/components/layout/header';
import { Footer } from './src/components/layout/footer';
import { CookieConsent } from './src/components/privacy/cookie-consent';
import { Toaster } from './src/components/ui/toaster';
import { TooltipProvider } from './src/components/ui/tooltip';
import { LiveChatWidget, useLiveChatWidget } from './src/components/LiveChatWidget';
import SupportPage from './src/pages/support';
import Collaborate from './src/pages/collaborate';
import Projects from './src/pages/projects';
import Recipes from './src/pages/recipes';
import DesignElements from './src/pages/design-elements';
import Design2 from './src/pages/designs/design2';
import Login from './src/pages/auth/login';
import Register from './src/pages/auth/register';
import ContributorIndex from './src/pages/contributor/index';
import OrganizerIndex from './src/pages/organizer/index';
import FAQ from './src/pages/faq/index';

// Simple redirect component
const Redirect = ({ to }: { to: string }) => {
  const [, setLocation] = useLocation();

  useEffect(() => {
    setLocation(to);
  }, [to, setLocation]);

  return null;
};

function AppContent() {
  const [location] = useLocation();
  const shouldShowChat = useLiveChatWidget();

  useEffect(() => {
    console.log('Current location:', location);
  }, [location]);

  return (
    <>
      <Toaster />
      <Header />
      <Switch>
                    <Route path="/" component={() => <Redirect to="/recipe-books" />} />
                    <Route path="/login" component={Login} />
                    <Route path="/register" component={Register} />
                    <Route path="/accept-invite" component={AcceptInvite} />

                    {/* Protected Routes */}
                    <Route path="/recipe-books">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <RecipeBooks />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipe-books/create">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <CreateRecipeBook />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipe-books/:id">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <ProjectDetails />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipes/create">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <CreateRecipe />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipes/:id/edit">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <EditRecipe />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/order-tracking">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <OrderTracking />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/admin">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN]}>
                        <AdminPanel />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/admin/dashboard">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]}>
                        <AdminDashboard />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/organizer">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]}>
                        <OrganizerIndex />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/contributor">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <ContributorIndex />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/contributor/dashboard">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <ContributorDashboard />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/organizer/recipe-approvals">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]}>
                        <RecipeApprovals />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/settings">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <Settings />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/support">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <SupportPage />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/collaborate">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <Collaborate />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/projects">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <Projects />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/recipes">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <Recipes />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/design-elements">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <DesignElements />
                      </ProtectedRoute>
                    </Route>

                    <Route path="/designs/design2" component={Design2} />

                    <Route path="/faq">
                      <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR]}>
                        <FAQ />
                      </ProtectedRoute>
                    </Route>

                    {/* Pricing Calculator */}
                    <Route path="/pricing-calculator" component={PricingCalculatorPage} />

                    {/* Privacy-related routes */}
                    <Route path="/privacy-policy" component={PrivacyPolicyPage} />
                    <Route path="/data-request" component={DataRequestPage} />
                    <Route path="/terms" component={TermsPage} />
                    <Route path="/cookie-preferences" component={CookiePreferencesPage} />

                    {/* Support routes */}
                    <Route path="/help" component={HelpCenterPage} />
                    <Route path="/contact" component={ContactPage} />

                    <Route path="*" component={NotFound} />
      </Switch>
      <Footer />
      <CookieConsent />
      {shouldShowChat && <LiveChatWidget />}
    </>
  );
}

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ErrorBoundary>
          <QueryClientProvider client={queryClient}>
            <TooltipProvider>
              <Router>
                <AuthProvider>
                  <AppContent />
                </AuthProvider>
              </Router>
              <StatusBar style="dark" backgroundColor={Colors.background} />
            </TooltipProvider>
          </QueryClientProvider>
        </ErrorBoundary>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
