# RecipeBook Mobile App

This is the React Native mobile version of the RecipeBook web application. It provides the same functionality as the web version but optimized for mobile devices.

## Features

- **Authentication**: Login and registration with role-based access (Admin, Organizer, Contributor)
- **Recipe Books**: View, create, and manage recipe books
- **Recipes**: Create, edit, and view recipes with ingredients and instructions
- **Role-based Navigation**: Different interfaces for different user roles
- **Offline Support**: Uses React Query for caching and offline functionality
- **Native Mobile Experience**: Touch-optimized UI with native navigation

## Tech Stack

- **React Native**: Cross-platform mobile development
- **Expo**: Development platform and tools
- **TypeScript**: Type safety and better development experience
- **React Navigation**: Native navigation for iOS and Android
- **React Query**: Data fetching, caching, and synchronization
- **React Hook Form**: Form handling and validation
- **Zod**: Schema validation
- **React Native Paper**: Material Design components
- **AsyncStorage**: Local data persistence
- **Vector Icons**: Icon library

## Project Structure

```
mobile/
├── src/
│   ├── components/          # Reusable UI components
│   ├── screens/            # Screen components
│   │   ├── auth/           # Authentication screens
│   │   └── ...             # Other screens
│   ├── navigation/         # Navigation configuration
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API services
│   ├── lib/                # Utilities and constants
│   └── types/              # TypeScript type definitions
├── assets/                 # Images, fonts, and other assets
├── App.tsx                 # Main app component
└── package.json           # Dependencies and scripts
```

## Key Components

### Authentication
- **LoginScreen**: User login with email and password
- **RegisterScreen**: User registration with role selection
- **useAuth Hook**: Authentication state management

### Navigation
- **AppNavigator**: Main navigation container
- **MainTabNavigator**: Bottom tab navigation for authenticated users
- **AuthNavigator**: Stack navigation for authentication screens

### Screens
- **RecipeBooksScreen**: List of recipe books with search and filtering
- **RecipeBookDetailScreen**: Individual recipe book details
- **CreateRecipeBookScreen**: Form to create new recipe books
- **CreateRecipeScreen**: Form to create new recipes
- **ProfileScreen**: User profile and logout functionality

### Services
- **API Service**: HTTP client for backend communication
- **Query Client**: React Query configuration for data management

## API Integration

The mobile app connects to the same backend API as the web version:
- **Development**: `http://localhost:5000/api`
- **Production**: `https://storyworth.onrender.com/api`

## Styling

The app uses a consistent design system that matches the web version:
- **Colors**: Custom color palette matching the web theme
- **Typography**: Consistent font sizes and weights
- **Spacing**: Standardized spacing values
- **Components**: Reusable styled components

## Getting Started

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Start the Development Server**:
   ```bash
   npm start
   ```

3. **Run on Device/Simulator**:
   ```bash
   npm run android  # For Android
   npm run ios      # For iOS (macOS only)
   npm run web      # For web browser
   ```

## Development Scripts

- `npm start`: Start the Expo development server
- `npm run android`: Run on Android device/emulator
- `npm run ios`: Run on iOS device/simulator
- `npm run web`: Run in web browser

## Features Implemented

✅ **Core Features**:
- User authentication (login/register)
- Recipe books listing with search and filtering
- Role-based navigation and access control
- Responsive mobile UI
- API integration with React Query
- Local storage for authentication tokens

✅ **UI/UX**:
- Native mobile navigation
- Touch-optimized interface
- Loading states and error handling
- Consistent theming with web version
- Material Design components

## Features To Be Implemented

🚧 **Upcoming Features**:
- Recipe book detail view with recipes list
- Recipe creation and editing forms
- Image upload and camera integration
- Recipe scanning with OCR
- Push notifications
- Offline recipe viewing
- Recipe sharing functionality
- Advanced search and filtering
- User profile management
- Settings and preferences

## Architecture Decisions

1. **Expo**: Chosen for faster development and easier deployment
2. **React Navigation**: Native navigation for better performance
3. **React Query**: Robust data fetching and caching solution
4. **TypeScript**: Type safety and better developer experience
5. **React Native Paper**: Consistent Material Design components
6. **Modular Structure**: Organized codebase for maintainability

## Deployment

The app can be deployed using:
- **Expo Application Services (EAS)**: For app store deployment
- **Expo Go**: For development and testing
- **Standalone Builds**: For custom native builds

## Contributing

1. Follow the existing code structure and naming conventions
2. Use TypeScript for all new components
3. Implement proper error handling and loading states
4. Test on both iOS and Android platforms
5. Maintain consistency with the web version's design and functionality

## Notes

- The mobile app shares the same backend API with the web version
- Authentication tokens are stored securely using AsyncStorage
- The app follows React Native best practices for performance
- All screens are responsive and work on different screen sizes
- The navigation structure mirrors the web version's routing
