import React, { useEffect, useState } from 'react';
import { useAuth } from '../hooks/use-auth';

// For React Native, we'll create a placeholder since Intercom has its own RN SDK
// This maintains compatibility with the web version

interface IntercomChatProps {
  appId?: string;
}

export function IntercomChat({ appId }: IntercomChatProps) {
  const { user } = useAuth();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // In a real implementation, you would use @intercom/intercom-react-native
    // For now, we'll just log that Intercom would be initialized
    console.log('Intercom would be initialized for mobile with user:', user?.email);
    setIsLoaded(true);
  }, [user, appId]);

  // This component doesn't render anything visible in React Native
  // The Intercom widget would be handled by the native SDK
  return null;
}

// Hook to check if Intercom should be enabled
export function useIntercomChat() {
  // For mobile, we'll check if Intercom is configured
  // In a real app, this would check for the Intercom app ID
  return false; // Disabled for now since we don't have the native SDK
}

// Utility functions for Intercom API (mobile placeholders)
export const IntercomAPI = {
  // Show the messenger
  show: () => {
    console.log('Intercom show called (mobile)');
  },

  // Hide the messenger
  hide: () => {
    console.log('Intercom hide called (mobile)');
  },

  // Show a specific message
  showNewMessage: (message?: string) => {
    console.log('Intercom showNewMessage called (mobile):', message);
  },

  // Track an event
  trackEvent: (eventName: string, metadata?: Record<string, any>) => {
    console.log('Intercom trackEvent called (mobile):', eventName, metadata);
  },

  // Update user information
  updateUser: (userData: Record<string, any>) => {
    console.log('Intercom updateUser called (mobile):', userData);
  },

  // Get unread conversation count
  getUnreadCount: () => {
    return 0;
  }
};
