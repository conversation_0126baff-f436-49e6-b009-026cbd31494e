import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { API_URL } from '../lib/constants';
import { useToast } from '../hooks/use-toast';

interface RecipeActionsProps {
  recipe: {
    id: number;
    title: string;
  };
  onRecipeDeleted: () => void;
  isOrganizer: boolean;
  isAdmin: boolean;
}

export function RecipeActions({ recipe, onRecipeDeleted, isOrganizer, isAdmin }: RecipeActionsProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [rejectionMessage, setRejectionMessage] = useState('');
  const { toast } = useToast();

  const handleDelete = async () => {
    Alert.alert(
      'Delete Recipe',
      `Are you sure you want to delete "${recipe.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            setIsDeleting(true);
            try {
              const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}`, {
                method: 'DELETE',
                headers: {
                  Authorization: `Bearer ${localStorage.getItem('token')}`,
                },
              });

              if (!response.ok) {
                throw new Error('Failed to delete recipe');
              }

              toast({
                title: 'Success',
                description: 'Recipe deleted successfully',
              });
              onRecipeDeleted();
            } catch (error) {
              console.error('Error deleting recipe:', error);
              toast({
                title: 'Error',
                description: error instanceof Error ? error.message : 'Failed to delete recipe',
                variant: 'destructive',
              });
            } finally {
              setIsDeleting(false);
            }
          }
        }
      ]
    );
  };

  const handleReject = () => {
    Alert.prompt(
      'Reject Recipe',
      'Please provide a reason for rejecting this recipe:',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reject', 
          style: 'destructive',
          onPress: async (message) => {
            if (!message?.trim()) {
              toast({
                title: 'Error',
                description: 'Please provide a rejection message',
                variant: 'destructive',
              });
              return;
            }

            setIsRejecting(true);
            try {
              const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}/reject`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${localStorage.getItem('token')}`,
                },
                body: JSON.stringify({ rejectionMessage: message }),
              });

              if (!response.ok) {
                throw new Error('Failed to reject recipe');
              }

              toast({
                title: 'Success',
                description: 'Recipe rejected successfully',
              });
              onRecipeDeleted();
            } catch (error) {
              console.error('Error rejecting recipe:', error);
              toast({
                title: 'Error',
                description: error instanceof Error ? error.message : 'Failed to reject recipe',
                variant: 'destructive',
              });
            } finally {
              setIsRejecting(false);
            }
          }
        }
      ],
      'plain-text'
    );
  };

  if (!isOrganizer && !isAdmin) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Button
        title={isRejecting ? "Rejecting..." : "Reject"}
        onPress={handleReject}
        disabled={isRejecting}
        variant="destructive"
        style={styles.button}
      />
      
      <Button
        title={isDeleting ? "Deleting..." : "Delete"}
        onPress={handleDelete}
        disabled={isDeleting}
        variant="destructive"
        style={styles.button}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  button: {
    minWidth: 80,
  },
});
