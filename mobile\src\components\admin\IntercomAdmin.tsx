import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useToast } from '../../hooks/use-toast';
import { API_URL } from '../../lib/constants';

interface IntercomSettings {
  appId: string;
  accessToken: string;
  enabled: boolean;
}

interface SupportTicket {
  id: number;
  subject: string;
  status: string;
  priority: string;
  userEmail: string;
  createdAt: string;
  category: string;
}

export function IntercomAdmin() {
  const [settings, setSettings] = useState<IntercomSettings>({
    appId: '',
    accessToken: '',
    enabled: false
  });
  const [tickets, setTickets] = useState<SupportTicket[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadSettings();
    loadTickets();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch(`${API_URL}/admin/intercom/settings`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      }
    } catch (error) {
      console.error('Error loading Intercom settings:', error);
    }
  };

  const loadTickets = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/admin/support/tickets`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTickets(data.tickets || []);
      }
    } catch (error) {
      console.error('Error loading support tickets:', error);
      toast({
        title: 'Error',
        description: 'Failed to load support tickets',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    setIsSaving(true);
    try {
      const response = await fetch(`${API_URL}/admin/intercom/settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Intercom settings saved successfully',
        });
      } else {
        throw new Error('Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving Intercom settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save Intercom settings',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const closeTicket = async (ticketId: number) => {
    Alert.alert(
      'Close Ticket',
      'Are you sure you want to close this support ticket?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Close',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await fetch(`${API_URL}/admin/support/tickets/${ticketId}/close`, {
                method: 'POST',
                headers: {
                  Authorization: `Bearer ${localStorage.getItem('token')}`,
                },
              });

              if (response.ok) {
                toast({
                  title: 'Success',
                  description: 'Ticket closed successfully',
                });
                loadTickets(); // Refresh the list
              } else {
                throw new Error('Failed to close ticket');
              }
            } catch (error) {
              console.error('Error closing ticket:', error);
              toast({
                title: 'Error',
                description: 'Failed to close ticket',
                variant: 'destructive',
              });
            }
          }
        }
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return '#ef4444';
      case 'in_progress':
        return '#f59e0b';
      case 'resolved':
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return '#ef4444';
      case 'medium':
        return '#f59e0b';
      case 'low':
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Intercom & Support Management</Text>

        {/* Intercom Settings */}
        <Card style={styles.card}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Intercom Configuration</Text>
            
            <View style={styles.field}>
              <Label>App ID</Label>
              <Input
                value={settings.appId}
                onChangeText={(value) => setSettings(prev => ({ ...prev, appId: value }))}
                placeholder="Enter Intercom App ID"
              />
            </View>

            <View style={styles.field}>
              <Label>Access Token</Label>
              <Input
                value={settings.accessToken}
                onChangeText={(value) => setSettings(prev => ({ ...prev, accessToken: value }))}
                placeholder="Enter Intercom Access Token"
                secureTextEntry
              />
            </View>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Enable Intercom</Text>
              <Button
                title={settings.enabled ? 'Enabled' : 'Disabled'}
                onPress={() => setSettings(prev => ({ ...prev, enabled: !prev.enabled }))}
                variant={settings.enabled ? 'default' : 'outline'}
                size="sm"
              />
            </View>

            <Button
              title={isSaving ? 'Saving...' : 'Save Settings'}
              onPress={saveSettings}
              disabled={isSaving}
              style={styles.saveButton}
            />
          </View>
        </Card>

        {/* Support Tickets */}
        <Card style={styles.card}>
          <View style={styles.cardContent}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Support Tickets</Text>
              <Button
                title="Refresh"
                onPress={loadTickets}
                variant="outline"
                size="sm"
                disabled={isLoading}
              />
            </View>

            {isLoading ? (
              <Text style={styles.loadingText}>Loading tickets...</Text>
            ) : tickets.length === 0 ? (
              <Text style={styles.emptyText}>No support tickets found</Text>
            ) : (
              <View style={styles.ticketsList}>
                {tickets.map((ticket) => (
                  <View key={ticket.id} style={styles.ticketCard}>
                    <View style={styles.ticketHeader}>
                      <Text style={styles.ticketSubject}>{ticket.subject}</Text>
                      <View style={styles.ticketBadges}>
                        <View style={[styles.badge, { backgroundColor: getStatusColor(ticket.status) }]}>
                          <Text style={styles.badgeText}>{ticket.status}</Text>
                        </View>
                        <View style={[styles.badge, { backgroundColor: getPriorityColor(ticket.priority) }]}>
                          <Text style={styles.badgeText}>{ticket.priority}</Text>
                        </View>
                      </View>
                    </View>
                    
                    <View style={styles.ticketMeta}>
                      <Text style={styles.metaText}>From: {ticket.userEmail}</Text>
                      <Text style={styles.metaText}>Category: {ticket.category}</Text>
                      <Text style={styles.metaText}>
                        Created: {new Date(ticket.createdAt).toLocaleDateString()}
                      </Text>
                    </View>

                    {ticket.status !== 'closed' && (
                      <Button
                        title="Close Ticket"
                        onPress={() => closeTicket(ticket.id)}
                        variant="destructive"
                        size="sm"
                        style={styles.closeButton}
                      />
                    )}
                  </View>
                ))}
              </View>
            )}
          </View>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    color: '#111827',
  },
  card: {
    marginBottom: 24,
  },
  cardContent: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  field: {
    marginBottom: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
  },
  saveButton: {
    marginTop: 8,
  },
  loadingText: {
    textAlign: 'center',
    color: '#6b7280',
    fontSize: 16,
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    color: '#6b7280',
    fontSize: 16,
    padding: 20,
  },
  ticketsList: {
    gap: 16,
  },
  ticketCard: {
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  ticketHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  ticketSubject: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    flex: 1,
    marginRight: 12,
  },
  ticketBadges: {
    flexDirection: 'row',
    gap: 8,
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  ticketMeta: {
    marginBottom: 12,
  },
  metaText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  closeButton: {
    alignSelf: 'flex-start',
  },
});
