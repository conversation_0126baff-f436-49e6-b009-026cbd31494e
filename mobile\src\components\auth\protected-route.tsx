import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useAuth } from '../../hooks/use-auth';
import { UserRole } from '../../lib/constants';
import { Colors, Spacing } from '../../lib/constants';
import { Button } from '../ui/button';
import { useLocation } from '../../lib/router';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles: (typeof UserRole[keyof typeof UserRole])[];
}

export function ProtectedRoute({ children, allowedRoles }: ProtectedRouteProps) {
  const { user, isLoading } = useAuth();
  const [, setLocation] = useLocation();

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Authentication Required</Text>
        <Text style={styles.message}>Please sign in to access this page.</Text>
        <Button onPress={() => setLocation('/login')}>
          Sign In
        </Button>
      </View>
    );
  }

  if (!allowedRoles.includes(user.role)) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Access Denied</Text>
        <Text style={styles.message}>
          You don't have permission to access this page.
        </Text>
        <Button onPress={() => setLocation('/recipe-books')}>
          Go to Recipe Books
        </Button>
      </View>
    );
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
    backgroundColor: Colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  loadingText: {
    fontSize: 18,
    color: Colors.foreground,
  },
});
