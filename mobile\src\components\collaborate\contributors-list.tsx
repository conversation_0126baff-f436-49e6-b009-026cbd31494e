import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card } from "../ui/card";

type Contributor = {
  initials: string;
  name: string;
  email: string;
  role: "Organizer" | "Contributor";
  color: string;
};

const contributors: Contributor[] = [
  {
    initials: "<PERSON><PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Organizer",
    color: "#6b7280",
  },
  {
    initials: "TS",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Contributor",
    color: "#3b82f6",
  },
  {
    initials: "<PERSON>",
    name: "Aunt <PERSON>",
    email: "<EMAIL>",
    role: "Contributor",
    color: "#8b5cf6",
  },
];

export function ContributorsList() {
  return (
    <Card style={styles.card}>
      <View style={styles.content}>
        <Text style={styles.title}>Current Contributors</Text>
        
        <View style={styles.list}>
          {contributors.map((contributor, index) => (
            <View 
              key={index}
              style={[
                styles.contributorItem,
                index < contributors.length - 1 && styles.borderBottom
              ]}
            >
              <View style={styles.contributorInfo}>
                <View style={[styles.avatar, { backgroundColor: contributor.color }]}>
                  <Text style={styles.initials}>{contributor.initials}</Text>
                </View>
                <View style={styles.details}>
                  <Text style={styles.name}>{contributor.name}</Text>
                  <Text style={styles.email}>{contributor.email}</Text>
                </View>
              </View>
              <View style={[
                styles.badge,
                contributor.role === "Organizer" 
                  ? styles.organizerBadge 
                  : styles.contributorBadge
              ]}>
                <Text style={[
                  styles.badgeText,
                  contributor.role === "Organizer" 
                    ? styles.organizerBadgeText 
                    : styles.contributorBadgeText
                ]}>
                  {contributor.role}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 24,
  },
  content: {
    padding: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    fontFamily: 'serif',
  },
  list: {
    gap: 16,
  },
  contributorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingBottom: 12,
  },
  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  contributorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  initials: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  details: {
    flex: 1,
  },
  name: {
    fontWeight: '500',
    fontSize: 16,
    marginBottom: 2,
  },
  email: {
    fontSize: 12,
    color: '#6b7280',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    borderWidth: 1,
  },
  organizerBadge: {
    backgroundColor: '#dbeafe',
    borderColor: '#3b82f6',
  },
  contributorBadge: {
    backgroundColor: '#f3f4f6',
    borderColor: '#6b7280',
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  organizerBadgeText: {
    color: '#3b82f6',
  },
  contributorBadgeText: {
    color: '#6b7280',
  },
});
