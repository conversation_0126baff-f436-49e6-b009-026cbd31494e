import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { useQuery } from "@tanstack/react-query";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Input } from "../ui/input";
import { useAuth } from "../../hooks/use-auth";
import { useToast } from "../../hooks/use-toast";
import { API_URL } from '../../lib/constants';

const API_BASE = API_URL;

interface Project {
  id: number;
  name: string;
  description: string;
}

export function InviteForm() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [selectedProject, setSelectedProject] = useState<string>("");
  const [emails, setEmails] = useState<string>("<EMAIL>");
  const [deadline, setDeadline] = useState<string>(() => {
    const date = new Date();
    date.setMinutes(date.getMinutes() + 10);
    return date.toISOString().slice(0, 16);
  });
  const [reminderFrequency, setReminderFrequency] = useState<string>("1min");
  const [isLoading, setIsLoading] = useState(false);

  // Fetch only the organizer's projects
  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ['organizer-projects'],
    queryFn: async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_BASE}/organizer/my-projects`, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe books');
      }

      const data = await response.json();
      return data.projects || [];
    },
    enabled: !!user && user.role === 'organizer'
  });

  const handleSubmit = async () => {
    if (!selectedProject || !emails.trim()) return;

    setIsLoading(true);
    try {
      const emailList = emails.split(",").map(email => email.trim());
      
      for (const email of emailList) {
        const response = await fetch(`${API_BASE}/organizer/projects/${selectedProject}/invite`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            email,
            deadline: deadline || undefined,
            reminderFrequency: "1min"
          }),
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || "Failed to send invitation");
        }
      }

      toast({
        title: "Success",
        description: "Invitations sent successfully!"
      });
      setEmails("");
      setDeadline("");
      setReminderFrequency("weekly");
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitations",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.title}>Invite Contributors</Text>
      </View>
      <View style={styles.content}>
        <View style={styles.formGroup}>
          <Text style={styles.label}>Select Project</Text>
          {/* Project selection would need a picker component */}
          <Text style={styles.placeholder}>Select a project</Text>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Contributor Emails</Text>
          <Input
            placeholder="Enter email addresses (comma-separated)"
            value={emails}
            onChangeText={setEmails}
            style={styles.input}
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Submission Deadline (Optional)</Text>
          <Input
            placeholder="Select deadline"
            value={deadline}
            onChangeText={setDeadline}
            style={styles.input}
          />
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Reminder Frequency</Text>
          <Text style={styles.placeholder}>Weekly</Text>
        </View>
        
        <View style={styles.footer}>
          <Text style={styles.footerText}>You can invite up to 5 more contributors</Text>
          <Button 
            title={isLoading ? "Sending..." : "Send Invitations"}
            onPress={handleSubmit}
            disabled={!selectedProject || !emails.trim() || isLoading}
          />
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    marginBottom: 0,
  },
  placeholder: {
    padding: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 6,
    color: '#6b7280',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  footerText: {
    fontSize: 12,
    color: '#6b7280',
    flex: 1,
  },
});
