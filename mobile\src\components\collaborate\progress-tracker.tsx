import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card } from "../ui/card";

type ProgressData = {
  name: string;
  progress: number;
};

const progressData: ProgressData[] = [
  { name: "Overall Completion", progress: 65 },
  { name: "<PERSON>", progress: 100 },
  { name: "<PERSON> <PERSON>", progress: 75 },
  { name: "<PERSON>", progress: 25 },
];

export function ProgressTracker() {
  return (
    <Card style={styles.card}>
      <View style={styles.content}>
        <Text style={styles.title}>Submission Progress</Text>
        
        <View style={styles.progressList}>
          {progressData.map((item, index) => (
            <View key={index} style={styles.progressItem}>
              <View style={styles.progressHeader}>
                <Text style={styles.progressName}>{item.name}</Text>
                <Text style={styles.progressPercent}>{item.progress}%</Text>
              </View>
              <View style={styles.progressBarContainer}>
                <View 
                  style={[
                    styles.progressBar,
                    { width: `${item.progress}%` }
                  ]}
                />
              </View>
            </View>
          ))}
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  content: {
    padding: 24,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    fontFamily: 'serif',
  },
  progressList: {
    gap: 16,
  },
  progressItem: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  progressName: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressPercent: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#3b82f6',
    borderRadius: 4,
  },
});
