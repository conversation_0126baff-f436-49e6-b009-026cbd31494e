import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from '../ui/button';

export function Buttons() {
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Buttons</Text>
      
      <View style={styles.buttonGroup}>
        <Text style={styles.groupTitle}>Primary Buttons</Text>
        <View style={styles.buttonRow}>
          <Button title="Default" />
          <Button title="Loading" disabled />
          <Button title="Disabled" disabled />
        </View>
      </View>

      <View style={styles.buttonGroup}>
        <Text style={styles.groupTitle}>Secondary Buttons</Text>
        <View style={styles.buttonRow}>
          <Button title="Secondary" variant="secondary" />
          <Button title="Loading" variant="secondary" disabled />
          <Button title="Disabled" variant="secondary" disabled />
        </View>
      </View>

      <View style={styles.buttonGroup}>
        <Text style={styles.groupTitle}>Outline Buttons</Text>
        <View style={styles.buttonRow}>
          <Button title="Outline" variant="outline" />
          <Button title="Loading" variant="outline" disabled />
          <Button title="Disabled" variant="outline" disabled />
        </View>
      </View>

      <View style={styles.buttonGroup}>
        <Text style={styles.groupTitle}>Destructive Buttons</Text>
        <View style={styles.buttonRow}>
          <Button title="Destructive" variant="destructive" />
          <Button title="Loading" variant="destructive" disabled />
          <Button title="Disabled" variant="destructive" disabled />
        </View>
      </View>

      <View style={styles.buttonGroup}>
        <Text style={styles.groupTitle}>Button Sizes</Text>
        <View style={styles.buttonColumn}>
          <Button title="Large Button" size="lg" />
          <Button title="Default Button" />
          <Button title="Small Button" size="sm" />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  section: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    fontFamily: 'serif',
  },
  buttonGroup: {
    marginBottom: 32,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    color: '#111827',
  },
  buttonRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  buttonColumn: {
    gap: 12,
  },
});
