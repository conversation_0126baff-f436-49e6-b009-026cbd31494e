import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';

export function Cards() {
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Cards</Text>
      
      <View style={styles.cardGroup}>
        <Text style={styles.groupTitle}>Basic Cards</Text>
        
        <Card style={styles.card}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Simple Card</Text>
            <Text style={styles.cardDescription}>
              This is a basic card with just text content. Cards are used to group related information together.
            </Text>
          </View>
        </Card>

        <Card style={styles.card}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Card with Badge</Text>
            <View style={styles.badgeContainer}>
              <Badge>New</Badge>
              <Badge variant="secondary">Featured</Badge>
            </View>
            <Text style={styles.cardDescription}>
              This card includes badges to highlight important information or status.
            </Text>
          </View>
        </Card>
      </View>

      <View style={styles.cardGroup}>
        <Text style={styles.groupTitle}>Cards with Actions</Text>
        
        <Card style={styles.card}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Interactive Card</Text>
            <Text style={styles.cardDescription}>
              This card includes action buttons for user interaction.
            </Text>
            <View style={styles.cardActions}>
              <Button title="Primary" size="sm" />
              <Button title="Secondary" variant="outline" size="sm" />
            </View>
          </View>
        </Card>

        <Card style={styles.card}>
          <Image 
            source={{ uri: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400' }}
            style={styles.cardImage}
          />
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Card with Image</Text>
            <Text style={styles.cardDescription}>
              This card includes an image header along with content and actions.
            </Text>
            <View style={styles.cardActions}>
              <Button title="View Details" />
            </View>
          </View>
        </Card>
      </View>

      <View style={styles.cardGroup}>
        <Text style={styles.groupTitle}>Recipe Card Example</Text>
        
        <Card style={styles.card}>
          <Image 
            source={{ uri: 'https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=400' }}
            style={styles.cardImage}
          />
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Grandma's Apple Pie</Text>
            <Text style={styles.cardDescription}>
              A classic apple pie recipe passed down through generations. Perfect for family gatherings.
            </Text>
            <View style={styles.tagContainer}>
              <Badge variant="outline">Dessert</Badge>
              <Badge variant="secondary">90 min</Badge>
              <Badge>Family Recipe</Badge>
            </View>
            <View style={styles.cardMeta}>
              <Text style={styles.metaText}>by Grandma Rose</Text>
              <Text style={styles.metaText}>⭐ 4.8 (24 reviews)</Text>
            </View>
            <View style={styles.cardActions}>
              <Button title="View Recipe" />
              <Button title="♡" variant="outline" size="sm" />
            </View>
          </View>
        </Card>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  section: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    fontFamily: 'serif',
  },
  cardGroup: {
    marginBottom: 32,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    color: '#111827',
  },
  card: {
    marginBottom: 16,
  },
  cardImage: {
    width: '100%',
    height: 200,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  cardContent: {
    padding: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#111827',
  },
  cardDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
    marginBottom: 12,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
  },
  cardMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metaText: {
    fontSize: 12,
    color: '#6b7280',
  },
  cardActions: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
});
