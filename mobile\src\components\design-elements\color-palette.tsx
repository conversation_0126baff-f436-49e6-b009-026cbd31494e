import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../lib/constants';

const colors = [
  { name: 'Primary', value: Colors.primary, description: 'Main brand color' },
  { name: 'Foreground', value: Colors.foreground, description: 'Primary text color' },
  { name: 'Background', value: Colors.background, description: 'Main background' },
  { name: 'Card', value: Colors.card, description: 'Card backgrounds' },
  { name: 'Muted', value: Colors.muted, description: 'Muted backgrounds' },
  { name: 'Muted Foreground', value: Colors.mutedForeground, description: 'Secondary text' },
  { name: 'Accent', value: Colors.accent, description: 'Accent color' },
  { name: 'Success', value: Colors.success, description: 'Success states' },
  { name: 'Warning', value: Colors.warning, description: 'Warning states' },
  { name: 'Destructive', value: Colors.destructive, description: 'Error states' },
];

export function ColorPalette() {
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Color Palette</Text>
      <View style={styles.grid}>
        {colors.map((color, index) => (
          <View key={index} style={styles.colorCard}>
            <View style={[styles.colorSwatch, { backgroundColor: color.value }]} />
            <View style={styles.colorInfo}>
              <Text style={styles.colorName}>{color.name}</Text>
              <Text style={styles.colorValue}>{color.value}</Text>
              <Text style={styles.colorDescription}>{color.description}</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  section: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    fontFamily: 'serif',
    color: Colors.foreground,
  },
  grid: {
    gap: 16,
  },
  colorCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  colorSwatch: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 16,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  colorInfo: {
    flex: 1,
  },
  colorName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
    color: Colors.foreground,
  },
  colorValue: {
    fontSize: 14,
    color: Colors.mutedForeground,
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  colorDescription: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
});
