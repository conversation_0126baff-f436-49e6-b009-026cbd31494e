import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const colors = [
  { name: 'Primary', value: '#3b82f6', description: 'Main brand color' },
  { name: 'Secondary', value: '#6b7280', description: 'Secondary brand color' },
  { name: 'Success', value: '#10b981', description: 'Success states' },
  { name: 'Warning', value: '#f59e0b', description: 'Warning states' },
  { name: 'Error', value: '#ef4444', description: 'Error states' },
  { name: 'Background', value: '#ffffff', description: 'Main background' },
  { name: 'Surface', value: '#f9fafb', description: 'Card backgrounds' },
  { name: 'Text', value: '#111827', description: 'Primary text' },
  { name: 'Text Muted', value: '#6b7280', description: 'Secondary text' },
];

export function ColorPalette() {
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Color Palette</Text>
      <View style={styles.grid}>
        {colors.map((color, index) => (
          <View key={index} style={styles.colorCard}>
            <View style={[styles.colorSwatch, { backgroundColor: color.value }]} />
            <View style={styles.colorInfo}>
              <Text style={styles.colorName}>{color.name}</Text>
              <Text style={styles.colorValue}>{color.value}</Text>
              <Text style={styles.colorDescription}>{color.description}</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  section: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    fontFamily: 'serif',
  },
  grid: {
    gap: 16,
  },
  colorCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  colorSwatch: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  colorInfo: {
    flex: 1,
  },
  colorName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  colorValue: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  colorDescription: {
    fontSize: 12,
    color: '#9ca3af',
  },
});
