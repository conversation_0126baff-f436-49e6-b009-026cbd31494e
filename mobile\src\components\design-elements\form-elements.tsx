import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Select } from '../ui/select';
import { Button } from '../ui/button';
import { Colors } from '../../lib/constants';

export function FormElements() {
  const [inputValue, setInputValue] = useState('');
  const [textareaValue, setTextareaValue] = useState('');
  const [selectValue, setSelectValue] = useState('');

  const selectOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
  ];

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Form Elements</Text>

      <View style={styles.formGroup}>
        <Text style={styles.groupTitle}>Input Fields</Text>
        <View style={styles.formContent}>
          <View style={styles.field}>
            <Label>Default Input</Label>
            <Input
              placeholder="Enter text here..."
              value={inputValue}
              onChangeText={setInputValue}
            />
          </View>

          <View style={styles.field}>
            <Label>Disabled Input</Label>
            <Input
              placeholder="Disabled input"
              value="Disabled value"
              editable={false}
            />
          </View>

          <View style={styles.field}>
            <Label>Input with Error</Label>
            <Input
              placeholder="Error state"
              value="Invalid input"
              style={styles.errorInput}
            />
            <Text style={styles.errorText}>This field has an error</Text>
          </View>
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.groupTitle}>Textarea</Text>
        <View style={styles.formContent}>
          <View style={styles.field}>
            <Label>Message</Label>
            <Textarea
              placeholder="Enter your message here..."
              value={textareaValue}
              onChangeText={setTextareaValue}
            />
          </View>
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.groupTitle}>Select Dropdown</Text>
        <View style={styles.formContent}>
          <View style={styles.field}>
            <Label>Choose an option</Label>
            <Select
              value={selectValue}
              onValueChange={setSelectValue}
              placeholder="Select an option..."
              options={selectOptions}
            />
          </View>

          <View style={styles.field}>
            <Label>Disabled Select</Label>
            <Select
              value=""
              placeholder="Disabled select"
              options={selectOptions}
              disabled
            />
          </View>
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.groupTitle}>Form Example</Text>
        <View style={styles.formContent}>
          <View style={styles.field}>
            <Label>Name</Label>
            <Input placeholder="Your name" />
          </View>

          <View style={styles.field}>
            <Label>Email</Label>
            <Input placeholder="<EMAIL>" keyboardType="email-address" />
          </View>

          <View style={styles.field}>
            <Label>Category</Label>
            <Select
              placeholder="Select category"
              options={[
                { value: 'general', label: 'General' },
                { value: 'support', label: 'Support' },
                { value: 'feedback', label: 'Feedback' },
              ]}
            />
          </View>

          <View style={styles.field}>
            <Label>Message</Label>
            <Textarea placeholder="Your message..." />
          </View>

          <Button title="Submit Form" style={styles.submitButton} />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  section: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    fontFamily: 'serif',
    color: Colors.foreground,
  },
  formGroup: {
    marginBottom: 32,
    padding: 16,
    backgroundColor: Colors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  groupTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    color: Colors.foreground,
  },
  formContent: {
    gap: 16,
  },
  field: {
    marginBottom: 16,
  },
  errorInput: {
    borderColor: Colors.destructive,
  },
  errorText: {
    fontSize: 12,
    color: Colors.destructive,
    marginTop: 4,
  },
  submitButton: {
    marginTop: 8,
  },
});
