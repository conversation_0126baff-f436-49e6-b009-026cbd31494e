import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const typographyElements = [
  { name: 'Heading 1', component: 'h1', style: styles.h1, text: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Heading 2', component: 'h2', style: styles.h2, text: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Heading 3', component: 'h3', style: styles.h3, text: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Heading 4', component: 'h4', style: styles.h4, text: 'The quick brown fox jumps over the lazy dog' },
  { name: 'Body Large', component: 'p', style: styles.bodyLarge, text: 'The quick brown fox jumps over the lazy dog. This is a longer text to show how body text looks in paragraphs.' },
  { name: 'Body', component: 'p', style: styles.body, text: 'The quick brown fox jumps over the lazy dog. This is a longer text to show how body text looks in paragraphs.' },
  { name: 'Body Small', component: 'p', style: styles.bodySmall, text: 'The quick brown fox jumps over the lazy dog. This is a longer text to show how body text looks in paragraphs.' },
  { name: 'Caption', component: 'span', style: styles.caption, text: 'The quick brown fox jumps over the lazy dog' },
];

export function Typography() {
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Typography</Text>
      <View style={styles.typographyList}>
        {typographyElements.map((element, index) => (
          <View key={index} style={styles.typographyItem}>
            <View style={styles.typographyLabel}>
              <Text style={styles.labelText}>{element.name}</Text>
            </View>
            <Text style={element.style}>{element.text}</Text>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  section: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    fontFamily: 'serif',
  },
  typographyList: {
    gap: 24,
  },
  typographyItem: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  typographyLabel: {
    marginBottom: 8,
  },
  labelText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  h1: {
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 40,
    fontFamily: 'serif',
    color: '#111827',
  },
  h2: {
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 36,
    fontFamily: 'serif',
    color: '#111827',
  },
  h3: {
    fontSize: 24,
    fontWeight: '600',
    lineHeight: 32,
    fontFamily: 'serif',
    color: '#111827',
  },
  h4: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 28,
    color: '#111827',
  },
  bodyLarge: {
    fontSize: 18,
    lineHeight: 28,
    color: '#374151',
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
    color: '#374151',
  },
  bodySmall: {
    fontSize: 14,
    lineHeight: 20,
    color: '#374151',
  },
  caption: {
    fontSize: 12,
    lineHeight: 16,
    color: '#6b7280',
  },
});
