import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../lib/constants';



export function Typography() {
  const typographyElements = [
    { name: 'Heading 1', styleKey: 'h1', text: 'The quick brown fox jumps over the lazy dog' },
    { name: 'Heading 2', styleKey: 'h2', text: 'The quick brown fox jumps over the lazy dog' },
    { name: 'Heading 3', styleKey: 'h3', text: 'The quick brown fox jumps over the lazy dog' },
    { name: 'Heading 4', styleKey: 'h4', text: 'The quick brown fox jumps over the lazy dog' },
    { name: 'Body Large', styleKey: 'bodyLarge', text: 'The quick brown fox jumps over the lazy dog. This is a longer text to show how body text looks in paragraphs.' },
    { name: 'Body', styleKey: 'body', text: 'The quick brown fox jumps over the lazy dog. This is a longer text to show how body text looks in paragraphs.' },
    { name: 'Body Small', styleKey: 'bodySmall', text: 'The quick brown fox jumps over the lazy dog. This is a longer text to show how body text looks in paragraphs.' },
    { name: 'Capt<PERSON>', styleKey: 'caption', text: 'The quick brown fox jumps over the lazy dog' },
  ];

  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Typography</Text>
      <View style={styles.typographyList}>
        {typographyElements.map((element, index) => (
          <View key={index} style={styles.typographyItem}>
            <View style={styles.typographyLabel}>
              <Text style={styles.labelText}>{element.name}</Text>
            </View>
            <Text style={styles[element.styleKey as keyof typeof styles]}>{element.text}</Text>
          </View>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  section: {
    marginBottom: 48,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 24,
    fontFamily: 'serif',
    color: Colors.foreground,
  },
  typographyList: {
    gap: 24,
  },
  typographyItem: {
    padding: 16,
    backgroundColor: Colors.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  typographyLabel: {
    marginBottom: 8,
  },
  labelText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.mutedForeground,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  h1: {
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 40,
    fontFamily: 'serif',
    color: Colors.foreground,
  },
  h2: {
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 36,
    fontFamily: 'serif',
    color: Colors.foreground,
  },
  h3: {
    fontSize: 24,
    fontWeight: '600',
    lineHeight: 32,
    fontFamily: 'serif',
    color: Colors.foreground,
  },
  h4: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 28,
    color: Colors.foreground,
  },
  bodyLarge: {
    fontSize: 18,
    lineHeight: 28,
    color: Colors.foreground,
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
    color: Colors.foreground,
  },
  bodySmall: {
    fontSize: 14,
    lineHeight: 20,
    color: Colors.foreground,
  },
  caption: {
    fontSize: 12,
    lineHeight: 16,
    color: Colors.mutedForeground,
  },
});
