import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Spacing, BorderRadius, UserRole, API_URL } from '../../lib/constants';
import { useAuth } from '../../hooks/use-auth';
import { useLocation } from '../../lib/router';
import { useToast } from '../../hooks/use-toast';
import { Notifications } from '../Notifications';
import Icon from 'react-native-vector-icons/MaterialIcons';

export function Header() {
  const { user, logout } = useAuth();
  const [location, setLocation] = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { toast } = useToast();

  const navigation = [
    { href: "/recipe-books", label: "Recipe Books", icon: "book", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/recipe-books/create", label: "Create Book", icon: "add-box", roles: [UserRole.ADMIN, UserRole.ORGANIZER] },
    { href: "/recipes/create", label: "Add Recipe", icon: "restaurant", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/order-tracking", label: "Order Tracking", icon: "local-shipping", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/pricing-calculator", label: "Pricing", icon: "calculate", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/support", label: "Support", icon: "support", roles: [UserRole.ADMIN, UserRole.ORGANIZER, UserRole.CONTRIBUTOR] },
    { href: "/admin", label: "Admin Panel", icon: "admin-panel-settings", roles: [UserRole.ADMIN] },
    { href: "/admin/dashboard", label: "Recipe Approvals", icon: "approval", roles: [UserRole.ADMIN, UserRole.ORGANIZER] },
    { href: "/organizer", label: "Organizer Dashboard", icon: "dashboard", roles: [UserRole.ORGANIZER] },
    { href: "/organizer/recipe-approvals", label: "Pending Recipes", icon: "pending-actions", roles: [UserRole.ORGANIZER] },
    { href: "/contributor/dashboard", label: "My Recipes", icon: "person", roles: [UserRole.CONTRIBUTOR] },
  ];

  const filteredNavigation = navigation.filter(item => {
    if (!user?.role) return false;
    return item.roles.includes(user.role as UserRole);
  });

  const isActivePath = (path: string) => {
    return location === path;
  };

  const handleNavigation = (href: string) => {
    setLocation(href);
    setIsMenuOpen(false);
  };

  const handleDeleteAccount = async () => {
    Alert.alert(
      'Delete Account',
      'Are you absolutely sure? This action cannot be undone. This will permanently delete your account and remove your data from our servers.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Account',
          style: 'destructive',
          onPress: async () => {
            try {
              const token = await AsyncStorage.getItem('token');
              const response = await fetch(`${API_URL}/auth/me`, {
                method: 'DELETE',
                headers: {
                  'Authorization': `Bearer ${token}`
                }
              });

              if (!response.ok) {
                throw new Error('Failed to delete account');
              }

              logout();
              setIsUserMenuOpen(false);
              toast({
                title: "Success",
                description: "Account deleted successfully",
              });
            } catch (error) {
              toast({
                title: "Error",
                description: "Failed to delete account",
                variant: "destructive",
              });
            }
          }
        }
      ]
    );
  };

  const handleLogout = () => {
    logout();
    setIsUserMenuOpen(false);
    setLocation('/login');
  };

  return (
    <View style={styles.header}>
      {/* Logo */}
      <TouchableOpacity onPress={() => setLocation('/recipe-books')} style={styles.logoContainer}>
        <Icon name="book" size={24} color={Colors.primary} />
        <Text style={styles.title}>RecipeBook</Text>
      </TouchableOpacity>

      {user && (
        <View style={styles.headerActions}>
          {/* Notifications */}
          <Notifications />

          {/* Navigation Menu Button */}
          <TouchableOpacity
            onPress={() => setIsMenuOpen(true)}
            style={styles.menuButton}
          >
            <Icon name="menu" size={24} color={Colors.foreground} />
          </TouchableOpacity>

          {/* User Menu Button */}
          <TouchableOpacity
            onPress={() => setIsUserMenuOpen(true)}
            style={styles.userButton}
          >
            <Icon name="person" size={24} color={Colors.foreground} />
          </TouchableOpacity>
        </View>
      )}

      {/* Navigation Menu Modal */}
      <Modal
        visible={isMenuOpen}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsMenuOpen(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.menuModal}>
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>Navigation</Text>
              <TouchableOpacity
                onPress={() => setIsMenuOpen(false)}
                style={styles.closeButton}
              >
                <Icon name="close" size={24} color={Colors.foreground} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.menuContent}>
              {filteredNavigation.map((item) => (
                <TouchableOpacity
                  key={item.href}
                  style={[
                    styles.menuItem,
                    isActivePath(item.href) && styles.activeMenuItem
                  ]}
                  onPress={() => handleNavigation(item.href)}
                >
                  <Icon
                    name={item.icon}
                    size={20}
                    color={isActivePath(item.href) ? Colors.primary : Colors.foreground}
                  />
                  <Text style={[
                    styles.menuItemText,
                    isActivePath(item.href) && styles.activeMenuItemText
                  ]}>
                    {item.label}
                  </Text>
                  {isActivePath(item.href) && (
                    <Icon name="check" size={16} color={Colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* User Menu Modal */}
      <Modal
        visible={isUserMenuOpen}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setIsUserMenuOpen(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.userModal}>
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>My Account</Text>
              <TouchableOpacity
                onPress={() => setIsUserMenuOpen(false)}
                style={styles.closeButton}
              >
                <Icon name="close" size={24} color={Colors.foreground} />
              </TouchableOpacity>
            </View>

            <View style={styles.userInfo}>
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{user?.name}</Text>
                <Text style={styles.userEmail}>{user?.email}</Text>
                <View style={[
                  styles.roleBadge,
                  user?.role === 'admin' ? styles.roleAdmin :
                  user?.role === 'organizer' ? styles.roleOrganizer :
                  styles.roleContributor
                ]}>
                  <Text style={[
                    styles.roleText,
                    user?.role === 'admin' ? styles.roleAdminText :
                    user?.role === 'organizer' ? styles.roleOrganizerText :
                    styles.roleContributorText
                  ]}>{user?.role}</Text>
                </View>
              </View>
            </View>

            <View style={styles.userMenuContent}>
              <TouchableOpacity
                style={styles.userMenuItem}
                onPress={() => {
                  setIsUserMenuOpen(false);
                  setLocation('/settings');
                }}
              >
                <Icon name="settings" size={20} color={Colors.foreground} />
                <Text style={styles.userMenuItemText}>Settings</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.userMenuItem}
                onPress={handleLogout}
              >
                <Icon name="logout" size={20} color={Colors.foreground} />
                <Text style={styles.userMenuItemText}>Log out</Text>
              </TouchableOpacity>

              <View style={styles.separator} />

              <TouchableOpacity
                style={[styles.userMenuItem, styles.dangerMenuItem]}
                onPress={handleDeleteAccount}
              >
                <Icon name="delete" size={20} color={Colors.destructive} />
                <Text style={[styles.userMenuItemText, styles.dangerText]}>Delete Account</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  menuButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
  },
  userButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  menuModal: {
    backgroundColor: Colors.card,
    borderTopLeftRadius: BorderRadius.lg,
    borderTopRightRadius: BorderRadius.lg,
    maxHeight: '80%',
    minHeight: '50%',
  },
  userModal: {
    backgroundColor: Colors.card,
    borderTopLeftRadius: BorderRadius.lg,
    borderTopRightRadius: BorderRadius.lg,
    maxHeight: '70%',
    minHeight: '40%',
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  menuTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  closeButton: {
    padding: Spacing.sm,
  },
  menuContent: {
    flex: 1,
    padding: Spacing.md,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    padding: Spacing.lg,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.sm,
  },
  activeMenuItem: {
    backgroundColor: Colors.primary + '20',
  },
  menuItemText: {
    fontSize: 16,
    color: Colors.foreground,
    flex: 1,
  },
  activeMenuItemText: {
    color: Colors.primary,
    fontWeight: '500',
  },
  userInfo: {
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  userDetails: {
    gap: Spacing.sm,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  userEmail: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  roleBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    alignSelf: 'flex-start',
    marginTop: Spacing.xs,
  },
  roleAdmin: {
    backgroundColor: '#f3e8ff',
  },
  roleOrganizer: {
    backgroundColor: '#dbeafe',
  },
  roleContributor: {
    backgroundColor: '#f3f4f6',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  roleAdminText: {
    color: '#7c3aed',
  },
  roleOrganizerText: {
    color: '#2563eb',
  },
  roleContributorText: {
    color: '#6b7280',
  },
  userMenuContent: {
    padding: Spacing.md,
  },
  userMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    padding: Spacing.lg,
    borderRadius: BorderRadius.md,
    marginBottom: Spacing.sm,
  },
  dangerMenuItem: {
    backgroundColor: Colors.destructive + '10',
  },
  userMenuItemText: {
    fontSize: 16,
    color: Colors.foreground,
  },
  dangerText: {
    color: Colors.destructive,
  },
  separator: {
    height: 1,
    backgroundColor: Colors.border,
    marginVertical: Spacing.md,
  },
});
