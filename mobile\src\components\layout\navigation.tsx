import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useLocation } from '../../lib/router';
import { Button } from '../ui/button';
import { useAuth } from '../../hooks/use-auth';

export function Navigation() {
  const { user } = useAuth();
  const [, setLocation] = useLocation();

  const navigateTo = (path: string) => {
    setLocation(path);
  };

  return (
    <View style={styles.nav}>
      <View style={styles.container}>
        <View style={styles.content}>
          <TouchableOpacity onPress={() => navigateTo('/')}>
            <Text style={styles.logo}>Recipe Book</Text>
          </TouchableOpacity>

          <View style={styles.navItems}>
            {user?.role === 'contributor' && (
              <Button
                title="My Contributions"
                onPress={() => navigateTo('/contributor/dashboard')}
                variant="ghost"
                size="sm"
              />
            )}
            
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <>
                <Button
                  title="Recipe Approvals"
                  onPress={() => navigateTo('/organizer/recipe-approvals')}
                  variant="ghost"
                  size="sm"
                />
                <Button
                  title="Recipe Books"
                  onPress={() => navigateTo('/recipe-books')}
                  variant="ghost"
                  size="sm"
                />
              </>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  nav: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  container: {
    paddingHorizontal: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 64,
  },
  logo: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  navItems: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
});
