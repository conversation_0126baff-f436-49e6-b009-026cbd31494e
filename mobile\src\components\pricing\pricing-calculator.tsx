import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select } from '../ui/select';

interface PricingTier {
  name: string;
  basePrice: number;
  maxRecipes: number;
  features: string[];
}

const pricingTiers: PricingTier[] = [
  {
    name: 'Basic',
    basePrice: 29.99,
    maxRecipes: 25,
    features: ['Up to 25 recipes', 'Basic templates', 'PDF download']
  },
  {
    name: 'Standard',
    basePrice: 49.99,
    maxRecipes: 50,
    features: ['Up to 50 recipes', 'Premium templates', 'PDF download', 'Print-ready format']
  },
  {
    name: 'Premium',
    basePrice: 79.99,
    maxRecipes: 100,
    features: ['Up to 100 recipes', 'All templates', 'PDF download', 'Print-ready format', 'Custom cover design']
  }
];

const bookSizes = [
  { value: 'small', label: 'Small (6x9 inches)', multiplier: 1.0 },
  { value: 'medium', label: 'Medium (8x10 inches)', multiplier: 1.2 },
  { value: 'large', label: 'Large (8.5x11 inches)', multiplier: 1.4 }
];

const bindingTypes = [
  { value: 'spiral', label: 'Spiral Binding', multiplier: 1.0 },
  { value: 'perfect', label: 'Perfect Binding', multiplier: 1.3 },
  { value: 'hardcover', label: 'Hardcover', multiplier: 1.8 }
];

export function PricingCalculator() {
  const [selectedTier, setSelectedTier] = useState<PricingTier>(pricingTiers[0]);
  const [recipeCount, setRecipeCount] = useState('25');
  const [bookSize, setBookSize] = useState('medium');
  const [bindingType, setBindingType] = useState('perfect');
  const [quantity, setQuantity] = useState('1');

  const calculatePrice = () => {
    const recipes = parseInt(recipeCount) || 0;
    const qty = parseInt(quantity) || 1;
    
    // Find appropriate tier based on recipe count
    const tier = pricingTiers.find(t => recipes <= t.maxRecipes) || pricingTiers[pricingTiers.length - 1];
    
    // Get multipliers
    const sizeMultiplier = bookSizes.find(s => s.value === bookSize)?.multiplier || 1.0;
    const bindingMultiplier = bindingTypes.find(b => b.value === bindingType)?.multiplier || 1.0;
    
    // Calculate base price
    let basePrice = tier.basePrice;
    
    // Add extra cost for recipes over tier limit
    if (recipes > tier.maxRecipes) {
      const extraRecipes = recipes - tier.maxRecipes;
      basePrice += extraRecipes * 0.5; // $0.50 per extra recipe
    }
    
    // Apply multipliers
    const unitPrice = basePrice * sizeMultiplier * bindingMultiplier;
    
    // Quantity discounts
    let quantityDiscount = 1.0;
    if (qty >= 10) quantityDiscount = 0.9; // 10% off for 10+
    if (qty >= 25) quantityDiscount = 0.85; // 15% off for 25+
    if (qty >= 50) quantityDiscount = 0.8; // 20% off for 50+
    
    const totalPrice = unitPrice * qty * quantityDiscount;
    
    return {
      unitPrice: unitPrice.toFixed(2),
      totalPrice: totalPrice.toFixed(2),
      tier: tier.name,
      discount: qty >= 10 ? Math.round((1 - quantityDiscount) * 100) : 0
    };
  };

  const pricing = calculatePrice();

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Pricing Calculator</Text>
        <Text style={styles.subtitle}>
          Calculate the cost of your custom recipe book
        </Text>

        <Card style={styles.card}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Book Configuration</Text>

            <View style={styles.field}>
              <Label>Number of Recipes</Label>
              <Input
                value={recipeCount}
                onChangeText={setRecipeCount}
                placeholder="25"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.field}>
              <Label>Book Size</Label>
              <Select
                value={bookSize}
                onValueChange={setBookSize}
                options={bookSizes}
              />
            </View>

            <View style={styles.field}>
              <Label>Binding Type</Label>
              <Select
                value={bindingType}
                onValueChange={setBindingType}
                options={bindingTypes}
              />
            </View>

            <View style={styles.field}>
              <Label>Quantity</Label>
              <Input
                value={quantity}
                onChangeText={setQuantity}
                placeholder="1"
                keyboardType="numeric"
              />
            </View>
          </View>
        </Card>

        <Card style={styles.card}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Pricing Breakdown</Text>

            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Recommended Tier:</Text>
              <Text style={styles.pricingValue}>{pricing.tier}</Text>
            </View>

            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Unit Price:</Text>
              <Text style={styles.pricingValue}>${pricing.unitPrice}</Text>
            </View>

            {pricing.discount > 0 && (
              <View style={styles.pricingRow}>
                <Text style={styles.pricingLabel}>Quantity Discount:</Text>
                <Text style={[styles.pricingValue, styles.discountText]}>
                  -{pricing.discount}%
                </Text>
              </View>
            )}

            <View style={[styles.pricingRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total Price:</Text>
              <Text style={styles.totalValue}>${pricing.totalPrice}</Text>
            </View>
          </View>
        </Card>

        <Card style={styles.card}>
          <View style={styles.cardContent}>
            <Text style={styles.cardTitle}>Pricing Tiers</Text>
            
            {pricingTiers.map((tier, index) => (
              <View key={index} style={styles.tierCard}>
                <View style={styles.tierHeader}>
                  <Text style={styles.tierName}>{tier.name}</Text>
                  <Text style={styles.tierPrice}>${tier.basePrice}</Text>
                </View>
                <Text style={styles.tierRecipes}>Up to {tier.maxRecipes} recipes</Text>
                <View style={styles.tierFeatures}>
                  {tier.features.map((feature, featureIndex) => (
                    <Text key={featureIndex} style={styles.tierFeature}>
                      • {feature}
                    </Text>
                  ))}
                </View>
              </View>
            ))}
          </View>
        </Card>

        <View style={styles.notes}>
          <Text style={styles.notesTitle}>Notes:</Text>
          <Text style={styles.notesText}>
            • Prices include design and layout{'\n'}
            • Additional recipes beyond tier limit: $0.50 each{'\n'}
            • Quantity discounts: 10% off for 10+, 15% off for 25+, 20% off for 50+{'\n'}
            • Shipping costs calculated at checkout
          </Text>
        </View>

        <Button
          title="Start Your Recipe Book"
          style={styles.ctaButton}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#111827',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#6b7280',
    marginBottom: 32,
  },
  card: {
    marginBottom: 24,
  },
  cardContent: {
    padding: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    color: '#111827',
  },
  field: {
    marginBottom: 16,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  pricingLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  pricingValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  discountText: {
    color: '#10b981',
  },
  totalRow: {
    borderBottomWidth: 0,
    paddingTop: 16,
    marginTop: 8,
    borderTopWidth: 2,
    borderTopColor: '#e5e7eb',
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
  },
  totalValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  tierCard: {
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  tierHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  tierName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
  },
  tierPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  tierRecipes: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  tierFeatures: {
    gap: 4,
  },
  tierFeature: {
    fontSize: 12,
    color: '#6b7280',
  },
  notes: {
    backgroundColor: '#f0f9ff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
  },
  notesTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 8,
  },
  notesText: {
    fontSize: 12,
    color: '#1e40af',
    lineHeight: 18,
  },
  ctaButton: {
    marginBottom: 32,
  },
});
