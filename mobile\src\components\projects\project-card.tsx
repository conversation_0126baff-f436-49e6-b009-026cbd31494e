import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Button } from "../ui/button";
import { Card } from "../ui/card";

type ProjectCardProps = {
  title: string;
  recipeCount: number;
  lastUpdated: string;
  status: "In Progress" | "Draft" | "Completed";
  contributors: { initials: string; color: string }[];
  imageIndex: number;
  bgColor: string;
  role?: string;
};

const mockImages = [
  'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
  'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=400',
];

export function ProjectCard({
  title,
  recipeCount,
  lastUpdated,
  status,
  contributors,
  imageIndex,
  bgColor,
  role = 'contributor',
}: ProjectCardProps) {
  const statusColors = {
    "In Progress": { backgroundColor: '#f3f4f6', color: '#6b7280' },
    "Draft": { backgroundColor: '#dbeafe', color: '#3b82f6' },
    "Completed": { backgroundColor: '#dcfce7', color: '#16a34a' },
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return { backgroundColor: '#f3e8ff', color: '#7c3aed' };
      case 'organizer':
        return { backgroundColor: '#dbeafe', color: '#3b82f6' };
      case 'contributor':
        return { backgroundColor: '#f3f4f6', color: '#6b7280' };
      default:
        return { backgroundColor: '#f3f4f6', color: '#6b7280' };
    }
  };

  return (
    <Card style={styles.card}>
      <View style={[styles.header, { backgroundColor: bgColor }]}>
        <Image 
          source={{ uri: mockImages[imageIndex % mockImages.length] }}
          style={styles.headerImage}
        />
        <View style={styles.headerOverlay}>
          <Text style={styles.headerTitle}>{title}</Text>
        </View>
      </View>
      
      <View style={styles.content}>
        <View style={styles.contributorsRow}>
          <View style={styles.contributors}>
            {contributors.map((contributor, index) => (
              <View 
                key={index}
                style={[styles.avatar, { backgroundColor: contributor.color }]}
              >
                <Text style={styles.avatarText}>{contributor.initials}</Text>
              </View>
            ))}
            <TouchableOpacity style={styles.addButton}>
              <Text style={styles.addButtonText}>+</Text>
            </TouchableOpacity>
          </View>
          
          <View style={[styles.roleBadge, getRoleColor(role)]}>
            <Text style={[styles.roleBadgeText, { color: getRoleColor(role).color }]}>
              {role}
            </Text>
          </View>
        </View>
        
        <View style={styles.infoRow}>
          <View style={styles.info}>
            <Text style={styles.recipeCount}>{recipeCount} recipes</Text>
            <Text style={styles.lastUpdated}>Last updated {lastUpdated}</Text>
          </View>
          
          <View style={[styles.statusBadge, statusColors[status]]}>
            <Text style={[styles.statusBadgeText, { color: statusColors[status].color }]}>
              {status}
            </Text>
          </View>
        </View>
        
        <View style={styles.actions}>
          <Button title="Edit" style={styles.editButton} />
          <TouchableOpacity style={styles.moreButton}>
            <Text style={styles.moreButtonText}>⋯</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Card>
  );
}

export function NewProjectCard() {
  return (
    <Card style={[styles.card, styles.newProjectCard]}>
      <View style={styles.newProjectContent}>
        <View style={styles.newProjectIcon}>
          <Text style={styles.newProjectIconText}>+</Text>
        </View>
        <Text style={styles.newProjectTitle}>Create New Cookbook</Text>
        <Text style={styles.newProjectSubtitle}>Start a new collection of family recipes</Text>
        <Button title="Get Started" />
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  header: {
    height: 160,
    position: 'relative',
  },
  headerImage: {
    width: '100%',
    height: '100%',
    opacity: 0.5,
  },
  headerOverlay: {
    position: 'absolute',
    inset: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    fontFamily: 'serif',
  },
  content: {
    padding: 20,
  },
  contributorsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  contributors: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  avatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  addButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: '#d1d5db',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  addButtonText: {
    fontSize: 12,
    color: '#6b7280',
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  roleBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  info: {
    flex: 1,
  },
  recipeCount: {
    fontSize: 14,
    marginBottom: 2,
  },
  lastUpdated: {
    fontSize: 12,
    color: '#6b7280',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  editButton: {
    flex: 1,
  },
  moreButton: {
    width: 40,
    height: 40,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#d1d5db',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
  moreButtonText: {
    fontSize: 16,
    color: '#6b7280',
  },
  newProjectCard: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#d1d5db',
  },
  newProjectContent: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  newProjectIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#dbeafe',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  newProjectIconText: {
    fontSize: 24,
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  newProjectTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    fontFamily: 'serif',
  },
  newProjectSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 16,
  },
});
