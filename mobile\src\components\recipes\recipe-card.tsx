import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Card } from "../ui/card";
import { Button } from "../ui/button";

type Tag = {
  label: string;
  variant: "primary" | "secondary" | "outline";
};

type RecipeCardProps = {
  title: string;
  description: string;
  tags: Tag[];
  contributor: string;
  imageIndex: number;
  isSaved: boolean;
};

const mockImages = [
  'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400',
  'https://images.unsplash.com/photo-1551782450-a2132b4ba21d?w=400',
  'https://images.unsplash.com/photo-1464349095431-e9a21285b5f3?w=400',
];

export function RecipeCard({
  title,
  description,
  tags,
  contributor,
  imageIndex,
  isSaved,
}: RecipeCardProps) {
  const getTagStyle = (variant: string) => {
    switch (variant) {
      case 'primary':
        return { backgroundColor: '#dbeafe', color: '#3b82f6' };
      case 'secondary':
        return { backgroundColor: '#f3f4f6', color: '#6b7280' };
      case 'outline':
        return { backgroundColor: 'white', color: '#6b7280', borderWidth: 1, borderColor: '#d1d5db' };
      default:
        return { backgroundColor: '#f3f4f6', color: '#6b7280' };
    }
  };

  return (
    <Card style={styles.card}>
      <View style={styles.imageContainer}>
        <Image 
          source={{ uri: mockImages[imageIndex % mockImages.length] }}
          style={styles.image}
        />
        <TouchableOpacity style={styles.saveButton}>
          <Text style={[styles.saveButtonText, isSaved && styles.savedText]}>
            {isSaved ? '♥' : '♡'}
          </Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description}>{description}</Text>
        
        <View style={styles.tags}>
          {tags.map((tag, index) => (
            <View key={index} style={[styles.tag, getTagStyle(tag.variant)]}>
              <Text style={[styles.tagText, { color: getTagStyle(tag.variant).color }]}>
                {tag.label}
              </Text>
            </View>
          ))}
        </View>
        
        <View style={styles.footer}>
          <Text style={styles.contributor}>by {contributor}</Text>
          <Button title="View Recipe" size="sm" />
        </View>
      </View>
    </Card>
  );
}

export function AddRecipeCard() {
  return (
    <Card style={[styles.card, styles.addRecipeCard]}>
      <View style={styles.addRecipeContent}>
        <View style={styles.addRecipeIcon}>
          <Text style={styles.addRecipeIconText}>+</Text>
        </View>
        <Text style={styles.addRecipeTitle}>Add New Recipe</Text>
        <Text style={styles.addRecipeSubtitle}>Share your family's favorite recipe</Text>
        <Button title="Add Recipe" />
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
    height: 200,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  saveButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#6b7280',
  },
  savedText: {
    color: '#ef4444',
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'serif',
  },
  description: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 12,
    lineHeight: 20,
  },
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  tagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contributor: {
    fontSize: 12,
    color: '#6b7280',
    fontStyle: 'italic',
  },
  addRecipeCard: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: '#d1d5db',
    backgroundColor: '#f9fafb',
  },
  addRecipeContent: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  addRecipeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#dbeafe',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  addRecipeIconText: {
    fontSize: 20,
    color: '#3b82f6',
    fontWeight: 'bold',
  },
  addRecipeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'serif',
  },
  addRecipeSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 16,
  },
});
