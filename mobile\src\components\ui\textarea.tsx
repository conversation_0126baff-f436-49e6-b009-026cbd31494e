import React from 'react';
import { TextInput, StyleSheet, TextInputProps, ViewStyle } from 'react-native';

interface TextareaProps extends TextInputProps {
  style?: ViewStyle;
}

export function Textarea({ style, ...props }: TextareaProps) {
  return (
    <TextInput
      {...props}
      multiline
      textAlignVertical="top"
      style={[styles.textarea, style]}
    />
  );
}

const styles = StyleSheet.create({
  textarea: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 6,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#ffffff',
    minHeight: 80,
    color: '#111827',
  },
});
