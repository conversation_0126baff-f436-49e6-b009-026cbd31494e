import { useCallback } from 'react';
import { useAuth } from './use-auth';

// Mobile version of Intercom hooks
// In a real implementation, this would use @intercom/intercom-react-native

export function useIntercom() {
  const { user } = useAuth();

  const boot = useCallback((config?: any) => {
    console.log('Intercom boot called (mobile):', config);
  }, []);

  const shutdown = useCallback(() => {
    console.log('Intercom shutdown called (mobile)');
  }, []);

  const update = useCallback((data?: any) => {
    console.log('Intercom update called (mobile):', data);
  }, []);

  const show = useCallback(() => {
    console.log('Intercom show called (mobile)');
  }, []);

  const hide = useCallback(() => {
    console.log('Intercom hide called (mobile)');
  }, []);

  const showNewMessage = useCallback((message?: string) => {
    console.log('Intercom showNewMessage called (mobile):', message);
  }, []);

  const trackEvent = useCallback((eventName: string, metadata?: any) => {
    console.log('Intercom trackEvent called (mobile):', eventName, metadata);
  }, []);

  const trackUserCreated = useCallback((userData: any) => {
    trackEvent('user_created', {
      user_id: userData.id,
      email: userData.email,
      role: userData.role,
      created_at: userData.createdAt,
    });
  }, [trackEvent]);

  const trackRecipeCreated = useCallback((recipeData: any) => {
    trackEvent('recipe_created', {
      recipe_id: recipeData.id,
      recipe_title: recipeData.title,
      project_id: recipeData.projectId,
      contributor_id: recipeData.contributorId,
    });
  }, [trackEvent]);

  const trackRecipeBookCreated = useCallback((bookData: any) => {
    trackEvent('recipe_book_created', {
      book_id: bookData.id,
      book_title: bookData.title,
      organizer_id: bookData.organizerId,
      recipe_count: bookData.recipeCount || 0,
    });
  }, [trackEvent]);

  const trackInviteSent = useCallback((inviteData: any) => {
    trackEvent('invite_sent', {
      project_id: inviteData.projectId,
      invitee_email: inviteData.email,
      organizer_id: inviteData.organizerId,
      deadline: inviteData.deadline,
    });
  }, [trackEvent]);

  const trackRecipeApproved = useCallback((recipeData: any) => {
    trackEvent('recipe_approved', {
      recipe_id: recipeData.id,
      recipe_title: recipeData.title,
      project_id: recipeData.projectId,
      organizer_id: recipeData.organizerId,
    });
  }, [trackEvent]);

  const trackRecipeRejected = useCallback((recipeData: any) => {
    trackEvent('recipe_rejected', {
      recipe_id: recipeData.id,
      recipe_title: recipeData.title,
      project_id: recipeData.projectId,
      organizer_id: recipeData.organizerId,
      rejection_reason: recipeData.rejectionMessage,
    });
  }, [trackEvent]);

  const trackOrderPlaced = useCallback((orderData: any) => {
    trackEvent('order_placed', {
      order_id: orderData.id,
      book_id: orderData.bookId,
      quantity: orderData.quantity,
      total_amount: orderData.totalAmount,
    });
  }, [trackEvent]);

  const trackSupportTicketCreated = useCallback((ticketData: any) => {
    trackEvent('support_ticket_created', {
      ticket_id: ticketData.id,
      category: ticketData.category,
      priority: ticketData.priority,
      user_id: ticketData.userId,
    });
  }, [trackEvent]);

  return {
    boot,
    shutdown,
    update,
    show,
    hide,
    showNewMessage,
    trackEvent,
    trackUserCreated,
    trackRecipeCreated,
    trackRecipeBookCreated,
    trackInviteSent,
    trackRecipeApproved,
    trackRecipeRejected,
    trackOrderPlaced,
    trackSupportTicketCreated,
  };
}

export function useIntercomChat() {
  // For mobile, return false since we don't have Intercom configured
  return false;
}
