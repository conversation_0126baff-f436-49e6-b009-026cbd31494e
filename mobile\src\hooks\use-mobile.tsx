import * as React from "react"
import { Dimensions } from 'react-native';

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const updateIsMobile = () => {
      const { width } = Dimensions.get('window');
      setIsMobile(width < MOBILE_BREAKPOINT);
    };

    // Set initial value
    updateIsMobile();

    // Listen for dimension changes
    const subscription = Dimensions.addEventListener('change', updateIsMobile);

    return () => subscription?.remove();
  }, [])

  return !!isMobile
}
