// User roles
export const UserRole = {
  ADMIN: 'admin',
  ORGANIZER: 'organizer',
  CONTRIBUTOR: 'contributor'
} as const;

export type UserRoleType = typeof UserRole[keyof typeof UserRole];

// Recipe status
export const RecipeStatus = {
  DRAFT: 'draft',
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected'
} as const;

// Project status
export const ProjectStatus = {
  ACTIVE: 'active',
  COMPLETED: 'completed',
  ARCHIVED: 'archived'
} as const;

// Measurement systems
export const MeasurementSystem = {
  US: 'us',
  METRIC: 'metric'
} as const;

// Recipe categories
export const RecipeCategories = [
  'Appetizers',
  'Main Courses',
  'Desserts',
  'Beverages',
  'Soups',
  'Salads',
  'Side Dishes',
  'Breakfast',
  'Snacks',
  'Other'
];

// Common units for US system
export const US_UNITS = [
  'cup', 'cups',
  'tablespoon', 'tablespoons', 'tbsp',
  'teaspoon', 'teaspoons', 'tsp',
  'pound', 'pounds', 'lb', 'lbs',
  'ounce', 'ounces', 'oz',
  'pint', 'pints', 'pt',
  'quart', 'quarts', 'qt',
  'gallon', 'gallons', 'gal',
  'fluid ounce', 'fluid ounces', 'fl oz',
  'piece', 'pieces', 'pc',
  'slice', 'slices',
  'clove', 'cloves',
  'pinch', 'pinches',
  'dash', 'dashes',
  'to taste'
];

// Common units for metric system
export const METRIC_UNITS = [
  'gram', 'grams', 'g',
  'kilogram', 'kilograms', 'kg',
  'liter', 'liters', 'l',
  'milliliter', 'milliliters', 'ml',
  'centiliter', 'centiliters', 'cl',
  'deciliter', 'deciliters', 'dl',
  'piece', 'pieces', 'pc',
  'slice', 'slices',
  'clove', 'cloves',
  'pinch', 'pinches',
  'dash', 'dashes',
  'to taste'
];

// Page Count Estimation
export const PAGE_COUNT_PER_RECIPE = 2; // Estimated pages per recipe
export const MINIMUM_PAGE_COUNT = 20; // Minimum page count for any book

// API URLs - These will be the same as the web version
const API_BASE_URL = __DEV__
  ? 'http://localhost:5000'
  : 'https://storyworth.onrender.com';

export const API_URL = `${API_BASE_URL}/api`;

// Client URLs
export const CLIENT_URL = __DEV__
  ? 'http://localhost:3000'
  : 'https://storyworth.vercel.app';

// Theme colors matching the web version
export const Colors = {
  // Primary colors
  background: '#F8F7F4',
  foreground: '#2E4B7A', // Dark blue for main text

  // Muted colors
  muted: '#E6D5C4',
  mutedForeground: '#9B7A5D', // Brown for secondary text

  // Card colors
  card: '#FFFFFF',
  cardForeground: '#2E4B7A', // Dark blue for card text

  // Border and input
  border: '#E6D5C4',
  input: '#E6D5C4',

  // Primary
  primary: '#2E4B7A', // Dark blue for buttons
  primaryForeground: '#FFFFFF', // White text on dark blue buttons

  // Secondary
  secondary: '#E6D5C4',
  secondaryForeground: '#2E4B7A',

  // Accent
  accent: '#9B7A5D',
  accentForeground: '#2E4B7A',

  // Destructive
  destructive: '#DC2626',
  destructiveForeground: '#FFFFFF',

  // Success
  success: '#16A34A',
  successForeground: '#FFFFFF',

  // Warning
  warning: '#D97706',
  warningForeground: '#FFFFFF',

  // Info
  info: '#2563EB',
  infoForeground: '#FFFFFF',

  // Text colors for better readability
  textPrimary: '#2E4B7A', // Main text color - dark blue
  textSecondary: '#9B7A5D', // Secondary text color - brown
  textMuted: '#6B7280', // Muted text color - gray
};

// Font families
export const Fonts = {
  regular: 'System',
  serif: 'serif',
  handwritten: 'cursive',
};

// Spacing
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Border radius
export const BorderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
};

// Other constants
export const MAX_INGREDIENTS = 15;
export const MAX_INSTRUCTIONS = 15;
export const MAX_IMAGES = 3;
