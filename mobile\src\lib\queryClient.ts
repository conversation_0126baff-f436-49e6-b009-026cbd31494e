import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error && typeof error === 'object' && 'status' in error) {
          const status = (error as any).status;
          if (status >= 400 && status < 500) {
            return false;
          }
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
    mutations: {
      retry: false,
    },
  },
});

// Query keys for consistent caching
export const queryKeys = {
  // Recipe Books
  recipeBooks: ['recipeBooks'] as const,
  recipeBook: (id: number) => ['recipeBook', id] as const,
  
  // Recipes
  recipes: ['recipes'] as const,
  recipe: (id: number) => ['recipe', id] as const,
  recipesByProject: (projectId: number) => ['recipes', 'project', projectId] as const,
  
  // User
  user: ['user'] as const,
  profile: ['profile'] as const,
  
  // Contributors
  contributors: (projectId: number) => ['contributors', projectId] as const,
  
  // Notifications
  notifications: ['notifications'] as const,
};
