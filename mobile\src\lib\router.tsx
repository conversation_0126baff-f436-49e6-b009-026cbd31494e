import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { View } from 'react-native';

// Router context
interface RouterContextType {
  location: string;
  setLocation: (path: string) => void;
}

const RouterContext = createContext<RouterContextType | undefined>(undefined);

// Router provider
export function Router({ children }: { children: ReactNode }) {
  const [location, setLocation] = useState('/');

  return (
    <RouterContext.Provider value={{ location, setLocation }}>
      <View style={{ flex: 1 }}>
        {children}
      </View>
    </RouterContext.Provider>
  );
}

// useLocation hook (mimics wouter)
export function useLocation(): [string, (path: string) => void] {
  const context = useContext(RouterContext);
  if (!context) {
    throw new Error('useLocation must be used within a Router');
  }
  return [context.location, context.setLocation];
}

// Route component
interface RouteProps {
  path: string;
  component?: React.ComponentType<any>;
  children?: ReactNode;
}

export function Route({ path, component: Component, children }: RouteProps) {
  const [location] = useLocation();
  
  // Simple path matching (can be enhanced for params)
  const isMatch = path === '*' || location === path || 
    (path.includes(':') && matchPathWithParams(location, path));

  if (!isMatch) return null;

  if (Component) {
    return <Component />;
  }

  return <>{children}</>;
}

// Switch component (renders first matching route)
export function Switch({ children }: { children: ReactNode }) {
  const [location] = useLocation();
  
  const routes = React.Children.toArray(children) as React.ReactElement[];
  
  for (const route of routes) {
    if (route.type === Route) {
      const path = route.props.path;
      const isMatch = path === '*' || location === path || 
        (path.includes(':') && matchPathWithParams(location, path));
      
      if (isMatch) {
        return route;
      }
    }
  }
  
  return null;
}

// Simple path matching with parameters
function matchPathWithParams(location: string, pattern: string): boolean {
  const locationParts = location.split('/');
  const patternParts = pattern.split('/');
  
  if (locationParts.length !== patternParts.length) {
    return false;
  }
  
  for (let i = 0; i < patternParts.length; i++) {
    const patternPart = patternParts[i];
    const locationPart = locationParts[i];
    
    if (patternPart.startsWith(':')) {
      // Parameter match - always matches
      continue;
    }
    
    if (patternPart !== locationPart) {
      return false;
    }
  }
  
  return true;
}

// useParams hook for extracting route parameters
export function useParams(): Record<string, string> {
  const [location] = useLocation();
  // This would need to be enhanced to extract actual parameters
  // For now, return empty object
  return {};
}
