import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useAuth } from '../hooks/useAuth';
import { RootStackParamList, MainTabParamList } from '../types';
import { Colors } from '../lib/constants';

// Import screens (we'll create these next)
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import RecipeBooksScreen from '../screens/RecipeBooksScreen';
import RecipeBookDetailScreen from '../screens/RecipeBookDetailScreen';
import CreateRecipeBookScreen from '../screens/CreateRecipeBookScreen';
import CreateRecipeScreen from '../screens/CreateRecipeScreen';
import EditRecipeScreen from '../screens/EditRecipeScreen';
import RecipeDetailScreen from '../screens/RecipeDetailScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SettingsScreen from '../screens/SettingsScreen';
import AdminPanelScreen from '../screens/AdminPanelScreen';
import OrganizerDashboardScreen from '../screens/OrganizerDashboardScreen';
import ContributorDashboardScreen from '../screens/ContributorDashboardScreen';
import LoadingScreen from '../screens/LoadingScreen';

// Import icons (we'll use react-native-vector-icons)
import Icon from 'react-native-vector-icons/MaterialIcons';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

function MainTabNavigator() {
  const { user } = useAuth();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'RecipeBooks':
              iconName = 'book';
              break;
            case 'CreateRecipe':
              iconName = 'add-circle';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            case 'Settings':
              iconName = 'settings';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.mutedForeground,
        tabBarStyle: {
          backgroundColor: Colors.card,
          borderTopColor: Colors.border,
        },
        headerStyle: {
          backgroundColor: Colors.card,
          borderBottomColor: Colors.border,
        },
        headerTintColor: Colors.foreground,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="RecipeBooks" 
        component={RecipeBooksScreen}
        options={{ title: 'Recipe Books' }}
      />
      
      {/* Show Create Recipe tab for contributors and organizers */}
      {user?.role !== 'admin' && (
        <Tab.Screen 
          name="CreateRecipe" 
          component={CreateRecipeScreen}
          options={{ title: 'Create Recipe' }}
        />
      )}
      
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ title: 'Profile' }}
      />
      
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{ title: 'Settings' }}
      />
    </Tab.Navigator>
  );
}

function AuthNavigator() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.card,
          borderBottomColor: Colors.border,
        },
        headerTintColor: Colors.foreground,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="Login" 
        component={LoginScreen}
        options={{ title: 'Sign In' }}
      />
      <Stack.Screen 
        name="Register" 
        component={RegisterScreen}
        options={{ title: 'Sign Up' }}
      />
    </Stack.Navigator>
  );
}

function MainNavigator() {
  const { user } = useAuth();

  return (
    <Stack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.card,
          borderBottomColor: Colors.border,
        },
        headerTintColor: Colors.foreground,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen 
        name="Main" 
        component={MainTabNavigator}
        options={{ headerShown: false }}
      />
      
      <Stack.Screen 
        name="RecipeBookDetail" 
        component={RecipeBookDetailScreen}
        options={{ title: 'Recipe Book' }}
      />
      
      <Stack.Screen 
        name="CreateRecipeBook" 
        component={CreateRecipeBookScreen}
        options={{ title: 'Create Recipe Book' }}
      />
      
      <Stack.Screen 
        name="EditRecipe" 
        component={EditRecipeScreen}
        options={{ title: 'Edit Recipe' }}
      />
      
      <Stack.Screen 
        name="RecipeDetail" 
        component={RecipeDetailScreen}
        options={{ title: 'Recipe' }}
      />
      
      {/* Role-specific screens */}
      {user?.role === 'admin' && (
        <Stack.Screen 
          name="AdminPanel" 
          component={AdminPanelScreen}
          options={{ title: 'Admin Panel' }}
        />
      )}
      
      {user?.role === 'organizer' && (
        <Stack.Screen 
          name="OrganizerDashboard" 
          component={OrganizerDashboardScreen}
          options={{ title: 'Organizer Dashboard' }}
        />
      )}
      
      {user?.role === 'contributor' && (
        <Stack.Screen 
          name="ContributorDashboard" 
          component={ContributorDashboardScreen}
          options={{ title: 'My Contributions' }}
        />
      )}
    </Stack.Navigator>
  );
}

export default function AppNavigator() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      {user ? <MainNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
}
