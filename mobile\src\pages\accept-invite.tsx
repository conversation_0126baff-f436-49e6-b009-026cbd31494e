import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Linking
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { useToast } from '../hooks/use-toast';
import { useAuth } from '../hooks/use-auth';
import { useLocation } from '../lib/router';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';

export default function AcceptInvite() {
  console.log('AcceptInvite component rendered');

  const [isLoading, setIsLoading] = useState(false);
  const [projectName, setProjectName] = useState("");
  const [organizerName, setOrganizerName] = useState("");
  const [invitationStatus, setInvitationStatus] = useState<"pending" | "accepted" | "invalid">("pending");
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const { user, isLoading: isAuthLoading } = useAuth();

  // Function to accept the invitation
  const acceptInvitation = async (projectId: string, invitationToken: string) => {
    if (!user) {
      return false;
    }

    try {
      const authToken = await AsyncStorage.getItem("token");
      if (!authToken) {
        throw new Error("Authentication token not found");
      }

      console.log('Accepting invitation with:', { projectId, invitationToken });

      const response = await fetch(`${API_URL}/contributor/projects/${projectId}/accept-invite`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({ token: invitationToken })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.message || "Failed to accept invitation");
      }

      const data = await response.json();
      console.log('Invitation accepted:', data);

      // Set invitation status to accepted regardless of who invited
      setInvitationStatus("accepted");
      toast({
        title: "Success",
        description: "You have successfully joined the project!",
      });

      // Redirect to contributor dashboard after a short delay
      setTimeout(() => {
        setLocation("/recipe-books");
      }, 1500);

      return true;
    } catch (error) {
      console.error("Error accepting invitation:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to accept invitation. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  // Parse URL parameters from deep link
  const parseUrlParams = () => {
    // In a real React Native app, you would get these from deep linking
    // For now, we'll simulate getting them from the current location
    const urlParts = location.split('?');
    if (urlParts.length < 2) return {};

    const params: Record<string, string> = {};
    const searchParams = urlParts[1].split('&');

    searchParams.forEach(param => {
      const [key, value] = param.split('=');
      if (key && value) {
        params[key] = decodeURIComponent(value);
      }
    });

    return params;
  };

  useEffect(() => {
    console.log('AcceptInvite useEffect running');

    // Get project and organizer info from URL params
    const params = parseUrlParams();
    const projectId = params.projectId;
    const invitationToken = params.token;
    const fromEmail = params.fromEmail === "true";

    console.log('URL params:', { projectId, invitationToken, fromEmail });

    // If no parameters, show invalid invitation message
    if (!projectId || !invitationToken) {
      console.log('No parameters found, showing invalid invitation message');
      setInvitationStatus("invalid");
      return;
    }

    // Store invitation token and projectId in AsyncStorage
    AsyncStorage.setItem("invitationToken", invitationToken);
    AsyncStorage.setItem("invitationProjectId", projectId);

    // If auth is still loading, wait
    if (isAuthLoading) {
      console.log('Auth is still loading, waiting...');
      return;
    }

    // If user is not logged in, redirect to login with return URL
    if (!user) {
      console.log('User not logged in, redirecting to login');
      const returnUrl = `/accept-invite?projectId=${projectId}&token=${invitationToken}&fromEmail=true`;
      setLocation(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
      return;
    }

    // Fetch project details and check invitation status
    const fetchProjectDetails = async () => {
      try {
        console.log('Fetching project details');
        const authToken = await AsyncStorage.getItem("token");
        if (!authToken) {
          throw new Error("Authentication token not found");
        }

        const response = await fetch(`${API_URL}/contributor/projects/${projectId}/invite-status?token=${invitationToken}`, {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: "Failed to fetch project details" }));
          console.error('Error fetching project details:', errorData);
          throw new Error(errorData.message || "Failed to fetch project details");
        }

        const data = await response.json();
        console.log('Project details:', data);

        setProjectName(data.projectName);
        setOrganizerName(data.organizerName);
        setInvitationStatus(data.status);

        // If user came from email and invitation is still pending, automatically accept it
        if (fromEmail && data.status === "pending") {
          setIsLoading(true);
          await acceptInvitation(projectId, invitationToken);
          setIsLoading(false);
        } else if (data.status === "accepted") {
          // If invitation is already accepted, just show a success message without trying to accept again
          toast({
            title: "Already Joined",
            description: "You have already joined this project.",
          });

          // Redirect to contributor dashboard after a short delay
          setTimeout(() => {
            setLocation("/recipe-books");
          }, 1500);
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
        toast({
          title: "Error",
          description: "Failed to load project details. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchProjectDetails();
  }, [toast, setLocation, user, isAuthLoading, location]);

  const handleAcceptInvite = async () => {
    if (!user) {
      const params = parseUrlParams();
      const projectId = params.projectId;
      const invitationToken = params.token;
      const returnUrl = `/accept-invite?projectId=${projectId}&token=${invitationToken}`;
      setLocation(`/login?returnUrl=${encodeURIComponent(returnUrl)}`);
      return;
    }

    // Don't try to accept if already accepted
    if (invitationStatus === "accepted") {
      toast({
        title: "Already Joined",
        description: "You have already joined this project.",
      });

      // Redirect to contributor dashboard after a short delay
      setTimeout(() => {
        setLocation("/recipe-books");
      }, 1500);
      return;
    }

    setIsLoading(true);
    const params = parseUrlParams();
    const projectId = params.projectId;
    const invitationToken = params.token;

    if (!projectId || !invitationToken) {
      toast({
        title: "Error",
        description: "Missing project ID or token",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    await acceptInvitation(projectId, invitationToken);
    setIsLoading(false);
  };

  if (invitationStatus === "invalid") {
    return (
      <View style={styles.container}>
        <Card style={styles.card}>
          <CardHeader>
            <Text style={styles.cardTitle}>Invalid Invitation</Text>
            <Text style={styles.cardDescription}>
              This invitation link is invalid or has expired.
            </Text>
          </CardHeader>
          <CardContent>
            <Button
              onPress={() => setLocation("/recipe-books")}
              style={styles.button}
            >
              Go to Dashboard
            </Button>
          </CardContent>
        </Card>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Project Invitation</Text>
          <Text style={styles.cardDescription}>
            {organizerName} has invited you to contribute to their recipe book "{projectName}"
          </Text>
        </CardHeader>
        <CardContent>
          <Text style={styles.description}>
            By accepting this invitation, you'll be able to submit recipes to this project.
          </Text>

          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Processing your invitation...</Text>
            </View>
          ) : invitationStatus === "pending" ? (
            <View style={styles.actionContainer}>
              <Button
                onPress={handleAcceptInvite}
                disabled={isLoading}
                style={styles.acceptButton}
              >
                Accept Invitation
              </Button>
              <Text style={styles.helpText}>
                If the invitation wasn't automatically accepted, please click the button above.
              </Text>
            </View>
          ) : invitationStatus === "accepted" ? (
            <View style={styles.successContainer}>
              <Text style={styles.successText}>
                You have successfully joined the project!
              </Text>
            </View>
          ) : null}
        </CardContent>
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: Spacing.lg,
  },
  card: {
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    lineHeight: 20,
  },
  description: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.lg,
    lineHeight: 20,
  },
  loadingContainer: {
    alignItems: 'center',
    padding: Spacing.lg,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
    textAlign: 'center',
  },
  actionContainer: {
    gap: Spacing.md,
  },
  acceptButton: {
    backgroundColor: '#16a34a', // green-600
    borderColor: '#16a34a',
  },
  helpText: {
    fontSize: 12,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 16,
  },
  successContainer: {
    alignItems: 'center',
    padding: Spacing.md,
  },
  successText: {
    fontSize: 16,
    color: '#16a34a', // green-600
    fontWeight: '500',
    textAlign: 'center',
  },
  button: {
    marginTop: Spacing.md,
  },
});