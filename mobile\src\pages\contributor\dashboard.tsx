import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { useToast } from '../../hooks/use-toast';
import { useAuth } from '../../hooks/use-auth';
import { useLocation } from '../../lib/router';
import { Colors, Spacing, BorderRadius, API_URL } from '../../lib/constants';

interface Project {
  id: number;
  name: string;
  description: string;
  status: string;
  createdAt: string;
}

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  projectId: number;
  project?: {
    id: number;
    name: string;
    description: string;
  };
}

export default function ContributorDashboard() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();
  const [, setLocation] = useLocation();

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Starting to fetch data...');

        const token = await AsyncStorage.getItem('token');
        if (!token) {
          throw new Error('Not authenticated');
        }

        // Fetch projects
        console.log('Fetching projects...');
        const projectsResponse = await fetch(`${API_URL}/contributor/all-projects`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!projectsResponse.ok) {
          throw new Error("Failed to fetch projects");
        }

        const projectsData = await projectsResponse.json();
        console.log('Projects response:', projectsData);
        setProjects(projectsData.projects || []);

        // Fetch recipes
        console.log('Fetching recipes...');
        const recipesResponse = await fetch(`${API_URL}/contributor/recipes`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!recipesResponse.ok) {
          throw new Error("Failed to fetch recipes");
        }

        const recipesData = await recipesResponse.json();
        console.log('Recipes response:', recipesData);
        // Handle both array and object with recipes property
        const recipesArray = Array.isArray(recipesData) ? recipesData : (recipesData.recipes || []);
        console.log('Setting recipes array:', recipesArray);
        setRecipes(recipesArray);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load your data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [toast]);

  // Add effect to log state changes
  useEffect(() => {
    console.log('Projects state updated:', projects);
  }, [projects]);

  useEffect(() => {
    console.log('Recipes state updated:', recipes);
  }, [recipes]);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Welcome, {user?.name}</Text>
      </View>

      <View style={styles.content}>
        {/* Projects Card */}
        <Card style={styles.card}>
          <CardHeader>
            <Text style={styles.cardTitle}>Your Projects</Text>
            <Text style={styles.cardDescription}>
              View and manage your recipe book contributions
            </Text>
          </CardHeader>
          <CardContent>
            {projects?.length === 0 ? (
              <Text style={styles.emptyText}>You haven't joined any projects yet.</Text>
            ) : (
              <View style={styles.projectsList}>
                {projects.map((project) => (
                  <TouchableOpacity
                    key={project.id}
                    onPress={() => setLocation(`/recipe-books/${project.id}`)}
                    style={styles.projectCard}
                  >
                    <Card>
                      <CardHeader>
                        <Text style={styles.projectTitle}>{project.name}</Text>
                        <Text style={styles.projectDescription}>{project.description}</Text>
                      </CardHeader>
                      <CardContent>
                        <View style={styles.projectMeta}>
                          <Text style={styles.metaText}>
                            Status: {project.status}
                          </Text>
                          <Text style={styles.metaText}>
                            Joined: {new Date(project.createdAt).toLocaleDateString()}
                          </Text>
                        </View>
                      </CardContent>
                    </Card>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </CardContent>
        </Card>

        {/* Recipes Card */}
        <Card style={styles.card}>
          <CardHeader>
            <View style={styles.recipesHeader}>
              <View style={styles.recipesHeaderText}>
                <Text style={styles.cardTitle}>Your Recipe Submissions</Text>
                <Text style={styles.cardDescription}>
                  Track the status of your submitted recipes
                </Text>
              </View>
              <Button
                onPress={() => setLocation("/recipes/create")}
                size="sm"
              >
                Add New Recipe
              </Button>
            </View>
          </CardHeader>
          <CardContent>
            {!recipes || recipes.length === 0 ? (
              <Text style={styles.emptyText}>You haven't submitted any recipes yet.</Text>
            ) : (
              <View style={styles.recipesList}>
                {recipes.map((recipe) => (
                  <View key={recipe.id} style={styles.recipeCard}>
                    <Card>
                      <CardHeader>
                        <Text style={styles.recipeTitle}>{recipe.title}</Text>
                        <Text style={styles.recipeDescription}>{recipe.description}</Text>
                      </CardHeader>
                      <CardContent>
                        <View style={styles.recipeFooter}>
                          <View style={styles.recipeInfo}>
                            <View style={styles.statusContainer}>
                              <Text style={[
                                styles.statusText,
                                recipe.status === 'approved' ? styles.statusApproved :
                                recipe.status === 'rejected' ? styles.statusRejected :
                                styles.statusPending
                              ]}>
                                Status: {recipe.status.charAt(0).toUpperCase() + recipe.status.slice(1)}
                              </Text>
                            </View>
                            <Text style={styles.projectText}>
                              Project: {recipe.project?.name || 'Unknown Project'}
                            </Text>
                            <Text style={styles.dateText}>
                              Submitted: {new Date(recipe.createdAt).toLocaleDateString()}
                            </Text>
                          </View>
                          <Button
                            variant="outline"
                            size="sm"
                            onPress={() => setLocation(`/recipes/${recipe.id}/edit`)}
                          >
                            Edit
                          </Button>
                        </View>
                      </CardContent>
                    </Card>
                  </View>
                ))}
              </View>
            )}
          </CardContent>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  content: {
    padding: Spacing.lg,
    gap: Spacing.lg,
  },
  card: {
    marginBottom: Spacing.md,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    padding: Spacing.xl,
  },
  projectsList: {
    gap: Spacing.md,
  },
  projectCard: {
    marginBottom: Spacing.sm,
  },
  projectTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  projectDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  projectMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  recipesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: Spacing.md,
  },
  recipesHeaderText: {
    flex: 1,
  },
  recipesList: {
    gap: Spacing.md,
  },
  recipeCard: {
    marginBottom: Spacing.sm,
  },
  recipeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  recipeDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  recipeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    gap: Spacing.md,
  },
  recipeInfo: {
    flex: 1,
    gap: Spacing.xs,
  },
  statusContainer: {
    marginBottom: Spacing.xs,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusApproved: {
    color: '#16a34a', // green-600
  },
  statusRejected: {
    color: '#dc2626', // red-600
  },
  statusPending: {
    color: '#ca8a04', // yellow-600
  },
  projectText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  dateText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
});