import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { ColorPalette } from "../components/design-elements/color-palette";
import { Typography } from "../components/design-elements/typography";
import { Buttons } from "../components/design-elements/buttons";
import { FormElements } from "../components/design-elements/form-elements";
import { Cards } from "../components/design-elements/cards";
import { Colors } from "../lib/constants";

export default function DesignElements() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Design Elements</Text>

        {/* COLOR PALETTE */}
        <ColorPalette />

        {/* TYPOGRAPHY */}
        <Typography />

        {/* BUTTONS */}
        <Buttons />

        {/* FORM ELEMENTS */}
        <FormElements />

        {/* CARDS */}
        <Cards />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 32,
    fontFamily: 'serif',
    color: Colors.textPrimary,
  },
});
