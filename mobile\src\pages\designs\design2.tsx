import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Image } from 'react-native';
import { Card } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Badge } from "../../components/ui/badge";

export default function Design2() {
  const [activeTab, setActiveTab] = useState("light");
  
  return (
    <ScrollView style={styles.container}>
      {/* Hero Section - Storyworth Style */}
      <View style={styles.heroSection}>
        <View style={styles.heroContent}>
          <Text style={styles.heroTitle}>
            Everyone has a recipe worth sharing
          </Text>
          <Text style={styles.heroSubtitle}>
            Preserve your family's culinary heritage through meaningful stories and beautiful books
          </Text>
          <Button 
            title="Start Your Family's Story"
            style={styles.heroButton}
          />
        </View>
      </View>

      {/* Featured By Section */}
      <View style={styles.featuredSection}>
        <Text style={styles.featuredText}>Featured in...</Text>
        <View style={styles.featuredLogos}>
          <View style={styles.logoPlaceholder} />
          <View style={styles.logoPlaceholder} />
          <View style={styles.logoPlaceholder} />
        </View>
      </View>

      {/* Our Books Section */}
      <View style={styles.booksSection}>
        <Text style={styles.sectionTitle}>Our Recipe Books</Text>
        <Text style={styles.sectionSubtitle}>
          Beautiful, hardcover books filled with your family's recipes and stories
        </Text>
        <View style={styles.booksGrid}>
          {[1, 2, 3, 4, 5].map((book) => (
            <View key={book} style={styles.bookCard} />
          ))}
        </View>
        <Button 
          title="See Sample Books"
          style={styles.sampleButton}
        />
      </View>

      {/* How It Works Section */}
      <View style={styles.howItWorksSection}>
        <Text style={styles.sectionTitleDark}>How It Works</Text>
        <View style={styles.stepsContainer}>
          {[
            {
              number: "1",
              title: "Share Your Recipes",
              description: "Add your cherished family recipes and the stories behind them"
            },
            {
              number: "2",
              title: "Invite Family",
              description: "Let family members contribute their favorite recipes"
            },
            {
              number: "3",
              title: "Create Your Book",
              description: "Get a beautiful printed cookbook of your family's recipes"
            }
          ].map((step, index) => (
            <View key={index} style={styles.stepCard}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>{step.number}</Text>
              </View>
              <Text style={styles.stepTitle}>{step.title}</Text>
              <Text style={styles.stepDescription}>{step.description}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Features Section */}
      <View style={styles.featuresSection}>
        <View style={styles.featuresGrid}>
          {[
            {
              icon: "♥",
              title: "Made with Love",
              description: "Create a lasting legacy of family recipes"
            },
            {
              icon: "📖",
              title: "Beautiful Books",
              description: "Professional quality, hardcover cookbooks"
            },
            {
              icon: "👥",
              title: "Family Collaboration",
              description: "Invite everyone to contribute their stories"
            }
          ].map((feature, index) => (
            <Card key={index} style={styles.featureCard}>
              <View style={styles.featureContent}>
                <View style={styles.featureIcon}>
                  <Text style={styles.featureIconText}>{feature.icon}</Text>
                </View>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureDescription}>{feature.description}</Text>
              </View>
            </Card>
          ))}
        </View>
      </View>

      {/* Testimonials Section */}
      <View style={styles.testimonialsSection}>
        <Text style={styles.sectionTitleDark}>Family Stories</Text>
        <View style={styles.testimonialsGrid}>
          {[
            {
              quote: "We created a beautiful cookbook with recipes from three generations. It's now our family's most treasured possession.",
              author: "The Johnson Family"
            },
            {
              quote: "Every recipe has a story, and now we've captured them all. Our children will have these memories forever.",
              author: "The Martinez Family"
            }
          ].map((testimonial, index) => (
            <Card key={index} style={styles.testimonialCard}>
              <View style={styles.testimonialContent}>
                <Text style={styles.testimonialStar}>⭐</Text>
                <Text style={styles.testimonialQuote}>{testimonial.quote}</Text>
                <Text style={styles.testimonialAuthor}>{testimonial.author}</Text>
              </View>
            </Card>
          ))}
        </View>
      </View>

      {/* CTA Section */}
      <View style={styles.ctaSection}>
        <Text style={styles.ctaTitle}>Your family's story starts here</Text>
        <Text style={styles.ctaSubtitle}>
          Begin preserving your family's recipes and stories today
        </Text>
        <Button 
          title="Start Your Collection →"
          style={styles.ctaButton}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F7F4',
  },
  heroSection: {
    backgroundColor: '#F8F7F4',
    paddingTop: 60,
    paddingBottom: 80,
    paddingHorizontal: 16,
  },
  heroContent: {
    alignItems: 'center',
    maxWidth: 600,
    alignSelf: 'center',
  },
  heroTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#2E4B7A',
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: 'serif',
  },
  heroSubtitle: {
    fontSize: 18,
    color: '#9B7A5D',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 26,
  },
  heroButton: {
    backgroundColor: '#9B7A5D',
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  featuredSection: {
    backgroundColor: '#ffffff',
    paddingVertical: 48,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  featuredText: {
    color: '#9B7A5D',
    marginBottom: 24,
    fontWeight: '500',
  },
  featuredLogos: {
    flexDirection: 'row',
    gap: 48,
  },
  logoPlaceholder: {
    width: 128,
    height: 32,
    backgroundColor: '#e5e7eb',
    borderRadius: 4,
  },
  booksSection: {
    backgroundColor: '#2E4B7A',
    paddingVertical: 80,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 16,
    fontFamily: 'serif',
  },
  sectionSubtitle: {
    color: '#bfdbfe',
    textAlign: 'center',
    marginBottom: 48,
    maxWidth: 400,
  },
  booksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 32,
    justifyContent: 'center',
    marginBottom: 48,
  },
  bookCard: {
    width: 120,
    height: 160,
    backgroundColor: '#ffffff',
    borderRadius: 8,
  },
  sampleButton: {
    backgroundColor: '#9B7A5D',
  },
  howItWorksSection: {
    backgroundColor: '#ffffff',
    paddingVertical: 80,
    paddingHorizontal: 16,
  },
  sectionTitleDark: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2E4B7A',
    textAlign: 'center',
    marginBottom: 64,
    fontFamily: 'serif',
  },
  stepsContainer: {
    gap: 48,
  },
  stepCard: {
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  stepNumber: {
    width: 80,
    height: 80,
    backgroundColor: '#F8F7F4',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  stepNumberText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2E4B7A',
    fontFamily: 'serif',
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E4B7A',
    marginBottom: 12,
    fontFamily: 'serif',
  },
  stepDescription: {
    color: '#9B7A5D',
    textAlign: 'center',
    fontSize: 16,
  },
  featuresSection: {
    backgroundColor: '#F8F7F4',
    paddingVertical: 80,
    paddingHorizontal: 16,
  },
  featuresGrid: {
    gap: 32,
  },
  featureCard: {
    backgroundColor: '#ffffff',
  },
  featureContent: {
    padding: 32,
    alignItems: 'center',
  },
  featureIcon: {
    width: 64,
    height: 64,
    backgroundColor: '#2E4B7A20',
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  featureIconText: {
    fontSize: 24,
    color: '#2E4B7A',
  },
  featureTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2E4B7A',
    marginBottom: 12,
    fontFamily: 'serif',
  },
  featureDescription: {
    color: '#9B7A5D',
    textAlign: 'center',
    fontSize: 16,
  },
  testimonialsSection: {
    backgroundColor: '#ffffff',
    paddingVertical: 80,
    paddingHorizontal: 16,
  },
  testimonialsGrid: {
    gap: 32,
  },
  testimonialCard: {
    backgroundColor: '#F8F7F4',
  },
  testimonialContent: {
    padding: 32,
  },
  testimonialStar: {
    fontSize: 20,
    marginBottom: 8,
  },
  testimonialQuote: {
    fontSize: 18,
    color: '#2E4B7A',
    fontStyle: 'italic',
    marginBottom: 24,
    lineHeight: 26,
  },
  testimonialAuthor: {
    color: '#9B7A5D',
    fontWeight: '500',
    fontFamily: 'serif',
  },
  ctaSection: {
    backgroundColor: '#2E4B7A',
    paddingVertical: 80,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: 'serif',
  },
  ctaSubtitle: {
    fontSize: 18,
    color: '#bfdbfe',
    textAlign: 'center',
    marginBottom: 32,
    maxWidth: 400,
  },
  ctaButton: {
    backgroundColor: '#9B7A5D',
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
});
