import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Card } from '../../components/ui/card';
import { Colors } from '../../lib/constants';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    question: "How do I create a recipe book?",
    answer: "To create a recipe book, go to the Recipe Books page and click 'Create New Recipe Book'. Fill in the details like title, description, and settings, then start adding recipes.",
    category: "Getting Started"
  },
  {
    question: "How many recipes can I add to a book?",
    answer: "The number of recipes depends on your chosen plan. Basic allows up to 25 recipes, Standard up to 50, and Premium up to 100 recipes per book.",
    category: "Plans & Pricing"
  },
  {
    question: "Can I invite family members to contribute?",
    answer: "Yes! As an organizer, you can invite family members to contribute recipes to your book. They'll receive an email invitation with instructions on how to add their recipes.",
    category: "Collaboration"
  },
  {
    question: "How do I print my recipe book?",
    answer: "Once your book is complete, you can order printed copies through our print-on-demand service. Choose from various binding options and sizes.",
    category: "Printing"
  },
  {
    question: "Can I edit recipes after they're submitted?",
    answer: "Contributors can edit their own recipes. Organizers can edit any recipe in their books. Admins have full editing access to all recipes.",
    category: "Editing"
  },
  {
    question: "What image formats are supported?",
    answer: "We support JPEG, PNG, and WebP image formats. Images should be at least 800x600 pixels for best print quality.",
    category: "Images"
  },
  {
    question: "How do I cancel my subscription?",
    answer: "You can cancel your subscription at any time from your account settings. Your books will remain accessible until the end of your billing period.",
    category: "Account"
  },
  {
    question: "Is my data secure?",
    answer: "Yes, we use industry-standard encryption to protect your data. Your recipes and personal information are stored securely and never shared with third parties.",
    category: "Security"
  }
];

const categories = Array.from(new Set(faqData.map(item => item.category)));

export default function FAQ() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());

  const filteredFAQs = selectedCategory
    ? faqData.filter(item => item.category === selectedCategory)
    : faqData;

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Frequently Asked Questions</Text>
        <Text style={styles.subtitle}>
          Find answers to common questions about using our recipe book platform
        </Text>

        {/* Category Filter */}
        <View style={styles.categorySection}>
          <Text style={styles.categoryTitle}>Filter by Category</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
            <View style={styles.categoryButtons}>
              <TouchableOpacity
                style={[
                  styles.categoryButton,
                  selectedCategory === null && styles.selectedCategoryButton
                ]}
                onPress={() => setSelectedCategory(null)}
              >
                <Text style={[
                  styles.categoryButtonText,
                  selectedCategory === null && styles.selectedCategoryButtonText
                ]}>
                  All
                </Text>
              </TouchableOpacity>

              {categories.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category && styles.selectedCategoryButton
                  ]}
                  onPress={() => setSelectedCategory(category)}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    selectedCategory === category && styles.selectedCategoryButtonText
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* FAQ Items */}
        <View style={styles.faqSection}>
          {filteredFAQs.map((item, index) => (
            <Card key={index} style={styles.faqCard}>
              <TouchableOpacity
                style={styles.faqHeader}
                onPress={() => toggleExpanded(index)}
              >
                <View style={styles.faqHeaderContent}>
                  <Text style={styles.faqQuestion}>{item.question}</Text>
                  <Text style={styles.faqCategory}>{item.category}</Text>
                </View>
                <Text style={styles.expandIcon}>
                  {expandedItems.has(index) ? '−' : '+'}
                </Text>
              </TouchableOpacity>

              {expandedItems.has(index) && (
                <View style={styles.faqAnswer}>
                  <Text style={styles.faqAnswerText}>{item.answer}</Text>
                </View>
              )}
            </Card>
          ))}
        </View>

        {/* Contact Section */}
        <Card style={styles.contactCard}>
          <View style={styles.contactContent}>
            <Text style={styles.contactTitle}>Still have questions?</Text>
            <Text style={styles.contactText}>
              Can't find what you're looking for? Our support team is here to help.
            </Text>
            <TouchableOpacity style={styles.contactButton}>
              <Text style={styles.contactButtonText}>Contact Support</Text>
            </TouchableOpacity>
          </View>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: Colors.foreground,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: Colors.mutedForeground,
    marginBottom: 32,
    lineHeight: 24,
  },
  categorySection: {
    marginBottom: 32,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: Colors.foreground,
  },
  categoryScroll: {
    marginBottom: 8,
  },
  categoryButtons: {
    flexDirection: 'row',
    gap: 8,
    paddingRight: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.card,
  },
  selectedCategoryButton: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  categoryButtonText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    fontWeight: '500',
  },
  selectedCategoryButtonText: {
    color: Colors.primaryForeground,
  },
  faqSection: {
    gap: 12,
    marginBottom: 32,
  },
  faqCard: {
    overflow: 'hidden',
  },
  faqHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  faqHeaderContent: {
    flex: 1,
    marginRight: 12,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: 4,
  },
  faqCategory: {
    fontSize: 12,
    color: Colors.mutedForeground,
    backgroundColor: Colors.muted,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  expandIcon: {
    fontSize: 20,
    color: Colors.mutedForeground,
    fontWeight: 'bold',
  },
  faqAnswer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  faqAnswerText: {
    fontSize: 14,
    color: Colors.foreground,
    lineHeight: 20,
  },
  contactCard: {
    backgroundColor: Colors.muted,
    borderColor: Colors.border,
  },
  contactContent: {
    padding: 24,
    alignItems: 'center',
  },
  contactTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: 8,
  },
  contactText: {
    fontSize: 14,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  contactButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
  },
  contactButtonText: {
    color: Colors.primaryForeground,
    fontWeight: '600',
    fontSize: 14,
  },
});
