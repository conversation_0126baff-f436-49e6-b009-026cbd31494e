import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Spacing, BorderRadius, API_URL } from '../lib/constants';
import { useToast } from '../hooks/use-toast';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Order {
  id: number;
  blurbOrderId: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  trackingUrl?: string;
}

interface TrackingDetails {
  orderId: number;
  blurbOrderId: string;
  status: string;
  trackingNumber?: string;
  carrier?: string;
  estimatedDelivery?: string;
  trackingHistory: Array<{
    date: string;
    status: string;
    description: string;
    location?: string;
  }>;
  shippingAddress: {
    name: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  orderDate: string;
  lastUpdated: string;
}

export default function OrderTracking() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [trackingDetails, setTrackingDetails] = useState<TrackingDetails | null>(null);
  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  // Get user orders
  const fetchOrders = async () => {
    try {
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/blurb/orders`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get orders');
      }

      const result = await response.json();
      setOrders(result.orders);

    } catch (error) {
      console.error('Error getting orders:', error);
      toast({
        title: "Get Orders Failed",
        description: error instanceof Error ? error.message : "Failed to get orders.",
        variant: "destructive",
      });
    }
  };

  // Get detailed tracking for an order
  const fetchTrackingDetails = async (orderId: number) => {
    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/blurb/order/${orderId}/tracking`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get tracking details');
      }

      const result = await response.json();
      setTrackingDetails(result);
      setSelectedOrderId(orderId);

      toast({
        title: "Tracking Details Retrieved",
        description: `Status: ${result.status}`,
      });

    } catch (error) {
      console.error('Error getting tracking:', error);
      toast({
        title: "Tracking Failed",
        description: error instanceof Error ? error.message : "Failed to get tracking details.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Test Blurb API configuration
  const testBlurbAPI = async () => {
    try {
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/blurb/test`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to test Blurb API');
      }

      const result = await response.json();

      Alert.alert(
        "Blurb API Test",
        `API configured: ${result.configured}\nEnvironment: ${result.environment}`,
        [{ text: "OK" }]
      );

    } catch (error) {
      console.error('Error testing Blurb API:', error);
      Alert.alert(
        "Blurb API Test Failed",
        error instanceof Error ? error.message : "Failed to test Blurb API."
      );
    }
  };

  // Refresh orders
  const onRefresh = async () => {
    setIsRefreshing(true);
    await fetchOrders();
    setIsRefreshing(false);
  };

  // Load orders on component mount
  useEffect(() => {
    fetchOrders();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending': return Colors.mutedForeground;
      case 'processing': return '#f59e0b';
      case 'printing': return '#3b82f6';
      case 'shipped': return '#10b981';
      case 'delivered': return '#059669';
      default: return Colors.mutedForeground;
    }
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>Order Tracking</Text>
        <Text style={styles.subtitle}>Track your recipe book print orders</Text>

        <TouchableOpacity style={styles.testButton} onPress={testBlurbAPI}>
          <Icon name="settings" size={20} color={Colors.primary} />
          <Text style={styles.testButtonText}>Test API</Text>
        </TouchableOpacity>
      </View>

      {orders.length === 0 ? (
        <View style={styles.emptyState}>
          <Icon name="local-shipping" size={48} color={Colors.mutedForeground} />
          <Text style={styles.emptyTitle}>No Orders Found</Text>
          <Text style={styles.emptyDescription}>
            You haven't placed any print orders yet.
          </Text>
        </View>
      ) : (
        <View style={styles.ordersContainer}>
          <Text style={styles.sectionTitle}>Your Orders ({orders.length})</Text>

          {orders.map((order) => (
            <View key={order.id} style={styles.orderCard}>
              <View style={styles.orderHeader}>
                <View style={styles.orderInfo}>
                  <Text style={styles.orderTitle}>Order #{order.id}</Text>
                  <Text style={styles.orderSubtitle}>
                    Blurb Order: {order.blurbOrderId}
                  </Text>
                  <View style={styles.statusContainer}>
                    <View style={[
                      styles.statusBadge,
                      { backgroundColor: getStatusColor(order.status) + '20' }
                    ]}>
                      <Text style={[
                        styles.statusText,
                        { color: getStatusColor(order.status) }
                      ]}>
                        {order.status}
                      </Text>
                    </View>
                  </View>
                  <Text style={styles.orderDate}>
                    Created: {formatDate(order.createdAt)}
                  </Text>
                </View>

                <TouchableOpacity
                  style={styles.trackButton}
                  onPress={() => fetchTrackingDetails(order.id)}
                  disabled={isLoading}
                >
                  <Icon
                    name="local-shipping"
                    size={20}
                    color={Colors.primary}
                  />
                  <Text style={styles.trackButtonText}>Track</Text>
                </TouchableOpacity>
              </View>

              {selectedOrderId === order.id && trackingDetails && (
                <View style={styles.trackingDetails}>
                  <Text style={styles.trackingTitle}>Tracking Details</Text>

                  <View style={styles.trackingInfo}>
                    <View style={styles.trackingRow}>
                      <Text style={styles.trackingLabel}>Status:</Text>
                      <Text style={styles.trackingValue}>{trackingDetails.status}</Text>
                    </View>

                    {trackingDetails.trackingNumber && (
                      <View style={styles.trackingRow}>
                        <Text style={styles.trackingLabel}>Tracking:</Text>
                        <Text style={styles.trackingValue}>{trackingDetails.trackingNumber}</Text>
                      </View>
                    )}

                    {trackingDetails.carrier && (
                      <View style={styles.trackingRow}>
                        <Text style={styles.trackingLabel}>Carrier:</Text>
                        <Text style={styles.trackingValue}>{trackingDetails.carrier}</Text>
                      </View>
                    )}

                    {trackingDetails.estimatedDelivery && (
                      <View style={styles.trackingRow}>
                        <Text style={styles.trackingLabel}>Est. Delivery:</Text>
                        <Text style={styles.trackingValue}>
                          {formatDate(trackingDetails.estimatedDelivery)}
                        </Text>
                      </View>
                    )}
                  </View>

                  {trackingDetails.trackingHistory && trackingDetails.trackingHistory.length > 0 && (
                    <View style={styles.historyContainer}>
                      <Text style={styles.historyTitle}>Tracking History</Text>
                      {trackingDetails.trackingHistory.map((event, idx) => (
                        <View key={idx} style={styles.historyItem}>
                          <View style={styles.historyDate}>
                            <Text style={styles.historyDateText}>
                              {formatDate(event.date)}
                            </Text>
                          </View>
                          <View style={styles.historyContent}>
                            <Text style={styles.historyStatus}>{event.status}</Text>
                            <Text style={styles.historyDescription}>
                              {event.description}
                            </Text>
                            {event.location && (
                              <Text style={styles.historyLocation}>
                                📍 {event.location}
                              </Text>
                            )}
                          </View>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              )}
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    padding: Spacing.md,
    backgroundColor: Colors.primary + '10',
    borderRadius: BorderRadius.md,
    alignSelf: 'flex-start',
  },
  testButtonText: {
    color: Colors.primary,
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
    marginTop: 100,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  emptyDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  ordersContainer: {
    padding: Spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  orderCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderInfo: {
    flex: 1,
  },
  orderTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  orderSubtitle: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
  },
  statusContainer: {
    marginBottom: Spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  orderDate: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  trackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    padding: Spacing.sm,
    backgroundColor: Colors.primary + '10',
    borderRadius: BorderRadius.md,
  },
  trackButtonText: {
    color: Colors.primary,
    fontWeight: '500',
    fontSize: 12,
  },
  trackingDetails: {
    marginTop: Spacing.lg,
    padding: Spacing.md,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
  },
  trackingTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  trackingInfo: {
    marginBottom: Spacing.md,
  },
  trackingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.sm,
  },
  trackingLabel: {
    fontSize: 14,
    color: Colors.mutedForeground,
    fontWeight: '500',
  },
  trackingValue: {
    fontSize: 14,
    color: Colors.foreground,
    flex: 1,
    textAlign: 'right',
  },
  historyContainer: {
    marginTop: Spacing.md,
  },
  historyTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  historyItem: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
    paddingBottom: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  historyDate: {
    width: 80,
    marginRight: Spacing.md,
  },
  historyDateText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  historyContent: {
    flex: 1,
  },
  historyStatus: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  historyDescription: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  historyLocation: {
    fontSize: 11,
    color: Colors.mutedForeground,
  },
});
