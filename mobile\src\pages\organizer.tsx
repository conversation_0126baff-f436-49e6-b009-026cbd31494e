import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../hooks/use-auth';
import { UserRole, PricingTier, PricingModel, API_URL } from '../lib/constants';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { useToast } from '../hooks/use-toast';
import { useLocation } from '../lib/router';
import { ProtectedRoute } from '../components/auth/protected-route';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Colors, Spacing, BorderRadius } from '../lib/constants';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Project {
  id: number;
  name: string;
  description: string;
  status: string;
  pricingTier?: string;
  maxContributors?: number;
  createdAt: string;
  contributors: Array<{
    id: number;
    name: string;
    email: string;
    status: 'pending' | 'accepted' | 'rejected';
  }>;
}

export default function OrganizerDashboard() {
  const { user, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteDeadline, setInviteDeadline] = useState('');
  const [inviteReminderFrequency, setInviteReminderFrequency] = useState('weekly');
  const [isLoading, setIsLoading] = useState(false);

  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    pricingTier: PricingTier.SMALL,
    maxContributors: PricingModel[PricingTier.SMALL].maxContributors
  });

  const [pricingInfo, setPricingInfo] = useState({
    tier: PricingTier.SMALL,
    basePrice: PricingModel[PricingTier.SMALL].basePrice,
    pagePrice: 0,
    totalPrice: PricingModel[PricingTier.SMALL].basePrice,
    estimatedPages: 20
  });

  // Fetch projects based on view mode
  const { data: projects, isLoading: projectsLoading } = useQuery<Project[]>({
    queryKey: ["recipeBooks", user?.role],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const endpoint = `${API_URL}/organizer/my-projects`;

      const response = await fetch(endpoint, {
        headers: {
          "Authorization": `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch recipe books');
      }

      const data = await response.json();
      return data.projects || [];
    },
    enabled: !!user && user.role === 'organizer'
  });

  const handleCreateProject = async () => {
    if (!newProject.name.trim() || !newProject.description.trim()) {
      Alert.alert("Validation Error", "Please fill in all required fields");
      return;
    }

    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/organizer/projects`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name: newProject.name,
          description: newProject.description,
          pricingTier: newProject.pricingTier,
          maxContributors: newProject.maxContributors
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create recipe book');
      }

      // Invalidate the query to force a refresh
      queryClient.invalidateQueries({ queryKey: ["recipeBooks", user?.role] });
      setIsCreateDialogOpen(false);
      setNewProject({
        name: '',
        description: '',
        pricingTier: PricingTier.SMALL,
        maxContributors: PricingModel[PricingTier.SMALL].maxContributors
      });
      toast({
        title: "Success",
        description: "Recipe book created successfully",
      });
    } catch (error) {
      console.error('Error creating recipe book:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create recipe book",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInviteContributor = async (project: Project) => {
    // Check if we've reached the maximum number of contributors for this tier
    const currentContributorCount = project.contributors?.length || 0;
    const maxContributors = project.maxContributors ||
      (project.pricingTier ? PricingModel[project.pricingTier as keyof typeof PricingModel]?.maxContributors : 10);

    if (currentContributorCount >= maxContributors) {
      Alert.alert(
        "Maximum Contributors Reached",
        `You have reached the maximum of ${maxContributors} contributors for the ${project.pricingTier || 'current'} tier. Please upgrade to a higher tier to add more contributors.`
      );
      return;
    }

    setSelectedProject(project);
    setIsInviteDialogOpen(true);
  };

  const handleSendInvite = async () => {
    if (!selectedProject) return;

    try {
      setIsLoading(true);
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/organizer/projects/${selectedProject.id}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          email: inviteEmail,
          deadline: inviteDeadline ? new Date(inviteDeadline).toISOString() : undefined,
          reminderFrequency: inviteReminderFrequency
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to invite contributor');
      }

      // Invalidate the query to force a refresh
      queryClient.invalidateQueries({ queryKey: ["recipeBooks", user?.role] });
      setIsInviteDialogOpen(false);
      setInviteEmail('');
      setInviteDeadline('');
      setInviteReminderFrequency('weekly');
      toast({
        title: "Success",
        description: "Invitation sent successfully",
      });
    } catch (error) {
      console.error('Error inviting contributor:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitation",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (authLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Please log in to access the organizer dashboard.</Text>
      </View>
    );
  }

  return (
    <ProtectedRoute allowedRoles={[UserRole.ORGANIZER]}>
      <ScrollView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Organizer Dashboard</Text>
          <Button
            onPress={() => setIsCreateDialogOpen(true)}
            style={styles.createButton}
          >
            <Icon name="add" size={16} color={Colors.primaryForeground} />
            <Text style={styles.createButtonText}>Create New Recipe Book</Text>
          </Button>
        </View>

        <View style={styles.content}>
          {projectsLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading recipe books...</Text>
            </View>
          ) : projects && projects.length > 0 ? (
            <View style={styles.projectsList}>
              {projects.map((project) => (
                <Card key={project.id} style={styles.projectCard}>
                  <CardHeader>
                    <View style={styles.projectHeader}>
                      <View style={styles.projectHeaderLeft}>
                        <Text style={styles.projectTitle}>{project.name}</Text>
                        <Text style={styles.projectDate}>
                          Created on {new Date(project.createdAt).toLocaleDateString()}
                        </Text>
                      </View>
                      <View style={styles.projectActions}>
                        <Button
                          variant="outline"
                          size="sm"
                          onPress={() => handleInviteContributor(project)}
                          style={styles.actionButton}
                        >
                          Invite Contributor
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onPress={() => setLocation(`/recipe-books/${project.id}`)}
                          style={styles.actionButton}
                        >
                          View Details
                        </Button>
                      </View>
                    </View>
                  </CardHeader>
                  <CardContent>
                    <Text style={styles.projectDescription}>{project.description}</Text>
                    <View style={styles.projectMeta}>
                      <View style={styles.projectStats}>
                        <Text style={styles.statText}>
                          Contributors: {project.contributors?.length || 0}
                          {project.maxContributors && <Text> / {project.maxContributors}</Text>}
                        </Text>
                        {project.pricingTier && (
                          <Text style={styles.statText}>
                            Tier: {PricingModel[project.pricingTier as keyof typeof PricingModel]?.name || project.pricingTier}
                          </Text>
                        )}
                      </View>
                      <View style={styles.statusContainer}>
                        <Text style={styles.statusText}>
                          Status: {project.status}
                        </Text>
                      </View>
                    </View>
                  </CardContent>
                </Card>
              ))}
            </View>
          ) : (
            <View style={styles.emptyState}>
              <Icon name="book" size={48} color={Colors.mutedForeground} style={styles.emptyIcon} />
              <Text style={styles.emptyText}>
                No recipe books found. Create your first recipe book!
              </Text>
              <Button
                onPress={() => setIsCreateDialogOpen(true)}
                style={styles.emptyButton}
              >
                Create Recipe Book
              </Button>
            </View>
          )}
        </View>

        {/* Create Project Modal */}
        {isCreateDialogOpen && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Create New Recipe Book</Text>
                <Text style={styles.modalDescription}>
                  Create a new recipe book and invite contributors to share their recipes.
                </Text>
              </View>

              <View style={styles.modalBody}>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Recipe Book Name</Text>
                  <Input
                    value={newProject.name}
                    onChangeText={(text) => setNewProject({ ...newProject, name: text })}
                    placeholder="Enter recipe book name"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Description</Text>
                  <TextInput
                    style={styles.textArea}
                    value={newProject.description}
                    onChangeText={(text) => setNewProject({ ...newProject, description: text })}
                    placeholder="Describe your recipe book"
                    multiline
                    numberOfLines={4}
                    textAlignVertical="top"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Cookbook Size</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={newProject.pricingTier}
                      onValueChange={(value) => {
                        const tierData = PricingModel[value as keyof typeof PricingModel];
                        setNewProject({
                          ...newProject,
                          pricingTier: value,
                          maxContributors: tierData.maxContributors
                        });

                        // Update pricing info when tier changes
                        setPricingInfo(prevInfo => ({
                          ...prevInfo,
                          tier: value,
                          basePrice: tierData.basePrice,
                          totalPrice: tierData.basePrice + prevInfo.pagePrice
                        }));
                      }}
                      style={styles.picker}
                    >
                      <Picker.Item
                        label={`${PricingModel[PricingTier.SMALL].name} - ${PricingModel[PricingTier.SMALL].description}`}
                        value={PricingTier.SMALL}
                      />
                      <Picker.Item
                        label={`${PricingModel[PricingTier.MEDIUM].name} - ${PricingModel[PricingTier.MEDIUM].description}`}
                        value={PricingTier.MEDIUM}
                      />
                      <Picker.Item
                        label={`${PricingModel[PricingTier.LARGE].name} - ${PricingModel[PricingTier.LARGE].description}`}
                        value={PricingTier.LARGE}
                      />
                    </Picker>
                  </View>
                </View>

                {/* Pricing Information */}
                <View style={styles.pricingCard}>
                  <Text style={styles.pricingTitle}>Pricing Information</Text>
                  <View style={styles.pricingDetails}>
                    <View style={styles.pricingRow}>
                      <Text style={styles.pricingLabel}>Selected Tier:</Text>
                      <Text style={styles.pricingValue}>{PricingModel[newProject.pricingTier as keyof typeof PricingModel].name}</Text>
                    </View>
                    <View style={styles.pricingRow}>
                      <Text style={styles.pricingLabel}>Base Price:</Text>
                      <Text style={styles.pricingValue}>${pricingInfo.basePrice}</Text>
                    </View>
                    <View style={styles.pricingRow}>
                      <Text style={styles.pricingLabel}>Max Contributors:</Text>
                      <Text style={styles.pricingValue}>{newProject.maxContributors}</Text>
                    </View>
                    <View style={styles.pricingRow}>
                      <Text style={styles.pricingLabel}>Estimated Pages:</Text>
                      <Text style={styles.pricingValue}>{pricingInfo.estimatedPages}</Text>
                    </View>
                    <View style={[styles.pricingRow, styles.totalRow]}>
                      <Text style={styles.totalLabel}>Total Price:</Text>
                      <Text style={styles.totalValue}>${pricingInfo.totalPrice}</Text>
                    </View>
                  </View>
                </View>
              </View>

              <View style={styles.modalFooter}>
                <Button
                  variant="outline"
                  onPress={() => setIsCreateDialogOpen(false)}
                  style={styles.modalButton}
                >
                  Cancel
                </Button>
                <Button
                  onPress={handleCreateProject}
                  disabled={isLoading}
                  style={styles.modalButton}
                >
                  {isLoading ? "Creating..." : "Create Recipe Book"}
                </Button>
              </View>
            </View>
          </View>
        )}

        {/* Invite Contributor Modal */}
        {isInviteDialogOpen && (
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Invite Contributor</Text>
                <Text style={styles.modalDescription}>
                  Invite a contributor to join your project.
                </Text>
              </View>

              <View style={styles.modalBody}>
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Email Address</Text>
                  <Input
                    value={inviteEmail}
                    onChangeText={setInviteEmail}
                    placeholder="Enter contributor's email"
                    keyboardType="email-address"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Submission Deadline (Optional)</Text>
                  <Input
                    value={inviteDeadline}
                    onChangeText={setInviteDeadline}
                    placeholder="YYYY-MM-DD HH:MM"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>Reminder Frequency</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={inviteReminderFrequency}
                      onValueChange={setInviteReminderFrequency}
                      style={styles.picker}
                    >
                      <Picker.Item label="Every 1 minute (Test)" value="1min" />
                      <Picker.Item label="Every 2 minutes (Test)" value="2min" />
                      <Picker.Item label="Every 5 minutes (Test)" value="5min" />
                      <Picker.Item label="Every 15 minutes (Test)" value="15min" />
                      <Picker.Item label="Every 30 minutes (Test)" value="30min" />
                      <Picker.Item label="Every hour (Test)" value="1hour" />
                      <Picker.Item label="Daily" value="daily" />
                      <Picker.Item label="Weekly" value="weekly" />
                      <Picker.Item label="Bi-weekly" value="biweekly" />
                      <Picker.Item label="Monthly" value="monthly" />
                    </Picker>
                  </View>
                </View>
              </View>

              <View style={styles.modalFooter}>
                <Button
                  variant="outline"
                  onPress={() => setIsInviteDialogOpen(false)}
                  style={styles.modalButton}
                >
                  Cancel
                </Button>
                <Button
                  onPress={handleSendInvite}
                  disabled={isLoading}
                  style={styles.modalButton}
                >
                  {isLoading ? "Sending..." : "Send Invitation"}
                </Button>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </ProtectedRoute>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  errorText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    padding: Spacing.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
    marginRight: Spacing.md,
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  createButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
  content: {
    padding: Spacing.lg,
  },
  projectsList: {
    gap: Spacing.lg,
  },
  projectCard: {
    marginBottom: Spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    gap: Spacing.md,
  },
  projectHeaderLeft: {
    flex: 1,
  },
  projectTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  projectDate: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  projectActions: {
    gap: Spacing.sm,
  },
  actionButton: {
    marginBottom: Spacing.xs,
  },
  projectDescription: {
    fontSize: 16,
    color: Colors.mutedForeground,
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  projectMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  projectStats: {
    flex: 1,
  },
  statText: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginBottom: Spacing.xs,
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  statusText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  emptyState: {
    alignItems: 'center',
    padding: Spacing.xl,
    marginTop: Spacing.xl,
  },
  emptyIcon: {
    opacity: 0.5,
    marginBottom: Spacing.md,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  emptyButton: {
    marginTop: Spacing.md,
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    margin: Spacing.lg,
    maxHeight: '90%',
    width: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    padding: Spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  modalDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  modalBody: {
    padding: Spacing.lg,
    maxHeight: 400,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.background,
    color: Colors.foreground,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.background,
  },
  picker: {
    height: 50,
    color: Colors.foreground,
  },
  pricingCard: {
    backgroundColor: Colors.muted + '30',
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    marginTop: Spacing.md,
  },
  pricingTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  pricingDetails: {
    gap: Spacing.sm,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pricingLabel: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  pricingValue: {
    fontSize: 14,
    color: Colors.foreground,
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  modalFooter: {
    flexDirection: 'row',
    gap: Spacing.md,
    padding: Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  modalButton: {
    flex: 1,
  },
});