import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  Alert
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { useToast } from '../../hooks/use-toast';
import { useAuth } from '../../hooks/use-auth';
import { useLocation } from '../../lib/router';
import { Colors, Spacing, BorderRadius, API_URL } from '../../lib/constants';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Recipe {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  contributor: {
    name: string;
  };
  createdAt: string;
  projectId: number;
}

interface Project {
  id: number;
  name: string;
  description: string;
}

export default function RecipeApprovals() {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();
  const [, setLocation] = useLocation();

  useEffect(() => {
    if (user && user.role !== 'organizer' && user.role !== 'admin') {
      setLocation("/");
      toast({
        title: "Access Denied",
        description: "Only organizers can access this page",
        variant: "destructive",
      });
      return;
    }

    const fetchData = async () => {
      try {
        const token = await AsyncStorage.getItem('token');
        if (!token) {
          throw new Error('Not authenticated');
        }

        // Fetch projects
        const projectResponse = await fetch(`${API_URL}/organizer/my-projects`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (!projectResponse.ok) throw new Error("Failed to fetch projects");
        const projectData = await projectResponse.json();
        setProjects(projectData.projects);

        // Fetch pending recipes for each project
        const pendingRecipes: Recipe[] = [];
        for (const project of projectData.projects) {
          const recipeResponse = await fetch(`${API_URL}/organizer/projects/${project.id}/pending-recipes`, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
          if (recipeResponse.ok) {
            const recipeData = await recipeResponse.json();
            pendingRecipes.push(...recipeData);
          }
        }
        setRecipes(pendingRecipes);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load recipes. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.role === 'organizer' || user?.role === 'admin') {
      fetchData();
    }
  }, [toast, user, setLocation]);

  const handleApproval = async (recipeId: number, status: 'approved' | 'rejected') => {
    try {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`${API_URL}/organizer/recipes/${recipeId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ status })
      });

      if (!response.ok) throw new Error('Failed to update recipe status');

      // Update local state
      setRecipes(recipes.filter(r => r.id !== recipeId));

      toast({
        title: "Success",
        description: `Recipe ${status} successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update recipe status",
        variant: "destructive",
      });
    }
  };

  const confirmApproval = (recipe: Recipe, status: 'approved' | 'rejected') => {
    const action = status === 'approved' ? 'approve' : 'reject';
    Alert.alert(
      `${action.charAt(0).toUpperCase() + action.slice(1)} Recipe`,
      `Are you sure you want to ${action} "${recipe.title}" by ${recipe.contributor.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: action.charAt(0).toUpperCase() + action.slice(1),
          style: status === 'approved' ? 'default' : 'destructive',
          onPress: () => handleApproval(recipe.id, status)
        }
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Recipe Approvals</Text>
      </View>

      <View style={styles.content}>
        {recipes.length === 0 ? (
          <Card style={styles.emptyCard}>
            <CardContent>
              <View style={styles.emptyState}>
                <Icon name="check-circle" size={48} color={Colors.mutedForeground} style={styles.emptyIcon} />
                <Text style={styles.emptyText}>No recipes pending approval.</Text>
              </View>
            </CardContent>
          </Card>
        ) : (
          <View style={styles.recipesList}>
            {recipes.map((recipe) => (
              <Card key={recipe.id} style={styles.recipeCard}>
                <CardContent>
                  <View style={styles.recipeHeader}>
                    <View style={styles.recipeInfo}>
                      <View style={styles.recipeTitleRow}>
                        <Text style={styles.recipeTitle}>{recipe.title}</Text>
                        <View style={styles.pendingBadge}>
                          <Text style={styles.pendingBadgeText}>Pending</Text>
                        </View>
                      </View>
                      <Text style={styles.recipeDescription}>{recipe.description}</Text>
                      <View style={styles.recipeMetaRow}>
                        <Text style={styles.metaText}>By: {recipe.contributor.name}</Text>
                        <Text style={styles.metaText}>
                          Submitted: {new Date(recipe.createdAt).toLocaleDateString()}
                        </Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.actionButtons}>
                    <Button
                      onPress={() => confirmApproval(recipe, 'approved')}
                      style={styles.approveButton}
                    >
                      <Icon name="check-circle" size={16} color={Colors.primaryForeground} />
                      <Text style={styles.approveButtonText}>Approve</Text>
                    </Button>
                    <Button
                      onPress={() => confirmApproval(recipe, 'rejected')}
                      variant="destructive"
                      style={styles.rejectButton}
                    >
                      <Icon name="cancel" size={16} color={Colors.primaryForeground} />
                      <Text style={styles.rejectButtonText}>Reject</Text>
                    </Button>
                  </View>
                </CardContent>
              </Card>
            ))}
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  content: {
    padding: Spacing.lg,
  },
  emptyCard: {
    marginTop: Spacing.xl,
  },
  emptyState: {
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyIcon: {
    opacity: 0.5,
    marginBottom: Spacing.md,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
  recipesList: {
    gap: Spacing.lg,
  },
  recipeCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  recipeHeader: {
    marginBottom: Spacing.lg,
  },
  recipeInfo: {
    flex: 1,
  },
  recipeTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  recipeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
  },
  pendingBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.full,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  pendingBadgeText: {
    fontSize: 12,
    color: Colors.mutedForeground,
    fontWeight: '500',
  },
  recipeDescription: {
    fontSize: 16,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
    lineHeight: 24,
  },
  recipeMetaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: Spacing.md,
  },
  metaText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  approveButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    backgroundColor: '#16a34a', // emerald-600
    borderColor: '#16a34a',
  },
  approveButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
  rejectButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  rejectButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
});