import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../../lib/router';
import { useToast } from '../../hooks/use-toast';
import { useAuth } from '../../hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Colors, Spacing, BorderRadius, API_URL } from '../../lib/constants';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Contributor {
  id: number;
  name: string;
  email: string;
  status: string;
}

interface Recipe {
  id: number;
  title: string;
  description: string;
  images: string[];
  role: string;
  tags: string[];
  ingredients: { name: string; amount: number; unit: string }[];
  instructions: string[];
  createdAt: string;
  status: string;
  contributor: {
    id: number;
    name: string;
  };
  project: {
    id: number;
    organizerId: number;
  };
}

interface Project {
  organizer: any;
  id: number;
  name: string;
  description: string;
  status: string;
  role: string;
  createdAt: string;
  contributors: Contributor[];
}

export default function ProjectDetails() {
  const [location, setLocation] = useLocation();
  const [project, setProject] = useState<Project | null>(null);
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recipeToDelete, setRecipeToDelete] = useState<Recipe | null>(null);
  const [expandedRecipes, setExpandedRecipes] = useState<Record<number, { ingredients: boolean, instructions: boolean }>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<Record<number, string>>({});
  const { toast } = useToast();
  const { user } = useAuth();

  // Extract project ID from location
  const projectId = location.split('/').pop();

  useEffect(() => {
    if (!user) {
      setLocation("/login");
      return;
    }

    const fetchProjectDetails = async () => {
      try {
        const token = await AsyncStorage.getItem("token");
        if (!token) {
          throw new Error("No authentication token found");
        }

        let endpoint = '';
        // Use different endpoints based on user role
        if (user.role === 'organizer') {
          endpoint = `${API_URL}/organizer/all-projects`;
        } else if (user.role === 'contributor') {
          endpoint = `${API_URL}/contributor/projects/${projectId}`;
        } else if (user.role === 'admin') {
          endpoint = `${API_URL}/admin/projects/${projectId}`;
        } else {
          throw new Error("User role not supported");
        }

        const response = await fetch(endpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const errorData = await response.json();
            console.error('Error response:', errorData);
            throw new Error(errorData.message || "Failed to fetch project details");
          } else {
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();

        // Handle different response formats based on endpoint
        let foundProject;
        if (user.role === 'organizer') {
          // This endpoint returns a list of projects
          const projectIdNum = projectId ? parseInt(projectId) : 0;
          foundProject = data.projects.find((p: Project) => p.id === projectIdNum);
        } else if (user.role === 'contributor' || user.role === 'admin') {
          // These endpoints return a single project
          foundProject = data.project;
        }

        if (foundProject) {
          setProject(foundProject);
        } else {
          throw new Error("Project not found");
        }
      } catch (error) {
        console.error("Error fetching project details:", error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load project details. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    const fetchRecipes = async () => {
      try {
        console.log('Fetching recipes for project:', projectId);
        const token = await AsyncStorage.getItem("token");
        if (!token) {
          throw new Error("No authentication token found");
        }

        let endpoint = '';
        // Use different endpoints based on user role
        if (user.role === 'organizer') {
          endpoint = `${API_URL}/organizer/projects/${projectId}/recipes`;
        } else if (user.role === 'contributor') {
          endpoint = `${API_URL}/contributor/projects/${projectId}/recipes`;
        } else if (user.role === 'admin') {
          endpoint = `${API_URL}/admin/projects/${projectId}/recipes`;
        } else {
          throw new Error("User role not supported");
        }

        console.log('Making request to:', endpoint);
        const response = await fetch(endpoint, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const errorData = await response.json();
            console.error('Error response:', errorData);
            throw new Error(errorData.message || "Failed to fetch recipes");
          } else {
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();
        console.log('Received recipes data:', data);
        if (!data.recipes) {
          console.error('No recipes array in response:', data);
          throw new Error('Invalid response format: missing recipes array');
        }

        // Filter recipes to only show approved ones
        const approvedRecipes = data.recipes.filter((recipe: Recipe) => {
          console.log(`Recipe ${recipe.id} status:`, recipe.status);
          return recipe.status === 'approved';
        });
        console.log('Filtered approved recipes:', approvedRecipes);
        setRecipes(approvedRecipes);
      } catch (error) {
        console.error("Error fetching recipes:", error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load recipes. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchProjectDetails();
    fetchRecipes();
  }, [toast, user, projectId]);

  const handleEditRecipe = (recipe: Recipe) => {
    setLocation(`/recipes/${recipe.id}/edit`);
  };

  const handleDeleteRecipe = async (recipe: Recipe) => {
    try {
      const token = await AsyncStorage.getItem("token");
      const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to delete recipe");
        } else {
          const text = await response.text();
          console.error('Non-JSON error response:', text);
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }

      // Only update the UI if the deletion was successful
      setRecipes(recipes.filter(r => r.id !== recipe.id));
      toast({
        title: "Success",
        description: "Recipe deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting recipe:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete recipe. Please try again.",
        variant: "destructive",
      });
    }
  };

  const canEditRecipe = (recipe: Recipe) => {
    if (!project || !user) return false;
    // Check if user is creator, organizer, or admin
    return (
      recipe.contributor.id === user.id ||
      project.organizer.id === user.id ||
      user.role === 'admin'
    );
  };

  const toggleRecipeSection = (recipeId: number, section: 'ingredients' | 'instructions') => {
    setExpandedRecipes(prev => ({
      ...prev,
      [recipeId]: {
        ...prev[recipeId],
        [section]: !prev[recipeId]?.[section]
      }
    }));
  };

  const setRecipeTab = (recipeId: number, tab: string) => {
    setActiveTab(prev => ({ ...prev, [recipeId]: tab }));
  };

  // Add filtering logic
  const filteredRecipes = useMemo(() => {
    if (!searchQuery) return recipes;

    const searchLower = searchQuery.toLowerCase();
    return recipes.filter(recipe =>
      recipe.title.toLowerCase().includes(searchLower) ||
      recipe.description.toLowerCase().includes(searchLower) ||
      recipe.tags.some(tag => tag.toLowerCase().includes(searchLower)) ||
      recipe.contributor.name.toLowerCase().includes(searchLower)
    );
  }, [recipes, searchQuery]);

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (!project) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <CardContent>
            <Text style={styles.errorText}>Project not found.</Text>
          </CardContent>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>{project.name}</Text>
        <View style={styles.headerActions}>
          <Button
            variant="outline"
            onPress={() => setLocation("/recipe-books")}
            size="sm"
          >
            Back to Recipe Books
          </Button>
        </View>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color={Colors.mutedForeground} style={styles.searchIcon} />
          <Input
            placeholder="Search recipes by name, description, tags, or contributor..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
          />
        </View>
      </View>

      {/* Project Details Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Project Details</Text>
          <Text style={styles.cardDescription}>
            Created on {new Date(project.createdAt).toLocaleDateString()}
            {project.organizer?.name && (
              <Text style={styles.organizerText}> by {project.organizer.name}</Text>
            )}
          </Text>
        </CardHeader>
        <CardContent>
          <Text style={styles.projectDescription}>{project.description}</Text>
          <View style={styles.projectMeta}>
            <View style={styles.statusContainer}>
              <Text style={styles.metaLabel}>Status:</Text>
              <View style={[
                styles.statusBadge,
                project.status === 'active' ? styles.statusActive :
                project.status === 'pending' ? styles.statusPending :
                styles.statusInactive
              ]}>
                <Text style={[
                  styles.statusText,
                  project.status === 'active' ? styles.statusActiveText :
                  project.status === 'pending' ? styles.statusPendingText :
                  styles.statusInactiveText
                ]}>{project.status}</Text>
              </View>
            </View>
            <View style={styles.roleContainer}>
              <Text style={styles.metaLabel}>Role:</Text>
              <View style={[
                styles.roleBadge,
                project.role === 'admin' ? styles.roleAdmin :
                project.role === 'organizer' ? styles.roleOrganizer :
                styles.roleContributor
              ]}>
                <Icon
                  name={
                    project.role === 'admin' ? 'star' :
                    project.role === 'organizer' ? 'shield' :
                    'person'
                  }
                  size={12}
                  color={
                    project.role === 'admin' ? '#7c3aed' :
                    project.role === 'organizer' ? '#2563eb' :
                    '#6b7280'
                  }
                />
                <Text style={[
                  styles.roleText,
                  project.role === 'admin' ? styles.roleAdminText :
                  project.role === 'organizer' ? styles.roleOrganizerText :
                  styles.roleContributorText
                ]}>{project.role}</Text>
              </View>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Contributors Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Contributors</Text>
          <Text style={styles.cardDescription}>
            {project?.contributors?.length || 0} contributors
          </Text>
        </CardHeader>
        <CardContent>
          <View style={styles.contributorsList}>
            {project?.contributors?.map((contributor) => (
              <View key={contributor.id} style={styles.contributorItem}>
                <View style={styles.contributorInfo}>
                  <Text style={styles.contributorName}>{contributor.name}</Text>
                  <Text style={styles.contributorEmail}>{contributor.email}</Text>
                </View>
              </View>
            ))}
          </View>
        </CardContent>
      </Card>

      {/* Recipes Card */}
      <Card style={styles.recipesCard}>
        <CardHeader style={styles.recipesHeader}>
          <View style={styles.recipesHeaderContent}>
            <Icon name="restaurant" size={20} color={Colors.primary} />
            <Text style={styles.recipesTitle}>Recipes</Text>
          </View>
          <Text style={styles.cardDescription}>
            {recipes.length} recipes
          </Text>
        </CardHeader>
        <CardContent>
          {filteredRecipes.length > 0 ? (
            <View style={styles.recipesList}>
              {filteredRecipes.map((recipe, index) => (
                <View key={recipe.id} style={styles.recipeCard}>
                  <View style={styles.recipeHeader}>
                    <View style={styles.recipeHeaderLeft}>
                      <Text style={styles.recipeTitle}>{recipe.title}</Text>
                      <Text style={styles.recipeDescription}>{recipe.description}</Text>

                      {/* Role Badge */}
                      <View style={styles.recipeBadgeContainer}>
                        {recipe.role === 'admin' ? (
                          <View style={styles.adminBadge}>
                            <Icon name="star" size={14} color="#7c3aed" />
                            <Text style={styles.adminBadgeText}>Admin</Text>
                          </View>
                        ) : recipe.role === "organizer" ? (
                          <View style={styles.organizerBadge}>
                            <Icon name="shield" size={14} color="#2563eb" />
                            <Text style={styles.organizerBadgeText}>Organizer</Text>
                          </View>
                        ) : (
                          <View style={styles.contributorBadge}>
                            <Icon name="group" size={14} color="#6b7280" />
                            <Text style={styles.contributorBadgeText}>Contributor</Text>
                          </View>
                        )}
                      </View>
                    </View>

                    {/* Action Buttons */}
                    {canEditRecipe(recipe) && (
                      <View style={styles.recipeActions}>
                        <TouchableOpacity
                          onPress={() => handleEditRecipe(recipe)}
                          style={styles.actionButton}
                        >
                          <Icon name="edit" size={16} color={Colors.foreground} />
                        </TouchableOpacity>
                        <TouchableOpacity
                          onPress={() => {
                            Alert.alert(
                              'Delete Recipe',
                              'Are you sure you want to delete this recipe? This action cannot be undone.',
                              [
                                { text: 'Cancel', style: 'cancel' },
                                {
                                  text: 'Delete',
                                  style: 'destructive',
                                  onPress: () => handleDeleteRecipe(recipe)
                                }
                              ]
                            );
                          }}
                          style={[styles.actionButton, styles.deleteButton]}
                        >
                          <Icon name="delete" size={16} color={Colors.destructive} />
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>

                  {/* Tags */}
                  {recipe.tags && recipe.tags.length > 0 && (
                    <View style={styles.tagsContainer}>
                      {recipe.tags.map((tag, tagIndex) => (
                        <View key={tagIndex} style={styles.tag}>
                          <Icon name="local-offer" size={12} color={Colors.mutedForeground} />
                          <Text style={styles.tagText}>{tag}</Text>
                        </View>
                      ))}
                    </View>
                  )}

                  {/* Contributor Info */}
                  <View style={styles.recipeFooter}>
                    <View style={styles.contributorInfo}>
                      <Icon name="person" size={16} color={Colors.mutedForeground} />
                      <Text style={styles.contributorName}>{recipe.contributor.name}</Text>
                    </View>
                  </View>

                  {/* Recipe Content Tabs */}
                  <View style={styles.tabsContainer}>
                    <View style={styles.tabsHeader}>
                      <TouchableOpacity
                        style={[
                          styles.tab,
                          (activeTab[recipe.id] || 'recipe') === 'recipe' && styles.activeTab
                        ]}
                        onPress={() => setRecipeTab(recipe.id, 'recipe')}
                      >
                        <Icon name="info" size={16} color={
                          (activeTab[recipe.id] || 'recipe') === 'recipe' ? Colors.primary : Colors.mutedForeground
                        } />
                        <Text style={[
                          styles.tabText,
                          (activeTab[recipe.id] || 'recipe') === 'recipe' && styles.activeTabText
                        ]}>Recipe</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[
                          styles.tab,
                          activeTab[recipe.id] === 'comments' && styles.activeTab
                        ]}
                        onPress={() => setRecipeTab(recipe.id, 'comments')}
                      >
                        <Icon name="comment" size={16} color={
                          activeTab[recipe.id] === 'comments' ? Colors.primary : Colors.mutedForeground
                        } />
                        <Text style={[
                          styles.tabText,
                          activeTab[recipe.id] === 'comments' && styles.activeTabText
                        ]}>Comments</Text>
                      </TouchableOpacity>
                    </View>

                    {/* Recipe Tab Content */}
                    {(activeTab[recipe.id] || 'recipe') === 'recipe' && (
                      <View style={styles.tabContent}>
                        {/* Ingredients Section */}
                        <TouchableOpacity
                          style={styles.sectionHeader}
                          onPress={() => toggleRecipeSection(recipe.id, 'ingredients')}
                        >
                          <View style={styles.sectionHeaderLeft}>
                            <Icon name="restaurant-menu" size={16} color={Colors.primary} />
                            <Text style={styles.sectionTitle}>
                              {recipe.ingredients.length} Ingredients
                            </Text>
                          </View>
                          <Icon
                            name={expandedRecipes[recipe.id]?.ingredients ? "expand-less" : "expand-more"}
                            size={20}
                            color={Colors.mutedForeground}
                          />
                        </TouchableOpacity>

                        {expandedRecipes[recipe.id]?.ingredients && (
                          <View style={styles.sectionContent}>
                            {recipe.ingredients.map((ingredient, idx) => (
                              <View key={idx} style={styles.ingredientItem}>
                                <View style={styles.bullet} />
                                <Text style={styles.ingredientText}>
                                  {ingredient.amount} {ingredient.unit} {ingredient.name}
                                </Text>
                              </View>
                            ))}
                          </View>
                        )}

                        {/* Instructions Section */}
                        <TouchableOpacity
                          style={styles.sectionHeader}
                          onPress={() => toggleRecipeSection(recipe.id, 'instructions')}
                        >
                          <View style={styles.sectionHeaderLeft}>
                            <Icon name="list" size={16} color={Colors.primary} />
                            <Text style={styles.sectionTitle}>
                              {recipe.instructions.length} Steps
                            </Text>
                          </View>
                          <Icon
                            name={expandedRecipes[recipe.id]?.instructions ? "expand-less" : "expand-more"}
                            size={20}
                            color={Colors.mutedForeground}
                          />
                        </TouchableOpacity>

                        {expandedRecipes[recipe.id]?.instructions && (
                          <View style={styles.sectionContent}>
                            {recipe.instructions.map((instruction, idx) => (
                              <View key={idx} style={styles.instructionItem}>
                                <View style={styles.stepNumber}>
                                  <Text style={styles.stepNumberText}>{idx + 1}</Text>
                                </View>
                                <Text style={styles.instructionText}>{instruction}</Text>
                              </View>
                            ))}
                          </View>
                        )}
                      </View>
                    )}

                    {/* Comments Tab Content */}
                    {activeTab[recipe.id] === 'comments' && (
                      <View style={styles.tabContent}>
                        <Text style={styles.commentsPlaceholder}>
                          Comments functionality would be implemented here
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyState}>
              <Icon name="restaurant" size={48} color={Colors.mutedForeground} style={styles.emptyIcon} />
              <Text style={styles.emptyText}>
                {searchQuery ? 'No recipes found matching your search.' : 'No recipes added yet.'}
              </Text>
            </View>
          )}
        </CardContent>
      </Card>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  errorCard: {
    margin: Spacing.lg,
  },
  errorText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    padding: Spacing.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
    marginRight: Spacing.md,
  },
  headerActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  searchContainer: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  searchInputContainer: {
    position: 'relative',
  },
  searchIcon: {
    position: 'absolute',
    left: Spacing.md,
    top: 12,
    zIndex: 1,
  },
  searchInput: {
    paddingLeft: 40,
  },
  card: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  organizerText: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  projectDescription: {
    fontSize: 16,
    color: Colors.mutedForeground,
    marginBottom: Spacing.lg,
    lineHeight: 24,
  },
  projectMeta: {
    flexDirection: 'row',
    gap: Spacing.lg,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  metaLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  statusBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  statusActive: {
    backgroundColor: '#dcfce7',
  },
  statusPending: {
    backgroundColor: '#fef3c7',
  },
  statusInactive: {
    backgroundColor: '#f3f4f6',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusActiveText: {
    color: '#166534',
  },
  statusPendingText: {
    color: '#92400e',
  },
  statusInactiveText: {
    color: '#374151',
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  roleAdmin: {
    backgroundColor: '#f3e8ff',
  },
  roleOrganizer: {
    backgroundColor: '#dbeafe',
  },
  roleContributor: {
    backgroundColor: '#f3f4f6',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  roleAdminText: {
    color: '#7c3aed',
  },
  roleOrganizerText: {
    color: '#2563eb',
  },
  roleContributorText: {
    color: '#6b7280',
  },
  contributorsList: {
    gap: Spacing.md,
  },
  contributorItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contributorInfo: {
    flex: 1,
  },
  contributorName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  contributorEmail: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  recipesCard: {
    margin: Spacing.lg,
    marginTop: 0,
    overflow: 'hidden',
  },
  recipesHeader: {
    backgroundColor: Colors.primary + '0D', // 5% opacity
  },
  recipesHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  recipesTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
  },
  recipesList: {
    gap: Spacing.xl,
  },
  recipeCard: {
    padding: Spacing.lg,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.card,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  recipeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },
  recipeHeaderLeft: {
    flex: 1,
    marginRight: Spacing.md,
  },
  recipeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  recipeDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.sm,
    lineHeight: 20,
  },
  recipeBadgeContainer: {
    marginTop: Spacing.sm,
  },
  adminBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    backgroundColor: '#f3e8ff',
    borderRadius: BorderRadius.md,
    alignSelf: 'flex-start',
  },
  adminBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#7c3aed',
  },
  organizerBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    backgroundColor: '#dbeafe',
    borderRadius: BorderRadius.md,
    alignSelf: 'flex-start',
  },
  organizerBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#2563eb',
  },
  contributorBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    backgroundColor: '#f3f4f6',
    borderRadius: BorderRadius.md,
    alignSelf: 'flex-start',
  },
  contributorBadgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
  },
  recipeActions: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  actionButton: {
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
    backgroundColor: Colors.muted,
  },
  deleteButton: {
    backgroundColor: Colors.destructive + '20',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
    marginBottom: Spacing.md,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    backgroundColor: Colors.muted + '80',
    borderRadius: BorderRadius.full,
  },
  tagText: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  recipeFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginBottom: Spacing.md,
  },
  tabsContainer: {
    marginTop: Spacing.md,
  },
  tabsHeader: {
    flexDirection: 'row',
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.md,
    padding: Spacing.xs,
    marginBottom: Spacing.md,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: Spacing.xs,
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: BorderRadius.sm,
  },
  activeTab: {
    backgroundColor: Colors.card,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  activeTabText: {
    color: Colors.foreground,
  },
  tabContent: {
    gap: Spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.md,
    backgroundColor: Colors.muted + '50',
    borderRadius: BorderRadius.lg,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  sectionContent: {
    padding: Spacing.md,
    backgroundColor: Colors.muted + '30',
    borderRadius: BorderRadius.lg,
    gap: Spacing.sm,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  bullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.primary + '80',
  },
  ingredientText: {
    fontSize: 14,
    color: Colors.foreground,
    flex: 1,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: Spacing.sm,
  },
  stepNumber: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  instructionText: {
    fontSize: 14,
    color: Colors.foreground,
    flex: 1,
    lineHeight: 20,
  },
  commentsPlaceholder: {
    fontSize: 14,
    color: Colors.mutedForeground,
    textAlign: 'center',
    padding: Spacing.lg,
  },
  emptyState: {
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyIcon: {
    opacity: 0.5,
    marginBottom: Spacing.md,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
});