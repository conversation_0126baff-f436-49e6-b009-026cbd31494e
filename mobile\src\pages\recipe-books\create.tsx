import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../../lib/router';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '../../hooks/use-toast';
import { useAuth } from '../../hooks/use-auth';
import { Colors, Spacing, BorderRadius, API_URL, PricingTier, PricingModel } from '../../lib/constants';
import { Picker } from '@react-native-picker/picker';

export default function CreateRecipeBook() {
  const [, setLocation] = useLocation();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [pricingTier, setPricingTier] = useState(PricingTier.SMALL);
  const [maxContributors, setMaxContributors] = useState(PricingModel[PricingTier.SMALL].maxContributors);
  const [pricingInfo, setPricingInfo] = useState({
    tier: PricingTier.SMALL,
    basePrice: PricingModel[PricingTier.SMALL].basePrice,
    pagePrice: 0,
    totalPrice: PricingModel[PricingTier.SMALL].basePrice,
    estimatedPages: 20
  });
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Redirect contributors away from this page
  useEffect(() => {
    if (user?.role === 'contributor') {
      setLocation("/recipe-books");
      toast({
        variant: "destructive",
        title: "Access Denied",
        description: "Only organizers and admins can create recipe books.",
      });
    }
  }, [user, setLocation, toast]);

  // If user is a contributor, don't render the form
  if (user?.role === 'contributor') {
    return null;
  }

  const { mutate: createRecipeBook, isPending } = useMutation({
    mutationFn: async (data: {
      title: string;
      description: string;
      pricingTier: string;
      maxContributors: number;
    }) => {
      console.log('Starting recipe book creation with data:', data);
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Only allow organizers and admins to create recipe books
      if (user?.role !== 'organizer' && user?.role !== 'admin') {
        throw new Error('Only organizers and admins can create recipe books');
      }

      const endpoint = `${API_URL}/organizer/projects`;

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: data.title,
          description: data.description,
          pricingTier: data.pricingTier,
          maxContributors: data.maxContributors,
        }),
      });

      console.log('Recipe book creation response status:', response.status);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create recipe book");
      }

      const result = await response.json();
      console.log('Recipe book creation response:', result);
      return result;
    },
    onSuccess: () => {
      console.log('Recipe book created successfully, redirecting...');
      // Invalidate the recipe books query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["recipeBooks"] });
      toast({
        title: "Success",
        description: "Recipe book created successfully",
      });
      setLocation("/recipe-books");
    },
    onError: (error: Error) => {
      console.error('Error creating recipe book:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });

  const handleSubmit = () => {
    if (!title.trim() || !description.trim()) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    createRecipeBook({
      title,
      description,
      pricingTier,
      maxContributors
    });
  };

  const handlePricingTierChange = (value: string) => {
    setPricingTier(value);
    const tierData = PricingModel[value as keyof typeof PricingModel];
    setMaxContributors(tierData.maxContributors);

    // Update pricing info when tier changes
    setPricingInfo(prevInfo => ({
      ...prevInfo,
      tier: value,
      basePrice: tierData.basePrice,
      totalPrice: tierData.basePrice + prevInfo.pagePrice
    }));
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Create New Recipe Book</Text>
      </View>

      <View style={styles.form}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Title *</Text>
          <Input
            value={title}
            onChangeText={setTitle}
            placeholder="Enter recipe book title"
            style={styles.input}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Description *</Text>
          <TextInput
            style={styles.textArea}
            value={description}
            onChangeText={setDescription}
            placeholder="Enter recipe book description"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Cookbook Size</Text>
          <View style={styles.pickerContainer}>
            <Picker
              selectedValue={pricingTier}
              onValueChange={handlePricingTierChange}
              style={styles.picker}
            >
              <Picker.Item
                label={`${PricingModel[PricingTier.SMALL].name} - ${PricingModel[PricingTier.SMALL].description}`}
                value={PricingTier.SMALL}
              />
              <Picker.Item
                label={`${PricingModel[PricingTier.MEDIUM].name} - ${PricingModel[PricingTier.MEDIUM].description}`}
                value={PricingTier.MEDIUM}
              />
              <Picker.Item
                label={`${PricingModel[PricingTier.LARGE].name} - ${PricingModel[PricingTier.LARGE].description}`}
                value={PricingTier.LARGE}
              />
            </Picker>
          </View>
        </View>

        {/* Pricing Information */}
        <View style={styles.pricingCard}>
          <Text style={styles.pricingTitle}>Pricing Information</Text>
          <View style={styles.pricingDetails}>
            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Selected Tier:</Text>
              <Text style={styles.pricingValue}>{PricingModel[pricingTier as keyof typeof PricingModel].name}</Text>
            </View>
            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Base Price:</Text>
              <Text style={styles.pricingValue}>${pricingInfo.basePrice}</Text>
            </View>
            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Max Contributors:</Text>
              <Text style={styles.pricingValue}>{maxContributors}</Text>
            </View>
            <View style={styles.pricingRow}>
              <Text style={styles.pricingLabel}>Estimated Pages:</Text>
              <Text style={styles.pricingValue}>{pricingInfo.estimatedPages}</Text>
            </View>
            <View style={[styles.pricingRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total Price:</Text>
              <Text style={styles.totalValue}>${pricingInfo.totalPrice}</Text>
            </View>
          </View>
        </View>

        <View style={styles.buttonGroup}>
          <Button
            onPress={handleSubmit}
            disabled={isPending}
            style={styles.submitButton}
          >
            {isPending ? "Creating..." : "Create Recipe Book"}
          </Button>
          <Button
            variant="outline"
            onPress={() => setLocation("/recipe-books")}
            style={styles.cancelButton}
          >
            Cancel
          </Button>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  form: {
    padding: Spacing.lg,
    maxWidth: 600,
    alignSelf: 'center',
    width: '100%',
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  input: {
    marginBottom: 0,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.foreground,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.card,
  },
  picker: {
    color: Colors.foreground,
  },
  pricingCard: {
    backgroundColor: Colors.card,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.lg,
  },
  pricingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
  },
  pricingDetails: {
    gap: Spacing.sm,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pricingLabel: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  pricingValue: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: Spacing.sm,
    marginTop: Spacing.sm,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  buttonGroup: {
    gap: Spacing.md,
    marginTop: Spacing.lg,
  },
  submitButton: {
    marginBottom: Spacing.sm,
  },
  cancelButton: {
    marginBottom: 0,
  },
});