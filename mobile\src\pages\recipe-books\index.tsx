import React, { useEffect, useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { useQuery } from "@tanstack/react-query";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button } from "../../components/ui/button";
import { useLocation } from "../../lib/router";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card";
import { useAuth } from "../../hooks/use-auth";
import { useToast } from "../../hooks/use-toast";
import { API_URL } from '../../lib/constants';
import { Input } from "../../components/ui/input";
import { Colors, Spacing } from '../../lib/constants';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Contributor {
  id: number;
  name: string;
  email: string;
  status: string;
}

interface RecipeBook {
  id: number;
  name: string;
  description: string;
  status: string;
  role: string;
  createdAt: string;
  contributors: Contributor[];
  organizer?: { name: string };
  organizerId: number;
}

interface RecipeBooksResponse {
  projects: RecipeBook[];
}

export default function RecipeBooks() {
  const [, setLocation] = useLocation();
  const { user } = useAuth();
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'all' | 'my'>('all');
  const [searchQuery, setSearchQuery] = useState('');

  const { data: recipeBooks, isLoading, error } = useQuery<RecipeBooksResponse, Error>({
    queryKey: ["recipeBooks", user?.role, viewMode],
    queryFn: async () => {
      const token = await AsyncStorage.getItem('token');
      if (!token) {
        throw new Error('Not authenticated');
      }

      // Use different endpoints based on user role and view mode
      let endpoint = '';
      if (user?.role === 'organizer') {
        endpoint = viewMode === 'all'
          ? `${API_URL}/organizer/all-projects`
          : `${API_URL}/organizer/my-projects`;
      } else if (user?.role === 'contributor') {
        endpoint = `${API_URL}/contributor/all-projects`;
      } else if (user?.role === 'admin') {
        endpoint = viewMode === 'all'
          ? `${API_URL}/admin/projects`
          : `${API_URL}/organizer/my-projects`;
      } else {
        throw new Error('User role not supported');
      }

      const response = await fetch(endpoint, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to fetch recipe books");
      }

      const data = await response.json();
      return { projects: data.projects || [] };
    },
    enabled: !!user?.role
  });

  useEffect(() => {
    if (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    }
  }, [error, toast]);

  // Log when recipe books data changes
  useEffect(() => {
    if (recipeBooks?.projects) {
      console.log('Current recipe books in state:', recipeBooks.projects.length);
    }
  }, [recipeBooks]);

  // Add filtering logic
  const filteredRecipeBooks = recipeBooks?.projects.filter((book) => {
    if (!searchQuery) return true;

    const searchLower = searchQuery.toLowerCase();
    return (
      book.name.toLowerCase().includes(searchLower) ||
      book.description.toLowerCase().includes(searchLower) ||
      (book.contributors?.some(contributor =>
        contributor?.name?.toLowerCase().includes(searchLower)
      ) ?? false)
    );
  });

  if (!user?.role) {
    return (
      <View style={styles.container}>
        <View style={styles.centerContent}>
          <Text style={styles.loadingText}>Loading user information...</Text>
        </View>
      </View>
    );
  }

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Recipe Books</Text>
          <View style={styles.headerActions}>
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <View style={styles.viewModeButtons}>
                <Button
                  variant={viewMode === 'all' ? 'default' : 'outline'}
                  onPress={() => setViewMode('all')}
                  size="sm"
                >
                  View All Recipe Books
                </Button>
                <Button
                  variant={viewMode === 'my' ? 'default' : 'outline'}
                  onPress={() => setViewMode('my')}
                  size="sm"
                >
                  My Recipe Books
                </Button>
              </View>
            )}
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <Button onPress={() => setLocation("/recipe-books/create")}>
                Create New Recipe Book
              </Button>
            )}
          </View>
        </View>
        <View style={styles.centerContent}>
          <Text style={styles.loadingText}>Loading recipe books...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Recipe Books</Text>
          <View style={styles.headerActions}>
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <View style={styles.viewModeButtons}>
                <Button
                  variant={viewMode === 'all' ? 'default' : 'outline'}
                  onPress={() => setViewMode('all')}
                  size="sm"
                >
                  View All Recipe Books
                </Button>
                <Button
                  variant={viewMode === 'my' ? 'default' : 'outline'}
                  onPress={() => setViewMode('my')}
                  size="sm"
                >
                  My Recipe Books
                </Button>
              </View>
            )}
            {(user?.role === 'organizer' || user?.role === 'admin') && (
              <Button onPress={() => setLocation("/recipe-books/create")}>
                Create New Recipe Book
              </Button>
            )}
          </View>
        </View>
        <View style={styles.centerContent}>
          <Text style={styles.errorText}>
            Failed to load recipe books. Please try again later.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Recipe Books</Text>
        <View style={styles.headerActions}>
          {(user?.role === 'organizer' || user?.role === 'admin') && (
            <View style={styles.viewModeButtons}>
              <Button
                variant={viewMode === 'all' ? 'default' : 'outline'}
                onPress={() => setViewMode('all')}
                size="sm"
              >
                View All Recipe Books
              </Button>
              <Button
                variant={viewMode === 'my' ? 'default' : 'outline'}
                onPress={() => setViewMode('my')}
                size="sm"
              >
                My Recipe Books
              </Button>
            </View>
          )}
          {(user?.role === 'organizer' || user?.role === 'admin') && (
            <Button onPress={() => setLocation("/recipe-books/create")}>
              Create New Recipe Book
            </Button>
          )}
        </View>
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color={Colors.mutedForeground} style={styles.searchIcon} />
          <Input
            placeholder="Search books by name"
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.searchInput}
          />
        </View>
      </View>

      {recipeBooks?.projects && filteredRecipeBooks && filteredRecipeBooks.length > 0 ? (
        <View style={styles.content}>
          <View style={styles.booksContainer}>
            {filteredRecipeBooks.map((book: RecipeBook) => {
              console.log('Rendering book:', book.id, book.name);
              return (
                <TouchableOpacity
                  key={book.id}
                  onPress={() => setLocation(`/recipe-books/${book.id}`)}
                  style={styles.bookCard}
                >
                  <Card>
                    <CardHeader>
                      <CardTitle>{book.name}</CardTitle>
                      <CardDescription>
                        Created on {new Date(book.createdAt).toLocaleDateString()}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <Text style={styles.bookDescription}>{book.description}</Text>
                      <View style={styles.bookFooter}>
                        <Text style={styles.contributorsText}>
                          Contributors: {book.contributors?.length || 0}
                        </Text>
                        <Button
                          variant="outline"
                          size="sm"
                          onPress={() => setLocation(`/recipe-books/${book.id}`)}
                        >
                          View Details
                        </Button>
                      </View>
                    </CardContent>
                  </Card>
                </TouchableOpacity>
              );
            })}
          </View>
          <View style={styles.summaryContainer}>
            <Text style={styles.summaryText}>
              Showing {filteredRecipeBooks.length} of {recipeBooks.projects.length} recipe books
            </Text>
          </View>
        </View>
      ) : (
        <View style={styles.centerContent}>
          <Text style={styles.emptyText}>
            {searchQuery ? 'No recipe books found matching your search.' : 'No recipe books found. Create your first recipe book!'}
          </Text>
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  viewModeButtons: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  searchContainer: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  searchInputContainer: {
    position: 'relative',
  },
  searchIcon: {
    position: 'absolute',
    left: Spacing.md,
    top: 12,
    zIndex: 1,
  },
  searchInput: {
    paddingLeft: 40,
  },
  content: {
    padding: Spacing.lg,
  },
  booksContainer: {
    gap: Spacing.md,
  },
  bookCard: {
    marginBottom: Spacing.md,
  },
  bookDescription: {
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  bookFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contributorsText: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  summaryContainer: {
    alignItems: 'center',
    marginTop: Spacing.lg,
  },
  summaryText: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.foreground,
  },
  errorText: {
    fontSize: 16,
    color: Colors.destructive,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
});
