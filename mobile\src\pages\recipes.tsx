import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Input } from "../components/ui/input";
import { Button } from "../components/ui/button";
import { RecipeCard, AddRecipeCard } from "../components/recipes/recipe-card";

const recipes = [
  {
    title: "Mediterranean Salad",
    description: "A refreshing salad with cucumbers, tomatoes, olives, and feta cheese.",
    tags: [
      { label: "Vegetarian", variant: "primary" as const },
      { label: "30 min", variant: "secondary" as const },
      { label: "Salad", variant: "outline" as const },
    ],
    contributor: "Aunt Maria",
    imageIndex: 0,
    isSaved: false,
  },
  {
    title: "Homemade Fettuccine",
    description: "Classic pasta made from scratch with just flour and eggs, the way <PERSON> used to make it.",
    tags: [
      { label: "Vegetarian", variant: "primary" as const },
      { label: "60 min", variant: "secondary" as const },
      { label: "Pasta", variant: "outline" as const },
    ],
    contributor: "Grandma Rose",
    imageIndex: 1,
    isSaved: false,
  },
  {
    title: "Classic Apple Pie",
    description: "A traditional apple pie with a flaky crust and cinnamon-spiced filling.",
    tags: [
      { label: "Vegetarian", variant: "primary" as const },
      { label: "90 min", variant: "secondary" as const },
      { label: "Dessert", variant: "outline" as const },
    ],
    contributor: "Mom",
    imageIndex: 2,
    isSaved: true,
  },
];

const categories = ["All", "Main Dishes", "Desserts", "Vegetarian"];

export default function Recipes() {
  const [selectedCategory, setSelectedCategory] = useState(0);
  const [searchText, setSearchText] = useState('');

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Recipes</Text>
        <Text style={styles.subtitle}>Browse and manage your recipe collection</Text>
        
        <View style={styles.searchSection}>
          <View style={styles.searchContainer}>
            <Input
              placeholder="Search recipes..."
              value={searchText}
              onChangeText={setSearchText}
              style={styles.searchInput}
            />
          </View>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesContainer}>
            <View style={styles.categories}>
              {categories.map((category, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.categoryButton,
                    selectedCategory === index && styles.selectedCategoryButton
                  ]}
                  onPress={() => setSelectedCategory(index)}
                >
                  <Text style={[
                    styles.categoryButtonText,
                    selectedCategory === index && styles.selectedCategoryButtonText
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
              <TouchableOpacity style={styles.filterButton}>
                <Text style={styles.filterButtonText}>⚙ More</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
        
        <View style={styles.recipesGrid}>
          {recipes.map((recipe, index) => (
            <RecipeCard
              key={index}
              title={recipe.title}
              description={recipe.description}
              tags={recipe.tags}
              contributor={recipe.contributor}
              imageIndex={recipe.imageIndex}
              isSaved={recipe.isSaved}
            />
          ))}
          
          <AddRecipeCard />
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'serif',
  },
  subtitle: {
    fontSize: 18,
    color: '#6b7280',
    marginBottom: 32,
  },
  searchSection: {
    marginBottom: 32,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchInput: {
    paddingLeft: 40,
  },
  categoriesContainer: {
    marginBottom: 16,
  },
  categories: {
    flexDirection: 'row',
    gap: 8,
    paddingRight: 16,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#d1d5db',
    backgroundColor: 'white',
  },
  selectedCategoryButton: {
    backgroundColor: '#dbeafe',
    borderColor: '#3b82f6',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  selectedCategoryButtonText: {
    color: '#3b82f6',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#d1d5db',
    backgroundColor: 'white',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  recipesGrid: {
    gap: 24,
  },
});
