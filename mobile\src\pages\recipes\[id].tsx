import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Image, Alert } from 'react-native';
import { Card } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { useToast } from '../../hooks/use-toast';
import { useAuth } from '../../hooks/use-auth';
import { API_URL } from '../../lib/constants';

interface Recipe {
  id: number;
  title: string;
  description: string;
  ingredients: string[];
  instructions: string[];
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  difficulty?: string;
  tags: string[];
  images: string[];
  contributor: {
    id: number;
    name: string;
    email: string;
  };
  project: {
    id: number;
    name: string;
  };
  status: string;
  createdAt: string;
}

interface RecipeDetailProps {
  recipeId: string;
}

export default function RecipeDetail({ recipeId }: RecipeDetailProps) {
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaved, setIsSaved] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    loadRecipe();
  }, [recipeId]);

  const loadRecipe = async () => {
    try {
      const response = await fetch(`${API_URL}/recipes/${recipeId}`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRecipe(data.recipe);
      } else {
        throw new Error('Recipe not found');
      }
    } catch (error) {
      console.error('Error loading recipe:', error);
      toast({
        title: 'Error',
        description: 'Failed to load recipe',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleSaved = async () => {
    try {
      const response = await fetch(`${API_URL}/recipes/${recipeId}/save`, {
        method: isSaved ? 'DELETE' : 'POST',
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        setIsSaved(!isSaved);
        toast({
          title: 'Success',
          description: isSaved ? 'Recipe removed from saved' : 'Recipe saved',
        });
      }
    } catch (error) {
      console.error('Error toggling saved recipe:', error);
      toast({
        title: 'Error',
        description: 'Failed to update saved status',
        variant: 'destructive',
      });
    }
  };

  const deleteRecipe = async () => {
    Alert.alert(
      'Delete Recipe',
      'Are you sure you want to delete this recipe?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await fetch(`${API_URL}/recipes/${recipeId}`, {
                method: 'DELETE',
                headers: {
                  Authorization: `Bearer ${localStorage.getItem('token')}`,
                },
              });

              if (response.ok) {
                toast({
                  title: 'Success',
                  description: 'Recipe deleted successfully',
                });
                // Navigate back
              } else {
                throw new Error('Failed to delete recipe');
              }
            } catch (error) {
              console.error('Error deleting recipe:', error);
              toast({
                title: 'Error',
                description: 'Failed to delete recipe',
                variant: 'destructive',
              });
            }
          }
        }
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading recipe...</Text>
      </View>
    );
  }

  if (!recipe) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Recipe not found</Text>
      </View>
    );
  }

  const canEdit = user?.id === recipe.contributor.id || user?.role === 'organizer' || user?.role === 'admin';
  const canDelete = user?.id === recipe.contributor.id || user?.role === 'organizer' || user?.role === 'admin';

  return (
    <ScrollView style={styles.container}>
      {/* Header Image */}
      {recipe.images && recipe.images.length > 0 && (
        <Image source={{ uri: recipe.images[0] }} style={styles.headerImage} />
      )}

      <View style={styles.content}>
        {/* Title and Actions */}
        <View style={styles.header}>
          <View style={styles.titleSection}>
            <Text style={styles.title}>{recipe.title}</Text>
            <Text style={styles.contributor}>by {recipe.contributor.name}</Text>
          </View>
          
          <View style={styles.actions}>
            <Button
              title={isSaved ? '♥' : '♡'}
              onPress={toggleSaved}
              variant="outline"
              size="sm"
              style={styles.saveButton}
            />
            {canEdit && (
              <Button
                title="Edit"
                variant="outline"
                size="sm"
              />
            )}
            {canDelete && (
              <Button
                title="Delete"
                onPress={deleteRecipe}
                variant="destructive"
                size="sm"
              />
            )}
          </View>
        </View>

        {/* Description */}
        {recipe.description && (
          <Text style={styles.description}>{recipe.description}</Text>
        )}

        {/* Recipe Info */}
        <View style={styles.infoRow}>
          {recipe.prepTime && (
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Prep Time</Text>
              <Text style={styles.infoValue}>{recipe.prepTime} min</Text>
            </View>
          )}
          {recipe.cookTime && (
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Cook Time</Text>
              <Text style={styles.infoValue}>{recipe.cookTime} min</Text>
            </View>
          )}
          {recipe.servings && (
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Servings</Text>
              <Text style={styles.infoValue}>{recipe.servings}</Text>
            </View>
          )}
          {recipe.difficulty && (
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Difficulty</Text>
              <Text style={styles.infoValue}>{recipe.difficulty}</Text>
            </View>
          )}
        </View>

        {/* Tags */}
        {recipe.tags && recipe.tags.length > 0 && (
          <View style={styles.tagsSection}>
            <View style={styles.tags}>
              {recipe.tags.map((tag, index) => (
                <Badge key={index} variant="outline">
                  {tag}
                </Badge>
              ))}
            </View>
          </View>
        )}

        {/* Ingredients */}
        <Card style={styles.section}>
          <View style={styles.sectionContent}>
            <Text style={styles.sectionTitle}>Ingredients</Text>
            <View style={styles.ingredientsList}>
              {recipe.ingredients.map((ingredient, index) => (
                <View key={index} style={styles.ingredientItem}>
                  <Text style={styles.ingredientBullet}>•</Text>
                  <Text style={styles.ingredientText}>{ingredient}</Text>
                </View>
              ))}
            </View>
          </View>
        </Card>

        {/* Instructions */}
        <Card style={styles.section}>
          <View style={styles.sectionContent}>
            <Text style={styles.sectionTitle}>Instructions</Text>
            <View style={styles.instructionsList}>
              {recipe.instructions.map((instruction, index) => (
                <View key={index} style={styles.instructionItem}>
                  <View style={styles.stepNumber}>
                    <Text style={styles.stepNumberText}>{index + 1}</Text>
                  </View>
                  <Text style={styles.instructionText}>{instruction}</Text>
                </View>
              ))}
            </View>
          </View>
        </Card>

        {/* Recipe Meta */}
        <Card style={styles.section}>
          <View style={styles.sectionContent}>
            <Text style={styles.sectionTitle}>Recipe Details</Text>
            <View style={styles.metaList}>
              <View style={styles.metaItem}>
                <Text style={styles.metaLabel}>Recipe Book:</Text>
                <Text style={styles.metaValue}>{recipe.project.name}</Text>
              </View>
              <View style={styles.metaItem}>
                <Text style={styles.metaLabel}>Status:</Text>
                <Badge variant={recipe.status === 'approved' ? 'default' : 'secondary'}>
                  {recipe.status}
                </Badge>
              </View>
              <View style={styles.metaItem}>
                <Text style={styles.metaLabel}>Created:</Text>
                <Text style={styles.metaValue}>
                  {new Date(recipe.createdAt).toLocaleDateString()}
                </Text>
              </View>
            </View>
          </View>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#ef4444',
  },
  headerImage: {
    width: '100%',
    height: 250,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  titleSection: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
    color: '#111827',
  },
  contributor: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  saveButton: {
    minWidth: 40,
  },
  description: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginBottom: 24,
  },
  infoRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 24,
  },
  infoItem: {
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  tagsSection: {
    marginBottom: 24,
  },
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#111827',
  },
  ingredientsList: {
    gap: 8,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  ingredientBullet: {
    fontSize: 16,
    color: '#6b7280',
    marginRight: 8,
    marginTop: 2,
  },
  ingredientText: {
    fontSize: 14,
    color: '#374151',
    flex: 1,
    lineHeight: 20,
  },
  instructionsList: {
    gap: 16,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#3b82f6',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    marginTop: 2,
  },
  stepNumberText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  instructionText: {
    fontSize: 14,
    color: '#374151',
    flex: 1,
    lineHeight: 20,
  },
  metaList: {
    gap: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  metaLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  metaValue: {
    fontSize: 14,
    color: '#111827',
    fontWeight: '500',
  },
});
