import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocation } from '../../../lib/router';
import { useToast } from '../../../hooks/use-toast';
import { useAuth } from '../../../hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Colors, Spacing, BorderRadius, API_URL } from '../../../lib/constants';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Recipe {
  id: number;
  title: string;
  description: string;
  tags: string[];
  ingredients: { name: string; amount: number; unit: string }[];
  instructions: string[];
  contributor: {
    id: number;
    name: string;
  };
  project: {
    id: number;
    organizerId: number;
  };
  images: string[];
}

export default function EditRecipe() {
  const [location, setLocation] = useLocation();
  const [recipe, setRecipe] = useState<Recipe | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // Extract recipe ID from location
  const recipeId = location.split('/')[2]; // /recipes/[id]/edit

  useEffect(() => {
    if (!user) {
      setLocation("/login");
      return;
    }

    const fetchRecipe = async () => {
      try {
        const token = await AsyncStorage.getItem('token');
        const response = await fetch(`${API_URL}/organizer/recipes/${recipeId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to fetch recipe");
          } else {
            const text = await response.text();
            console.error('Non-JSON error response:', text);
            throw new Error(`Server error: ${response.status} ${response.statusText}`);
          }
        }

        const data = await response.json();
        setRecipe(data.recipe);
      } catch (error) {
        console.error("Error fetching recipe:", error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to load recipe. Please try again.",
          variant: "destructive",
        });
        setLocation("/recipe-books");
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecipe();
  }, [toast, setLocation, user, recipeId]);

  const handleSubmit = async () => {
    if (!recipe) return;

    setIsSaving(true);
    try {
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${API_URL}/organizer/recipes/${recipe.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...recipe,
          // Process images to ensure consistent format - server expects images WITHOUT recipes/ prefix
          images: recipe.images.map((url: string) => {
            // If it's already just a key (not a full URL), return as is
            if (!url.includes('http')) {
              // Remove recipes/ prefix if it exists (server will store without prefix)
              return url.replace('recipes/', '');
            }

            // Extract the path after the bucket name
            const bucketNameIndex = url.lastIndexOf('recipe-book-images-bucket');
            if (bucketNameIndex === -1) return url;

            const pathStart = url.indexOf('/', bucketNameIndex);
            // Remove recipes/ prefix from the extracted path
            const path = pathStart !== -1 ? url.substring(pathStart + 1) : url;
            return path.replace('recipes/', '');
          }),
        }),
      });

      if (!response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to update recipe");
        } else {
          const text = await response.text();
          console.error('Non-JSON error response:', text);
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }
      }

      const result = await response.json();
      console.log('Recipe update response:', result);

      toast({
        title: "Success",
        description: "Recipe updated successfully",
      });
      setLocation(`/recipe-books/${recipe.project.id}`);
    } catch (error) {
      console.error('Error updating recipe:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update recipe. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const removeIngredient = (index: number) => {
    if (!recipe) return;
    if (recipe.ingredients.length > 1) {
      const newIngredients = [...recipe.ingredients];
      newIngredients.splice(index, 1);
      setRecipe({ ...recipe, ingredients: newIngredients });
    } else {
      toast({
        title: "Cannot remove",
        description: "At least one ingredient is required",
        variant: "destructive",
      });
    }
  };

  const removeInstruction = (index: number) => {
    if (!recipe) return;
    if (recipe.instructions.length > 1) {
      const newInstructions = [...recipe.instructions];
      newInstructions.splice(index, 1);
      setRecipe({ ...recipe, instructions: newInstructions });
    } else {
      toast({
        title: "Cannot remove",
        description: "At least one instruction step is required",
        variant: "destructive",
      });
    }
  };

  const updateIngredient = (index: number, field: string, value: any) => {
    if (!recipe) return;
    const newIngredients = [...recipe.ingredients];
    newIngredients[index] = {
      ...newIngredients[index],
      [field]: value
    };
    setRecipe({ ...recipe, ingredients: newIngredients });
  };

  const updateInstruction = (index: number, value: string) => {
    if (!recipe) return;
    const newInstructions = [...recipe.instructions];
    newInstructions[index] = value;
    setRecipe({ ...recipe, instructions: newInstructions });
  };

  const addIngredient = () => {
    if (!recipe) return;
    if (recipe.ingredients.length < 15) {
      setRecipe({
        ...recipe,
        ingredients: [...recipe.ingredients, { amount: 0, unit: "", name: "" }]
      });
    }
  };

  const addInstruction = () => {
    if (!recipe) return;
    if (recipe.instructions.length < 15) {
      setRecipe({
        ...recipe,
        instructions: [...recipe.instructions, '']
      });
    }
  };

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading recipe...</Text>
      </View>
    );
  }

  if (!recipe) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <CardContent>
            <Text style={styles.errorText}>Recipe not found.</Text>
          </CardContent>
        </Card>
      </View>
    );
  }

  // Check if user is authorized to edit
  const canEdit = user.id === recipe.contributor.id || user.id === recipe.project.organizerId || user.role === 'admin';
  if (!canEdit) {
    return (
      <View style={styles.container}>
        <Card style={styles.errorCard}>
          <CardContent>
            <Text style={styles.errorText}>
              You are not authorized to edit this recipe.
            </Text>
          </CardContent>
        </Card>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Edit Recipe</Text>
        <Button
          variant="outline"
          onPress={() => setLocation(`/recipe-books/${recipe.project.id}`)}
          size="sm"
        >
          Back to Recipe Book
        </Button>
      </View>

      {/* Basic Information Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Basic Information</Text>
          <Text style={styles.cardDescription}>
            Update the basic details of your recipe
          </Text>
        </CardHeader>
        <CardContent>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Title</Text>
            <Input
              value={recipe.title}
              onChangeText={(text) => setRecipe({ ...recipe, title: text })}
              placeholder="Recipe title"
              style={styles.input}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={styles.textArea}
              value={recipe.description}
              onChangeText={(text) => setRecipe({ ...recipe, description: text })}
              placeholder="Recipe description"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Recipe Images</Text>
            <Text style={styles.imageNote}>
              Image upload functionality would be implemented here
            </Text>
            {recipe.images.length > 0 && (
              <View style={styles.imagesList}>
                {recipe.images.map((image, index) => (
                  <Text key={index} style={styles.imageItem}>
                    Image {index + 1}: {image.substring(0, 30)}...
                  </Text>
                ))}
              </View>
            )}
          </View>
        </CardContent>
      </Card>

      {/* Ingredients Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Ingredients</Text>
          <Text style={styles.cardDescription}>
            List all ingredients needed for the recipe
          </Text>
        </CardHeader>
        <CardContent>
          <View style={styles.ingredientsList}>
            {recipe.ingredients.map((ingredient, index) => (
              <View key={index} style={styles.ingredientRow}>
                <View style={styles.ingredientInputs}>
                  <View style={styles.amountInput}>
                    <Input
                      value={ingredient.amount.toString()}
                      onChangeText={(text) => updateIngredient(index, 'amount', parseFloat(text) || 0)}
                      placeholder="Amount"
                      keyboardType="numeric"
                    />
                  </View>
                  <View style={styles.unitInput}>
                    <Input
                      value={ingredient.unit}
                      onChangeText={(text) => updateIngredient(index, 'unit', text)}
                      placeholder="Unit"
                    />
                  </View>
                  <View style={styles.nameInput}>
                    <Input
                      value={ingredient.name}
                      onChangeText={(text) => updateIngredient(index, 'name', text)}
                      placeholder="Ingredient name"
                    />
                  </View>
                </View>
                <TouchableOpacity
                  onPress={() => removeIngredient(index)}
                  style={styles.removeButton}
                >
                  <Icon name="close" size={20} color={Colors.destructive} />
                </TouchableOpacity>
              </View>
            ))}
            <Button
              variant="outline"
              onPress={addIngredient}
              disabled={recipe.ingredients.length >= 15}
              style={styles.addButton}
            >
              <Icon name="add" size={16} color={Colors.foreground} />
              <Text style={styles.addButtonText}>Add Ingredient</Text>
            </Button>
          </View>
        </CardContent>
      </Card>

      {/* Instructions Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Instructions</Text>
          <Text style={styles.cardDescription}>
            Add step-by-step instructions for preparing the recipe
          </Text>
        </CardHeader>
        <CardContent>
          <View style={styles.instructionsList}>
            {recipe.instructions.map((instruction, index) => (
              <View key={index} style={styles.instructionRow}>
                <View style={styles.stepHeader}>
                  <Text style={styles.stepLabel}>Step {index + 1}</Text>
                  <TouchableOpacity
                    onPress={() => removeInstruction(index)}
                    style={styles.removeButton}
                  >
                    <Icon name="close" size={20} color={Colors.destructive} />
                  </TouchableOpacity>
                </View>
                <TextInput
                  style={styles.instructionTextArea}
                  value={instruction}
                  onChangeText={(text) => updateInstruction(index, text)}
                  placeholder={`Step ${index + 1} instructions`}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            ))}
            <Button
              variant="outline"
              onPress={addInstruction}
              disabled={recipe.instructions.length >= 15}
              style={styles.addButton}
            >
              <Icon name="add" size={16} color={Colors.foreground} />
              <Text style={styles.addButtonText}>Add Step</Text>
            </Button>
          </View>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          variant="outline"
          onPress={() => setLocation(`/recipe-books/${recipe.project.id}`)}
          style={styles.cancelButton}
        >
          Cancel
        </Button>
        <Button
          onPress={handleSubmit}
          disabled={isSaving}
          style={styles.saveButton}
        >
          {isSaving ? (
            <>
              <ActivityIndicator size="small" color={Colors.primaryForeground} />
              <Text style={styles.savingText}>Saving...</Text>
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
  },
  errorCard: {
    margin: Spacing.lg,
  },
  errorText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    padding: Spacing.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
    marginRight: Spacing.md,
  },
  card: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  inputGroup: {
    marginBottom: Spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: Spacing.sm,
  },
  input: {
    marginBottom: 0,
  },
  textArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.foreground,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  imageNote: {
    fontSize: 14,
    color: Colors.mutedForeground,
    fontStyle: 'italic',
    marginBottom: Spacing.sm,
  },
  imagesList: {
    gap: Spacing.xs,
  },
  imageItem: {
    fontSize: 12,
    color: Colors.mutedForeground,
    backgroundColor: Colors.muted,
    padding: Spacing.sm,
    borderRadius: BorderRadius.sm,
  },
  ingredientsList: {
    gap: Spacing.md,
  },
  ingredientRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  ingredientInputs: {
    flex: 1,
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  amountInput: {
    width: 80,
  },
  unitInput: {
    width: 80,
  },
  nameInput: {
    flex: 1,
  },
  removeButton: {
    padding: Spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
    marginTop: Spacing.sm,
  },
  addButtonText: {
    color: Colors.foreground,
    marginLeft: Spacing.xs,
  },
  instructionsList: {
    gap: Spacing.lg,
  },
  instructionRow: {
    gap: Spacing.sm,
  },
  stepHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
  },
  instructionTextArea: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: BorderRadius.md,
    padding: Spacing.md,
    fontSize: 16,
    backgroundColor: Colors.card,
    color: Colors.foreground,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
    padding: Spacing.lg,
    paddingTop: 0,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  savingText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
});