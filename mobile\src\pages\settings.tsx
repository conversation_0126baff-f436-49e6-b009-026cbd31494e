import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { useAuth } from '../hooks/use-auth';
import { useLocation } from '../lib/router';
import { Colors, Spacing, BorderRadius } from '../lib/constants';
import Icon from 'react-native-vector-icons/MaterialIcons';

export default function Settings() {
  const { user, logout } = useAuth();
  const [, setLocation] = useLocation();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();
            setLocation('/login');
          }
        }
      ]
    );
  };

  const navigateToPage = (path: string) => {
    setLocation(path);
  };

  const settingsOptions = [
    {
      title: 'Recipe Books',
      description: 'View and manage your recipe books',
      icon: 'book',
      onPress: () => navigateToPage('/recipe-books'),
    },
    {
      title: 'Dashboard',
      description: user?.role === 'organizer' ? 'Organizer dashboard' :
                   user?.role === 'admin' ? 'Admin dashboard' :
                   'Contributor dashboard',
      icon: 'dashboard',
      onPress: () => navigateToPage(
        user?.role === 'organizer' ? '/organizer' :
        user?.role === 'admin' ? '/admin' :
        '/contributor/dashboard'
      ),
    },
  ];

  // Add role-specific options
  if (user?.role === 'organizer') {
    settingsOptions.push({
      title: 'Recipe Approvals',
      description: 'Review and approve submitted recipes',
      icon: 'approval',
      onPress: () => navigateToPage('/organizer/recipe-approvals'),
    });
  }

  if (user?.role === 'admin') {
    settingsOptions.push({
      title: 'Admin Panel',
      description: 'Manage users and system settings',
      icon: 'admin-panel-settings',
      onPress: () => navigateToPage('/admin'),
    });
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Settings</Text>
      </View>

      {/* User Profile Card */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Profile</Text>
          <Text style={styles.cardDescription}>Your account information</Text>
        </CardHeader>
        <CardContent>
          <View style={styles.profileInfo}>
            <View style={styles.profileRow}>
              <Text style={styles.profileLabel}>Name:</Text>
              <Text style={styles.profileValue}>{user?.name || 'N/A'}</Text>
            </View>
            <View style={styles.profileRow}>
              <Text style={styles.profileLabel}>Email:</Text>
              <Text style={styles.profileValue}>{user?.email || 'N/A'}</Text>
            </View>
            <View style={styles.profileRow}>
              <Text style={styles.profileLabel}>Role:</Text>
              <View style={[
                styles.roleBadge,
                user?.role === 'admin' ? styles.roleAdmin :
                user?.role === 'organizer' ? styles.roleOrganizer :
                styles.roleContributor
              ]}>
                <Text style={[
                  styles.roleText,
                  user?.role === 'admin' ? styles.roleAdminText :
                  user?.role === 'organizer' ? styles.roleOrganizerText :
                  styles.roleContributorText
                ]}>{user?.role || 'N/A'}</Text>
              </View>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Navigation Options */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>Quick Access</Text>
          <Text style={styles.cardDescription}>Navigate to different sections</Text>
        </CardHeader>
        <CardContent>
          <View style={styles.optionsList}>
            {settingsOptions.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={styles.optionItem}
                onPress={option.onPress}
              >
                <View style={styles.optionLeft}>
                  <Icon name={option.icon} size={24} color={Colors.primary} />
                  <View style={styles.optionText}>
                    <Text style={styles.optionTitle}>{option.title}</Text>
                    <Text style={styles.optionDescription}>{option.description}</Text>
                  </View>
                </View>
                <Icon name="chevron-right" size={20} color={Colors.mutedForeground} />
              </TouchableOpacity>
            ))}
          </View>
        </CardContent>
      </Card>

      {/* App Information */}
      <Card style={styles.card}>
        <CardHeader>
          <Text style={styles.cardTitle}>App Information</Text>
          <Text style={styles.cardDescription}>About this application</Text>
        </CardHeader>
        <CardContent>
          <View style={styles.appInfo}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Version:</Text>
              <Text style={styles.infoValue}>1.0.0</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Platform:</Text>
              <Text style={styles.infoValue}>React Native</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Build:</Text>
              <Text style={styles.infoValue}>Mobile App</Text>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Logout Button */}
      <View style={styles.logoutContainer}>
        <Button
          onPress={handleLogout}
          variant="destructive"
          style={styles.logoutButton}
        >
          <Icon name="logout" size={16} color={Colors.primaryForeground} />
          <Text style={styles.logoutButtonText}>Logout</Text>
        </Button>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  card: {
    margin: Spacing.lg,
    marginBottom: Spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.cardForeground,
    marginBottom: Spacing.xs,
  },
  cardDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  profileInfo: {
    gap: Spacing.md,
  },
  profileRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  profileLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
  },
  profileValue: {
    fontSize: 16,
    color: Colors.mutedForeground,
  },
  roleBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.full,
  },
  roleAdmin: {
    backgroundColor: '#f3e8ff',
  },
  roleOrganizer: {
    backgroundColor: '#dbeafe',
  },
  roleContributor: {
    backgroundColor: '#f3f4f6',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  roleAdminText: {
    color: '#7c3aed',
  },
  roleOrganizerText: {
    color: '#2563eb',
  },
  roleContributorText: {
    color: '#6b7280',
  },
  optionsList: {
    gap: Spacing.sm,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.md,
    backgroundColor: Colors.muted + '30',
    borderRadius: BorderRadius.md,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    flex: 1,
  },
  optionText: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginBottom: Spacing.xs,
  },
  optionDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  appInfo: {
    gap: Spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  infoValue: {
    fontSize: 14,
    color: Colors.mutedForeground,
  },
  logoutContainer: {
    padding: Spacing.lg,
    paddingTop: 0,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.xs,
  },
  logoutButtonText: {
    color: Colors.primaryForeground,
    marginLeft: Spacing.xs,
  },
});
