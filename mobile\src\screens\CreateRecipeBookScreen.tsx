import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Spacing } from '../lib/constants';

export default function CreateRecipeBookScreen() {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Create Recipe Book Screen</Text>
      <Text style={styles.subtext}>This screen will allow creating new recipe books</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
    padding: Spacing.lg,
  },
  text: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  subtext: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
  },
});
