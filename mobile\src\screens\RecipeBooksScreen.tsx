import React, { useState } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  Alert,
  TextInput,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../hooks/useAuth';
import { apiService } from '../services/api';
import { queryKeys } from '../lib/queryClient';
import { RootStackParamList, RecipeBook } from '../types';
import { Colors, Spacing, BorderRadius, UserRole } from '../lib/constants';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LoadingScreen from './LoadingScreen';

type RecipeBooksScreenNavigationProp = StackNavigationProp<RootStackParamList, 'RecipeBooks'>;

export default function RecipeBooksScreen() {
  const navigation = useNavigation<RecipeBooksScreenNavigationProp>();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'all' | 'my'>('all');

  const {
    data: recipeBooksData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: queryKeys.recipeBooks,
    queryFn: async () => {
      const response = await apiService.getRecipeBooks();
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch recipe books');
      }
      return response.data;
    },
  });

  const recipeBooks = recipeBooksData?.projects || [];

  // Filter recipe books based on search and view mode
  const filteredRecipeBooks = recipeBooks.filter((book: RecipeBook) => {
    const matchesSearch = book.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         book.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (viewMode === 'my') {
      return matchesSearch && book.organizerId === user?.id;
    }
    
    return matchesSearch;
  });

  const handleCreateRecipeBook = () => {
    if (user?.role === UserRole.CONTRIBUTOR) {
      Alert.alert(
        'Permission Denied',
        'Only organizers and admins can create recipe books.'
      );
      return;
    }
    navigation.navigate('CreateRecipeBook');
  };

  const handleRecipeBookPress = (book: RecipeBook) => {
    navigation.navigate('RecipeBookDetail', { id: book.id });
  };

  const renderRecipeBook = ({ item }: { item: RecipeBook }) => (
    <TouchableOpacity
      style={styles.bookCard}
      onPress={() => handleRecipeBookPress(item)}
    >
      <View style={styles.bookHeader}>
        <Text style={styles.bookTitle}>{item.name}</Text>
        <Text style={styles.bookDate}>
          {new Date(item.createdAt).toLocaleDateString()}
        </Text>
      </View>
      
      <Text style={styles.bookDescription} numberOfLines={2}>
        {item.description}
      </Text>
      
      <View style={styles.bookFooter}>
        <View style={styles.contributorsInfo}>
          <Icon name="people" size={16} color={Colors.mutedForeground} />
          <Text style={styles.contributorsText}>
            {item.contributors?.length || 0} contributors
          </Text>
        </View>
        
        <View style={styles.statusBadge}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="error" size={48} color={Colors.destructive} />
        <Text style={styles.errorText}>Failed to load recipe books</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Recipe Books</Text>
        {(user?.role === UserRole.ORGANIZER || user?.role === UserRole.ADMIN) && (
          <TouchableOpacity
            style={styles.createButton}
            onPress={handleCreateRecipeBook}
          >
            <Icon name="add" size={24} color={Colors.primaryForeground} />
          </TouchableOpacity>
        )}
      </View>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color={Colors.mutedForeground} />
          <TextInput
            style={styles.searchInput}
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholder="Search recipe books..."
            placeholderTextColor={Colors.mutedForeground}
          />
        </View>
        
        <View style={styles.filterContainer}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              viewMode === 'all' && styles.filterButtonActive,
            ]}
            onPress={() => setViewMode('all')}
          >
            <Text
              style={[
                styles.filterButtonText,
                viewMode === 'all' && styles.filterButtonTextActive,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.filterButton,
              viewMode === 'my' && styles.filterButtonActive,
            ]}
            onPress={() => setViewMode('my')}
          >
            <Text
              style={[
                styles.filterButtonText,
                viewMode === 'my' && styles.filterButtonTextActive,
              ]}
            >
              My Books
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Recipe Books List */}
      {filteredRecipeBooks.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Icon name="book" size={64} color={Colors.mutedForeground} />
          <Text style={styles.emptyTitle}>No Recipe Books Found</Text>
          <Text style={styles.emptyText}>
            {searchQuery
              ? 'Try adjusting your search terms'
              : 'Create your first recipe book to get started'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredRecipeBooks}
          renderItem={renderRecipeBook}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={refetch}
              colors={[Colors.primary]}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.foreground,
  },
  createButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.sm,
  },
  searchContainer: {
    padding: Spacing.lg,
    backgroundColor: Colors.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  searchInput: {
    flex: 1,
    padding: Spacing.md,
    fontSize: 16,
    color: Colors.foreground,
    marginLeft: Spacing.sm,
  },
  filterContainer: {
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  filterButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    backgroundColor: Colors.background,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.foreground,
  },
  filterButtonTextActive: {
    color: Colors.primaryForeground,
  },
  listContainer: {
    padding: Spacing.lg,
  },
  bookCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.lg,
    marginBottom: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  bookHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.sm,
  },
  bookTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.foreground,
    flex: 1,
    marginRight: Spacing.sm,
  },
  bookDate: {
    fontSize: 12,
    color: Colors.mutedForeground,
  },
  bookDescription: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginBottom: Spacing.md,
    lineHeight: 20,
  },
  bookFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contributorsInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contributorsText: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginLeft: Spacing.xs,
  },
  statusBadge: {
    backgroundColor: Colors.secondary,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.secondaryForeground,
    textTransform: 'capitalize',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.foreground,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 24,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.xl,
  },
  errorText: {
    fontSize: 18,
    color: Colors.destructive,
    marginTop: Spacing.md,
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.md,
  },
  retryButtonText: {
    color: Colors.primaryForeground,
    fontSize: 16,
    fontWeight: '600',
  },
});
