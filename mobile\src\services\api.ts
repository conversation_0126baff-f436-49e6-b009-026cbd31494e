import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_URL } from '../lib/constants';
import { 
  ApiResponse, 
  Recipe, 
  RecipeBook, 
  RecipeBooksResponse, 
  RecipeFormData,
  PaginatedResponse,
  PaginationParams 
} from '../types';

class ApiService {
  private baseURL = API_URL;

  private async getAuthHeaders(): Promise<HeadersInit> {
    const token = await AsyncStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Recipe Books API
  async getRecipeBooks(): Promise<ApiResponse<RecipeBooksResponse>> {
    return this.request<RecipeBooksResponse>('/projects');
  }

  async getRecipeBook(id: number): Promise<ApiResponse<RecipeBook>> {
    return this.request<RecipeBook>(`/projects/${id}`);
  }

  async createRecipeBook(data: {
    name: string;
    description: string;
  }): Promise<ApiResponse<RecipeBook>> {
    return this.request<RecipeBook>('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateRecipeBook(
    id: number,
    data: Partial<RecipeBook>
  ): Promise<ApiResponse<RecipeBook>> {
    return this.request<RecipeBook>(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteRecipeBook(id: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/projects/${id}`, {
      method: 'DELETE',
    });
  }

  // Recipes API
  async getRecipes(params?: PaginationParams): Promise<ApiResponse<PaginatedResponse<Recipe>>> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const queryString = queryParams.toString();
    const endpoint = `/recipes${queryString ? `?${queryString}` : ''}`;
    
    return this.request<PaginatedResponse<Recipe>>(endpoint);
  }

  async getRecipe(id: number): Promise<ApiResponse<Recipe>> {
    return this.request<Recipe>(`/recipes/${id}`);
  }

  async createRecipe(data: RecipeFormData): Promise<ApiResponse<Recipe>> {
    return this.request<Recipe>('/recipes', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateRecipe(id: number, data: Partial<RecipeFormData>): Promise<ApiResponse<Recipe>> {
    return this.request<Recipe>(`/recipes/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteRecipe(id: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/recipes/${id}`, {
      method: 'DELETE',
    });
  }

  async getRecipesByProject(projectId: number): Promise<ApiResponse<Recipe[]>> {
    return this.request<Recipe[]>(`/projects/${projectId}/recipes`);
  }

  // Contributors API
  async inviteContributor(projectId: number, email: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/projects/${projectId}/invite`, {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async removeContributor(projectId: number, contributorId: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/projects/${projectId}/contributors/${contributorId}`, {
      method: 'DELETE',
    });
  }

  // File upload API
  async uploadImage(file: FormData): Promise<ApiResponse<{ url: string }>> {
    try {
      const token = await AsyncStorage.getItem('token');
      const response = await fetch(`${this.baseURL}/upload/image`, {
        method: 'POST',
        headers: {
          ...(token && { 'Authorization': `Bearer ${token}` }),
        },
        body: file,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Upload failed');
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      console.error('Image upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  // Recipe approval API (for organizers)
  async approveRecipe(id: number): Promise<ApiResponse<Recipe>> {
    return this.request<Recipe>(`/recipes/${id}/approve`, {
      method: 'POST',
    });
  }

  async rejectRecipe(id: number, reason?: string): Promise<ApiResponse<Recipe>> {
    return this.request<Recipe>(`/recipes/${id}/reject`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  // User profile API
  async updateProfile(data: {
    name?: string;
    email?: string;
  }): Promise<ApiResponse<any>> {
    return this.request('/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async changePassword(data: {
    currentPassword: string;
    newPassword: string;
  }): Promise<ApiResponse<void>> {
    return this.request<void>('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

export const apiService = new ApiService();
