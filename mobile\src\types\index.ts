import { UserRole } from '../lib/constants';

// User types
export interface User {
  id: number;
  email: string;
  name: string;
  role: typeof UserRole[keyof typeof UserRole];
  createdAt?: string;
  updatedAt?: string;
}

// Auth types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  role: keyof typeof UserRole;
}

export interface AuthResponse {
  user: User;
  token: string;
}

// Recipe types
export interface Ingredient {
  id?: number;
  name: string;
  amount: number;
  unit: string;
}

export interface Recipe {
  id: number;
  title: string;
  description?: string;
  ingredients: Ingredient[];
  instructions: string[];
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  tags?: string[];
  images?: string[];
  measurementSystem: 'us' | 'metric';
  status: 'draft' | 'pending' | 'approved' | 'rejected';
  contributorId: number;
  projectId: number;
  createdAt: string;
  updatedAt: string;
  contributor?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface RecipeFormData {
  title: string;
  description?: string;
  ingredients: Ingredient[];
  instructions: string[];
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  tags: string[];
  images: string[];
  measurementSystem: 'us' | 'metric';
  projectId?: number;
}

// Project/Recipe Book types
export interface Contributor {
  id: number;
  name: string;
  email: string;
  role: string;
  joinedAt: string;
}

export interface RecipeBook {
  id: number;
  name: string;
  description: string;
  status: string;
  role: string;
  createdAt: string;
  contributors: Contributor[];
  organizer?: { name: string };
  organizerId: number;
  recipes?: Recipe[];
}

export interface RecipeBooksResponse {
  projects: RecipeBook[];
}

// Navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  RecipeBooks: undefined;
  RecipeBookDetail: { id: number };
  CreateRecipeBook: undefined;
  CreateRecipe: { projectId?: number };
  EditRecipe: { id: number };
  RecipeDetail: { id: number };
  Profile: undefined;
  Settings: undefined;
  AdminPanel: undefined;
  OrganizerDashboard: undefined;
  ContributorDashboard: undefined;
};

export type MainTabParamList = {
  RecipeBooks: undefined;
  CreateRecipe: undefined;
  Profile: undefined;
  Settings: undefined;
};

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Form validation types
export interface FormErrors {
  [key: string]: string | undefined;
}

// Notification types
export interface Notification {
  id: number;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
  userId: number;
}

// Image types
export interface ImageAsset {
  uri: string;
  type?: string;
  name?: string;
  size?: number;
}

// Theme types
export interface ThemeColors {
  background: string;
  foreground: string;
  muted: string;
  mutedForeground: string;
  card: string;
  cardForeground: string;
  border: string;
  input: string;
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  accent: string;
  accentForeground: string;
  destructive: string;
  destructiveForeground: string;
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
  info: string;
  infoForeground: string;
}

// Query types
export interface QueryOptions {
  enabled?: boolean;
  refetchOnMount?: boolean;
  refetchOnWindowFocus?: boolean;
  staleTime?: number;
  cacheTime?: number;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
