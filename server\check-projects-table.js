import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function checkProjectsTable() {
  console.log('Checking projects table structure...');
  
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL is not defined');
    process.exit(1);
  }
  
  // Add sslmode=require if not already present
  let connectionString = process.env.DATABASE_URL;
  if (!connectionString.includes('sslmode=require')) {
    connectionString += connectionString.includes('?') 
      ? '&sslmode=require' 
      : '?sslmode=require';
  }
  
  console.log(`Connecting to database: ${connectionString.includes('@') ? connectionString.split('@')[1] : 'DB'}`);
  
  const client = postgres(connectionString, {
    ssl: { rejectUnauthorized: false },
    max: 1
  });
  
  try {
    console.log('Fetching column information...');
    
    const columns = await client`
      SELECT column_name, data_type, column_default, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'projects'
      ORDER BY ordinal_position;
    `;
    
    console.log('\nProjects Table Structure:');
    columns.forEach(col => {
      console.log(`${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'} ${col.column_default ? `Default: ${col.column_default}` : ''}`);
    });
    
    // Check for our new columns
    const newColumns = [
      'font', 'chapter_style', 'cover', 'cover_title', 'cover_subtitle',
      'use_custom_cover_image', 'dedication', 'include_dedication', 
      'include_quotes', 'family_quotes'
    ];
    
    const foundColumns = columns.filter(col => newColumns.includes(col.column_name));
    
    console.log('\nNew Columns Added:');
    if (foundColumns.length === 0) {
      console.log('None of the new columns were found!');
    } else {
      foundColumns.forEach(col => {
        console.log(`${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'} ${col.column_default ? `Default: ${col.column_default}` : ''}`);
      });
      
      console.log(`\nFound ${foundColumns.length} of ${newColumns.length} expected new columns`);
    }
  } catch (error) {
    console.error('Error checking table structure:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

checkProjectsTable();
