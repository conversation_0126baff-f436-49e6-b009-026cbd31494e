// Pricing Tiers
export const PricingTier = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
} as const;

// Pricing Model
export const PricingModel = {
  [PricingTier.SMALL]: {
    name: 'Small',
    maxContributors: 1, // Limited to 1 for testing
    minRecipes: 1,
    maxRecipes: 1, // Limited to 1 for testing
    basePrice: 29.99,
    pricePerPage: 0.10,
    description: 'Perfect for small families (up to 1 contributor and 1 recipe for testing)'
  },
  [PricingTier.MEDIUM]: {
    name: 'Medium',
    maxContributors: 5, // Reduced for testing
    minRecipes: 6,
    maxRecipes: 15,
    basePrice: 49.99,
    pricePerPage: 0.08,
    description: 'Great for extended families (up to 5 contributors)'
  },
  [PricingTier.LARGE]: {
    name: 'Large',
    maxContributors: 10, // Reduced for testing
    minRecipes: 16,
    maxRecipes: 30,
    basePrice: 79.99,
    pricePerPage: 0.06,
    description: 'Ideal for large family reunions (up to 10 contributors)'
  }
};

// Get max contributors for a pricing tier
export function getMaxContributors(tier: string): number {
  const tierKey = tier as keyof typeof PricingModel;
  if (tierKey in PricingModel) {
    return PricingModel[tierKey].maxContributors;
  }
  // Default to small tier if not found
  return PricingModel[PricingTier.SMALL].maxContributors;
}

// Check if a project has reached its contributor limit
export function hasReachedContributorLimit(
  currentContributorCount: number,
  pricingTier: string
): boolean {
  const maxContributors = getMaxContributors(pricingTier);
  return currentContributorCount >= maxContributors;
}

// Get max recipes for a pricing tier
export function getMaxRecipes(tier: string): number {
  const tierKey = tier as keyof typeof PricingModel;
  if (tierKey in PricingModel) {
    return PricingModel[tierKey].maxRecipes;
  }
  // Default to small tier if not found
  return PricingModel[PricingTier.SMALL].maxRecipes;
}

// Check if a project has reached its recipe limit
export function hasReachedRecipeLimit(
  currentRecipeCount: number,
  pricingTier: string
): boolean {
  const maxRecipes = getMaxRecipes(pricingTier);

  // When adding a new recipe, we need to check if the current count is already at or above the max
  const willExceedLimit = currentRecipeCount >= maxRecipes;

  console.log(`[Recipe Limit] Checking limit for tier ${pricingTier}: ${currentRecipeCount} recipes (max: ${maxRecipes}), will exceed: ${willExceedLimit}`);

  return willExceedLimit;
}
