import { db } from '../db.js';
import { projects } from '../schema.js';
import { eq, sql } from 'drizzle-orm';

/**
 * Save book customization options for a specific recipe book
 */
export const saveBookCustomization = async (req, res) => {
  try {
    const { id } = req.params;
   // Validate inputs
   if (!id || isNaN(parseInt(id))) {
     return res.status(400).json({ error: 'Invalid book ID' });
   }
    const {
      theme,
      font,
      chapterStyle,
      cover,
      coverTitle,
      coverSubtitle,
      coverImage,
      useCustomCoverImage,
      dedication,
      includeDedication,
      includeQuotes,
      familyQuotes
    } = req.body;

   // Validate that required fields are present
   if (!theme || !font || !chapterStyle || !cover) {
     return res.status(400).json({ error: 'Missing required customization fields' });
   }
    // Verify the project exists and user has permission
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(id))
    });

    if (!project) {
      return res.status(404).json({ error: 'Recipe book not found' });
    }

    // Check if user is organizer or admin
 if (!req.user ||
     (req.user.role !== 'admin' && req.user.id !== project.organizerId)) {
     return res.status(403).json({ error: 'Not authorized to customize this book' });
   }

    // Update the project with customization options
    await db.update(projects)
      .set({
        theme,
        font,
        chapterStyle,
        cover,
        coverTitle,
        coverSubtitle,
        coverImage,
        useCustomCoverImage,
        dedication,
        includeDedication,
        includeQuotes,
        familyQuotes: JSON.stringify(familyQuotes || [])
      })
      .where(eq(projects.id, parseInt(id)));

    res.status(200).json({ message: 'Book customization saved successfully' });
  } catch (error) {
    console.error('Error saving book customization:', error);
    res.status(500).json({ error: 'Failed to save book customization' });
  }
};

/**
 * Get book customization options for a specific recipe book
 */
export const getBookCustomization = async (req, res) => {
  try {
    const { id } = req.params;

    // Get the project with customization options
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(id)),
      columns: {
        theme: true,
        font: true,
        chapterStyle: true,
        cover: true,
        coverTitle: true,
        coverSubtitle: true,
        coverImage: true,
        useCustomCoverImage: true,
        dedication: true,
        includeDedication: true,
        includeQuotes: true,
        familyQuotes: true
      }
    });

    if (!project) {
      return res.status(404).json({ error: 'Recipe book not found' });
    }

    // Create a response object with the correct property names
    const response = {
      theme: project.theme,
      font: project.font,
      chapterStyle: project.chapterStyle,
      cover: project.cover,
      coverTitle: project.coverTitle,
      coverSubtitle: project.coverSubtitle,
      coverImage: project.coverImage,
      useCustomCoverImage: project.useCustomCoverImage,
      dedication: project.dedication,
      includeDedication: project.includeDedication,
      includeQuotes: project.includeQuotes,
      familyQuotes: []
    };

    // Parse JSON fields
    if (project.familyQuotes) {
      try {

         // Check if it's already an array (might happen with some ORMs)
         if (Array.isArray(project.familyQuotes)) {
           response.familyQuotes = project.familyQuotes;
         } else {
           response.familyQuotes = JSON.parse(project.familyQuotes);
         }

      } catch (e) {
        console.error('Error parsing familyQuotes:', e);
        response.familyQuotes = [];
      }
    } else {
      response.familyQuotes = [];
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting book customization:', error);
    res.status(500).json({ error: 'Failed to get book customization' });
  }
};
