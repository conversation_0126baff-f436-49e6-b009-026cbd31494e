import cron from 'node-cron';
import { sendContributorR<PERSON><PERSON>s } from '../services/reminders.js';
import { db } from '../db.js';
import { projectContributors } from '../schema.js';
import { sql } from 'drizzle-orm';

// Helper function to get cron expression for a reminder frequency
function getCronExpression(frequency: string): string {
  switch (frequency) {
    case '1min': return '* * * * *'; // Every minute
    case '2min': return '*/2 * * * *'; // Every 2 minutes
    case '5min': return '*/5 * * * *'; // Every 5 minutes
    case '15min': return '*/15 * * * *'; // Every 15 minutes
    case '30min': return '*/30 * * * *'; // Every 30 minutes
    case '1hour': return '0 * * * *'; // Every hour
    case 'daily': return '0 9 * * *'; // Every day at 9 AM
    case 'weekly': return '0 9 * * 1'; // Every Monday at 9 AM
    case 'biweekly': return '0 9 1,15 * *'; // 1st and 15th at 9 AM
    case 'monthly': return '0 9 1 * *'; // 1st of every month at 9 AM
    default: return '0 9 * * *'; // Default to daily at 9 AM
  }
}

// Function to get the most frequent reminder interval from active contributors
async function getMostFrequentReminderInterval(): Promise<string> {
  console.log('Checking for most frequent reminder interval...');
  const result = await db.select({
    frequency: projectContributors.reminderFrequency
  })
  .from(projectContributors)
  .where(sql`${projectContributors.status} IN ('pending', 'accepted')`)
  .orderBy(sql`CASE 
    WHEN ${projectContributors.reminderFrequency} = '1min' THEN 1
    WHEN ${projectContributors.reminderFrequency} = '2min' THEN 2
    WHEN ${projectContributors.reminderFrequency} = '5min' THEN 3
    WHEN ${projectContributors.reminderFrequency} = '15min' THEN 4
    WHEN ${projectContributors.reminderFrequency} = '30min' THEN 5
    WHEN ${projectContributors.reminderFrequency} = '1hour' THEN 6
    WHEN ${projectContributors.reminderFrequency} = 'daily' THEN 7
    WHEN ${projectContributors.reminderFrequency} = 'weekly' THEN 8
    WHEN ${projectContributors.reminderFrequency} = 'biweekly' THEN 9
    WHEN ${projectContributors.reminderFrequency} = 'monthly' THEN 10
    ELSE 11
  END`)
  .limit(1);

  const frequency = result[0]?.frequency || 'daily';
  console.log(`Most frequent reminder interval found: ${frequency}`);
  return frequency;
}

let currentCronJob: cron.ScheduledTask | null = null;

// Function to update the cron schedule
async function updateCronSchedule() {
  console.log('Updating cron schedule...');
  const frequency = await getMostFrequentReminderInterval();
  const cronExpression = getCronExpression(frequency);

  // Stop the current cron job if it exists
  if (currentCronJob) {
    console.log('Stopping existing cron job...');
    currentCronJob.stop();
  }

  // Start a new cron job with the updated schedule
  console.log(`Starting new cron job with expression: ${cronExpression}`);
  currentCronJob = cron.schedule(cronExpression, async () => {
    console.log(`[${new Date().toISOString()}] Running contributor reminders cron job (frequency: ${frequency})...`);
    try {
      await sendContributorReminders();
      console.log(`[${new Date().toISOString()}] Contributor reminders sent successfully`);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error running contributor reminders:`, error);
    }
  });

  console.log(`Updated reminder schedule to run ${frequency}`);
}

// Initial schedule setup
console.log('Initializing reminder cron job...');
updateCronSchedule();

// Update schedule every hour to check for changes in reminder frequencies
console.log('Setting up hourly schedule check...');
cron.schedule('0 * * * *', () => {
  console.log('Checking for reminder frequency changes...');
  updateCronSchedule();
}); 