import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get environment variables
const DATABASE_URL = process.env.DATABASE_URL;
const JWT_SECRET = process.env.JWT_SECRET;

if (!DATABASE_URL) {
  throw new Error('DATABASE_URL is not defined');
}

if (!JWT_SECRET) {
  throw new Error('JWT_SECRET is not defined');
}

console.log('Connecting to database...'); // Debug log

// Create postgres client
const client = postgres(DATABASE_URL);

// Create drizzle instance
export const db = drizzle(client, { schema });

// Test database connection
client`SELECT 1`.then(() => {
  console.log('Database connection successful'); // Debug log
}).catch((error) => {
  console.error('Database connection failed:', error); // Debug log
  process.exit(1);
}); 