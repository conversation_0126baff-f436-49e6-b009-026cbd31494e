import { fileURLToPath } from 'url';
import { dirname } from 'path';
import postgres from 'postgres';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

const dropTablesQuery = `
-- Drop tables in reverse order of their dependencies
DROP TABLE IF EXISTS recipes CASCADE;
DROP TABLE IF EXISTS project_contributors CASCADE;
DROP TABLE IF EXISTS projects CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS users CASCADE;
`;

const dropTables = async () => {
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL is not set in environment variables');
  }

  const sql = postgres(process.env.DATABASE_URL, { max: 1 });

  try {
    console.log('Dropping all tables...');
    await sql.unsafe(dropTablesQuery);
    console.log('All tables dropped successfully');
  } catch (error) {
    console.error('Error dropping tables:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
};

dropTables(); 