import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from '../auth.js';

export const isAdmin = (req: AuthRequest, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized' });
  }

  next();
};

export const canManageAdmins = (req: AuthRequest, res: Response, next: NextFunction) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Not authorized to manage admins' });
  }

  // Check if the request is trying to create or modify an admin user
  const { role } = req.body;
  if (role === 'admin') {
    // Only allow if the requesting user is an admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Only admins can create or modify admin users' });
    }
  }

  next();
}; 