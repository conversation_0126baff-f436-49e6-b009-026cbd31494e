import { Request, Response, NextFunction, RequestHandler } from 'express';
import jwt from 'jsonwebtoken';
import { db } from '../db.js';
import { users } from '../schema.js';
import { eq } from 'drizzle-orm';
import { UserRole } from '../schema.js';

export interface AuthRequest extends Request {
  user?: {
    id: number;
    email: string;
    name: string;
    role: string;
  };
}

// Helper function to convert AuthRequestHandler to RequestHandler
export const asHandler = (handler: (req: AuthRequest, res: Response) => Promise<Response | void>): RequestHandler => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const authReq = req as AuthRequest;
    return handler(authReq, res);
  };
};

export const authMiddleware: RequestHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ message: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { id: number; email: string; role: string };
    
    const user = await db.query.users.findFirst({
      where: eq(users.id, decoded.id)
    });

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    (req as AuthRequest).user = {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    };

    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({ message: 'Invalid token' });
  }
};

export const requireAdmin: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthRequest;
  if (!authReq.user) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  if (authReq.user.role !== UserRole.ADMIN) {
    return res.status(403).json({ message: 'Not authorized' });
  }

  next();
};

export const requireOrganizer: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthRequest;
  if (!authReq.user) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  if (authReq.user.role !== UserRole.ORGANIZER && authReq.user.role !== UserRole.ADMIN && authReq.user.role !== UserRole.CONTRIBUTOR) {
    return res.status(403).json({ message: 'Not authorized' });
  }

  next();
};

// Role-specific middleware
export const requireContributor: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthRequest;
  if (!authReq.user) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  if (authReq.user.role !== UserRole.CONTRIBUTOR) {
    return res.status(403).json({ message: 'Not authorized' });
  }

  next();
};

// Project-specific middleware
export const requireProjectAccess: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthRequest;
  const projectId = parseInt(req.params.projectId);
  const userId = authReq.user?.id;

  if (!userId) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  // TODO: Check if user has access to the project
  // This will be implemented when we have the project access logic

  next();
}; 