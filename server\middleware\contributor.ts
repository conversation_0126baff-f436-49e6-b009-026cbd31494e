import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from '../auth.js';

export function isContributor(req: AuthRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  // if (req.user.role !== 'contributor') {
  //   return res.status(403).json({ message: 'Contributor access required' });
  // }

  next();
} 