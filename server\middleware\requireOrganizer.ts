import { Request, Response, NextFunction } from 'express';
import { AuthRequest } from '../middleware/auth.js';
import { UserRole } from '../schema.js';

export const requireOrganizer = (req: Request, res: Response, next: NextFunction) => {
  const authReq = req as AuthRequest;
  
  if (!authReq.user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }
  
  if (authReq.user.role !== UserRole.ORGANIZER && authReq.user.role !== UserRole.ADMIN) {
    return res.status(403).json({ message: 'Forbidden: Organizer access required' });
  }
  
  next();
}; 