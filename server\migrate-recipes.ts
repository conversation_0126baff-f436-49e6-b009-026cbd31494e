import path from 'path';
import { db } from './db';
import { recipes } from './schema';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import postgres from 'postgres';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

const recipesJsonPath = path.join(__dirname, 'recipes.json');

async function migrateRecipes() {
  const recipesJson = JSON.parse(readFileSync(recipesJsonPath, 'utf-8'));

  for (const recipe of recipesJson) {
    await db.insert(recipes).values({
      id: recipe.id,
      title: recipe.title,
      content: recipe.content,
      createdAt: new Date(recipe.createdAt),
      updatedAt: new Date(recipe.updatedAt),
      projectId: recipe.projectId,
      contributorId: recipe.contributorId
    });
  }

  console.log('Recipes migrated successfully');
}

migrateRecipes().catch(console.error);

const runMigration = async () => {
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL is not set in environment variables');
  }

  const sql = postgres(process.env.DATABASE_URL, { max: 1 });

  try {
    console.log('Running recipes table migration...');
    
    // Drop existing recipes table
    console.log('Dropping existing recipes table...');
    await sql.unsafe('DROP TABLE IF EXISTS recipes CASCADE;');
    
    const migrationFile = path.join(__dirname, 'migrations', '0006_create_recipes_table.sql');
    const migrationContent = readFileSync(migrationFile, 'utf-8');
    
    console.log('Creating new recipes table...');
    await sql.unsafe(migrationContent);
    
    console.log('Recipes table migration completed successfully');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
};

runMigration(); 