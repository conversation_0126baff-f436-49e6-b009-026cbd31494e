import postgres from 'postgres';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Set up logging to both console and file
const logFile = path.join(process.cwd(), 'migration-log.txt');
fs.writeFileSync(logFile, `Migration started at ${new Date().toISOString()}\n`);

function log(message: string, level = 'INFO'): void {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(logFile, logMessage + '\n');
}

// SQL query to add voice transcription fields
const alterTableQuery = `
-- Add voice transcription fields to recipes table
ALTER TABLE recipes 
ADD COLUMN IF NOT EXISTS is_voice_transcribed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS voice_source_audio TEXT,
ADD COLUMN IF NOT EXISTS voice_raw_text TEXT,
ADD COLUMN IF NOT EXISTS voice_extracted_data JSONB;
`;

// Try to extract database URL from environment
let databaseUrl = process.env.DATABASE_URL;
log(`DATABASE_URL from environment: ${databaseUrl ? 'Found' : 'Not found'}`);

// Modify the list of possible database URLs to try more combinations
const possibleUrls = [
  // Add the correct database URL as the first option to try
  "postgresql://storyworth_owner:<EMAIL>/storyworth?sslmode=require",
  databaseUrl,
  "postgres://postgres:postgres@localhost:5432/storyworth_db",
  "postgres://postgres:postgres@localhost:5432/postgres",
  // Try different user combinations for Neon database
  "postgresql://postgres:<EMAIL>/storyworth?sslmode=require",
  "postgresql://<EMAIL>/storyworth?sslmode=require", 
  "postgresql://<EMAIL>/storyworth?sslmode=require",
  "postgresql://neon:<EMAIL>/storyworth?sslmode=require",
  "postgresql://default:<EMAIL>/storyworth?sslmode=require",
  "postgresql://admin:<EMAIL>/storyworth?sslmode=require",
  // Common Neon default usernames
  "postgresql://postgres:<EMAIL>/storyworth?sslmode=require",
  "postgresql://neon:<EMAIL>/storyworth?sslmode=require",
  "postgresql://root:<EMAIL>/storyworth?sslmode=require",
  // Try without the pooler part
  "postgresql://postgres:<EMAIL>/storyworth?sslmode=require"
].filter(Boolean) as string[]; // Filter out undefined/null values

// Function to check if a column exists
async function columnExists(sql: postgres.Sql, table: string, column: string): Promise<boolean> {
  try {
    const result = await sql`
      SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = ${table}
        AND column_name = ${column}
      );
    `;
    return result[0]?.exists || false;
  } catch (error) {
    log(`Error checking if column ${column} exists in table ${table}: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    return false;
  }
}

async function tryConnect(url: string): Promise<postgres.Sql | null> {
  // Hide credentials in logs
  const logUrl = url.includes('@') ? url.split('@')[1] : 'unknown';
  log(`Attempting to connect to: ${logUrl}`);
  
  try {
    const sql = postgres(url, {
      max: 1,
      idle_timeout: 5,
      connect_timeout: 10
    });
    
    // Test connection
    await sql`SELECT 1`;
    log(`Successfully connected to ${logUrl}`);
    return sql;
  } catch (error) {
    log(`Failed to connect to ${logUrl}: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    return null;
  }
}

async function runMigration(): Promise<boolean> {
  log('Starting migration process for voice transcription fields');
  
  // Try each connection URL
  let sql: postgres.Sql | null = null;
  
  // Maximum retries
  const maxRetries = 3;
  let currentRetry = 0;
  
  while (currentRetry < maxRetries && !sql) {
    if (currentRetry > 0) {
      log(`Retry attempt ${currentRetry} of ${maxRetries}`);
      // Wait between retries
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    for (const url of possibleUrls) {
      sql = await tryConnect(url);
      if (sql) break;
    }
    
    currentRetry++;
    
    // If still no connection and we haven't maxed out retries
    if (!sql && currentRetry < maxRetries) {
      log(`All connection attempts failed on try ${currentRetry}. Retrying in 2 seconds...`, 'WARN');
    }
  }
  
  if (!sql) {
    log('Failed to connect to any database after all retries', 'ERROR');
    return false;
  }
  
  try {
    // Check if recipes table exists
    log('Checking if recipes table exists');
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'recipes'
      );
    `;
    
    if (!tableExists[0]?.exists) {
      log('Recipes table does not exist. Cannot proceed with migration.', 'ERROR');
      return false;
    }
    
    log('Recipes table exists. Checking for existing columns.');
    
    // Check if columns already exist
    const isVoiceTranscribedExists = await columnExists(sql, 'recipes', 'is_voice_transcribed');
    const voiceSourceAudioExists = await columnExists(sql, 'recipes', 'voice_source_audio');
    const voiceRawTextExists = await columnExists(sql, 'recipes', 'voice_raw_text');
    const voiceExtractedDataExists = await columnExists(sql, 'recipes', 'voice_extracted_data');
    
    log(`Column 'is_voice_transcribed' exists: ${isVoiceTranscribedExists}`);
    log(`Column 'voice_source_audio' exists: ${voiceSourceAudioExists}`);
    log(`Column 'voice_raw_text' exists: ${voiceRawTextExists}`);
    log(`Column 'voice_extracted_data' exists: ${voiceExtractedDataExists}`);
    
    if (isVoiceTranscribedExists && voiceSourceAudioExists && voiceRawTextExists && voiceExtractedDataExists) {
      log('All required columns already exist. Migration is not necessary.');
      return true;
    }
    
    // Execute the alter table query
    log('Adding missing voice transcription fields');
    await sql.unsafe(alterTableQuery);
    log('SQL query executed successfully');
    
    // Verify columns were added
    log('Verifying columns were added successfully');
    const verifyIsVoiceTranscribedExists = await columnExists(sql, 'recipes', 'is_voice_transcribed');
    const verifyVoiceSourceAudioExists = await columnExists(sql, 'recipes', 'voice_source_audio');
    const verifyVoiceRawTextExists = await columnExists(sql, 'recipes', 'voice_raw_text');
    const verifyVoiceExtractedDataExists = await columnExists(sql, 'recipes', 'voice_extracted_data');
    
    log(`Verified 'is_voice_transcribed' exists: ${verifyIsVoiceTranscribedExists}`);
    log(`Verified 'voice_source_audio' exists: ${verifyVoiceSourceAudioExists}`);
    log(`Verified 'voice_raw_text' exists: ${verifyVoiceRawTextExists}`);
    log(`Verified 'voice_extracted_data' exists: ${verifyVoiceExtractedDataExists}`);
    
    const allColumnsAdded = verifyIsVoiceTranscribedExists && 
                            verifyVoiceSourceAudioExists && 
                            verifyVoiceRawTextExists && 
                            verifyVoiceExtractedDataExists;
    
    if (allColumnsAdded) {
      log('Migration completed successfully. All voice transcription fields are now in the database.');
      return true;
    } else {
      log('Some columns were not added successfully.', 'ERROR');
      return false;
    }
  } catch (error) {
    log(`Error during migration: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    log(`Error stack: ${error instanceof Error && error.stack ? error.stack : 'No stack trace available'}`, 'ERROR');
    return false;
  } finally {
    if (sql) {
      log('Closing database connection');
      await sql.end();
      log('Database connection closed');
    }
  }
}

// Execute migration with retries
(async function() {
  let result = false;
  const maxTotalRetries = 5;
  let retryCount = 0;
  
  while (!result && retryCount < maxTotalRetries) {
    if (retryCount > 0) {
      log(`Overall migration retry ${retryCount} of ${maxTotalRetries}`);
      // Wait longer between full retries
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    result = await runMigration();
    retryCount++;
    
    if (!result && retryCount < maxTotalRetries) {
      log(`Migration failed. Will retry entire process in 3 seconds (attempt ${retryCount + 1} of ${maxTotalRetries})`, 'WARN');
    }
  }
  
  if (result) {
    log('MIGRATION SUCCESSFUL - Voice transcription fields have been added to the recipes table');
    log(`Migration log saved to: ${logFile}`);
  } else {
    log('MIGRATION FAILED - Could not add voice transcription fields after multiple attempts', 'ERROR');
    log(`Detailed logs available in: ${logFile}`);
    process.exit(1);
  }
})(); 