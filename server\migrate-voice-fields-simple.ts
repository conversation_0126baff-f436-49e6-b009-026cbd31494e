import postgres from 'postgres';

// Detailed logging function
function log(message: string, level = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

// Try to use the DATABASE_URL directly from env
// Set the direct postgres URL that you mentioned in the error logs
const DATABASE_URL = process.env.DATABASE_URL || "postgres://postgres:postgres@localhost:5432/storyworth_db";

// If that doesn't work, we'll look for other databases shown in the logs
const FALLBACK_URLS = [
  "postgres://postgres:postgres@localhost:5432/storyworth_db",
  "postgres://postgres:postgres@localhost:5432/postgres",
  // Add the connection string you've seen in the logs earlier
  "postgresql://postgres:<EMAIL>/storyworth?sslmode=require"
];

// SQL query to add voice transcription fields
const alterTableQuery = `
-- Add voice transcription fields to recipes table
ALTER TABLE recipes 
ADD COLUMN IF NOT EXISTS is_voice_transcribed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS voice_source_audio TEXT,
ADD COLUMN IF NOT EXISTS voice_raw_text TEXT,
ADD COLUMN IF NOT EXISTS voice_extracted_data JSONB;
`;

async function tryConnection(url: string) {
  log(`Trying to connect to: ${url.includes('@') ? url.split('@')[1] : 'DB'}`);
  try {
    const sql = postgres(url, { 
      max: 1,
      connect_timeout: 10,
      idle_timeout: 10
    });
    
    // Test connection
    await sql`SELECT 1`;
    log(`Connection successful to ${url.includes('@') ? url.split('@')[1] : 'DB'}`);
    return sql;
  } catch (error) {
    log(`Connection failed to ${url.includes('@') ? url.split('@')[1] : 'DB'}: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    return null;
  }
}

async function runMigration() {
  log('Starting migration: add-voice-transcription-fields');
  
  // Try the main URL first
  let sql = await tryConnection(DATABASE_URL);
  
  // If that fails, try the fallbacks
  if (!sql) {
    log('Main database connection failed, trying fallbacks', 'WARN');
    for (const url of FALLBACK_URLS) {
      sql = await tryConnection(url);
      if (sql) break;
    }
  }
  
  if (!sql) {
    log('All connection attempts failed', 'ERROR');
    return false;
  }
  
  try {
    log('Adding voice transcription fields to recipes table');
    await sql.unsafe(alterTableQuery);
    log('Migration completed successfully');
    return true;
  } catch (error) {
    log(`Error during migration: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    return false;
  } finally {
    log('Closing database connection');
    await sql.end();
    log('Database connection closed');
  }
}

// Execute migration
runMigration()
  .then(success => {
    if (success) {
      log('Voice transcription fields added successfully');
      log('Migration complete');
    } else {
      log('Migration failed', 'ERROR');
      process.exit(1);
    }
  })
  .catch(error => {
    log(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    process.exit(1);
  }); 