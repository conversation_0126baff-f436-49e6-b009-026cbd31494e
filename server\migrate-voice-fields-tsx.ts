import { Client } from 'pg';
import * as fs from 'fs';
import * as path from 'path';

// Set up logging to both console and file
const logFile = path.join(process.cwd(), 'migration-log.txt');
fs.writeFileSync(logFile, `Migration started at ${new Date().toISOString()}\n`);

function log(message: string, level = 'INFO'): void {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(logFile, logMessage + '\n');
}

// SQL query to add voice transcription fields
const alterTableQuery = `
-- Add voice transcription fields to recipes table
ALTER TABLE recipes 
ADD COLUMN IF NOT EXISTS is_voice_transcribed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS voice_source_audio TEXT,
ADD COLUMN IF NOT EXISTS voice_raw_text TEXT,
ADD COLUMN IF NOT EXISTS voice_extracted_data JSONB;
`;

// Use the provided database URL
const DATABASE_URL = "postgresql://storyworth_owner:<EMAIL>/storyworth?sslmode=require";

// Function to check if a column exists
async function columnExists(client: Client, table: string, column: string): Promise<boolean> {
  try {
    const result = await client.query(`
      SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = $1
        AND column_name = $2
      );
    `, [table, column]);
    return result.rows[0]?.exists || false;
  } catch (error) {
    log(`Error checking if column ${column} exists in table ${table}: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    return false;
  }
}

async function runMigration(): Promise<boolean> {
  log('Starting migration process for voice transcription fields');
  
  const client = new Client({
    connectionString: DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });
  
  try {
    log('Connecting to database...');
    await client.connect();
    log('Successfully connected to database');
    
    // Check if recipes table exists
    log('Checking if recipes table exists');
    const tableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'recipes'
      );
    `);
    
    const tableExists = tableResult.rows[0]?.exists;
    if (!tableExists) {
      log('Recipes table does not exist. Cannot proceed with migration.', 'ERROR');
      return false;
    }
    
    log('Recipes table exists. Checking for existing columns.');
    
    // Check if columns already exist
    const isVoiceTranscribedExists = await columnExists(client, 'recipes', 'is_voice_transcribed');
    const voiceSourceAudioExists = await columnExists(client, 'recipes', 'voice_source_audio');
    const voiceRawTextExists = await columnExists(client, 'recipes', 'voice_raw_text');
    const voiceExtractedDataExists = await columnExists(client, 'recipes', 'voice_extracted_data');
    
    log(`Column 'is_voice_transcribed' exists: ${isVoiceTranscribedExists}`);
    log(`Column 'voice_source_audio' exists: ${voiceSourceAudioExists}`);
    log(`Column 'voice_raw_text' exists: ${voiceRawTextExists}`);
    log(`Column 'voice_extracted_data' exists: ${voiceExtractedDataExists}`);
    
    if (isVoiceTranscribedExists && voiceSourceAudioExists && voiceRawTextExists && voiceExtractedDataExists) {
      log('All required columns already exist. Migration is not necessary.');
      return true;
    }
    
    // Execute the alter table query
    log('Adding missing voice transcription fields');
    await client.query(alterTableQuery);
    log('SQL query executed successfully');
    
    // Verify columns were added
    log('Verifying columns were added successfully');
    const verifyIsVoiceTranscribedExists = await columnExists(client, 'recipes', 'is_voice_transcribed');
    const verifyVoiceSourceAudioExists = await columnExists(client, 'recipes', 'voice_source_audio');
    const verifyVoiceRawTextExists = await columnExists(client, 'recipes', 'voice_raw_text');
    const verifyVoiceExtractedDataExists = await columnExists(client, 'recipes', 'voice_extracted_data');
    
    log(`Verified 'is_voice_transcribed' exists: ${verifyIsVoiceTranscribedExists}`);
    log(`Verified 'voice_source_audio' exists: ${verifyVoiceSourceAudioExists}`);
    log(`Verified 'voice_raw_text' exists: ${verifyVoiceRawTextExists}`);
    log(`Verified 'voice_extracted_data' exists: ${verifyVoiceExtractedDataExists}`);
    
    const allColumnsAdded = verifyIsVoiceTranscribedExists && 
                            verifyVoiceSourceAudioExists && 
                            verifyVoiceRawTextExists && 
                            verifyVoiceExtractedDataExists;
    
    if (allColumnsAdded) {
      log('Migration completed successfully. All voice transcription fields are now in the database.');
      return true;
    } else {
      log('Some columns were not added successfully.', 'ERROR');
      return false;
    }
  } catch (error) {
    log(`Error during migration: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    log(`Error stack: ${error instanceof Error && error.stack ? error.stack : 'No stack trace available'}`, 'ERROR');
    return false;
  } finally {
    try {
      log('Closing database connection');
      await client.end();
      log('Database connection closed');
    } catch (err) {
      log(`Error while closing connection: ${err instanceof Error ? err.message : 'Unknown error'}`, 'ERROR');
    }
  }
}

// Execute migration with retries
(async function() {
  let result = false;
  const maxTotalRetries = 5;
  let retryCount = 0;
  
  while (!result && retryCount < maxTotalRetries) {
    if (retryCount > 0) {
      log(`Overall migration retry ${retryCount} of ${maxTotalRetries}`);
      // Wait longer between full retries
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    result = await runMigration();
    retryCount++;
    
    if (!result && retryCount < maxTotalRetries) {
      log(`Migration failed. Will retry entire process in 3 seconds (attempt ${retryCount + 1} of ${maxTotalRetries})`, 'WARN');
    }
  }
  
  if (result) {
    log('MIGRATION SUCCESSFUL - Voice transcription fields have been added to the recipes table');
    log(`Migration log saved to: ${logFile}`);
  } else {
    log('MIGRATION FAILED - Could not add voice transcription fields after multiple attempts', 'ERROR');
    log(`Detailed logs available in: ${logFile}`);
    process.exit(1);
  }
})(); 