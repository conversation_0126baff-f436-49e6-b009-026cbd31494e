import { fileURLToPath } from 'url';
import { dirname } from 'path';
import postgres from 'postgres';
import dotenv from 'dotenv';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create a log file for the migration
const logFile = `${__dirname}/migration-log-${Date.now()}.txt`;

// Enhanced logging function that writes to both console and file
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  
  // Log to console
  console.log(logMessage);
  
  // Log to file
  fs.appendFileSync(logFile, logMessage + '\n');
}

// Load environment variables
log('Loading environment variables');
dotenv.config();

// SQL query to add voice transcription fields
const alterTableQuery = `
-- Add voice transcription fields to recipes table
ALTER TABLE recipes 
ADD COLUMN IF NOT EXISTS is_voice_transcribed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS voice_source_audio TEXT,
ADD COLUMN IF NOT EXISTS voice_raw_text TEXT,
ADD COLUMN IF NOT EXISTS voice_extracted_data JSONB;
`;

// Function to check if a column exists
const columnExistsQuery = (table, column) => `
SELECT EXISTS (
  SELECT 1
  FROM information_schema.columns
  WHERE table_name = '${table}'
  AND column_name = '${column}'
);
`;

const runMigration = async (attempt = 1, maxAttempts = 3) => {
  log(`Migration attempt ${attempt} of ${maxAttempts}`);
  
  if (!process.env.DATABASE_URL) {
    log('DATABASE_URL environment variable is required', 'ERROR');
    log('Checking for .env file presence');
    
    if (fs.existsSync(`${__dirname}/.env`)) {
      log('.env file exists in server directory, reading directly');
      const envContent = fs.readFileSync(`${__dirname}/.env`, 'utf8');
      const dbUrlMatch = envContent.match(/DATABASE_URL=(.+)/);
      if (dbUrlMatch && dbUrlMatch[1]) {
        process.env.DATABASE_URL = dbUrlMatch[1];
        log(`Found DATABASE_URL in .env file: ${process.env.DATABASE_URL.substring(0, 20)}...`);
      } else {
        log('DATABASE_URL not found in .env file', 'ERROR');
      }
    } else {
      log('.env file not found in server directory', 'WARN');
    }
    
    // If still not set, fallback to local development database
    if (!process.env.DATABASE_URL) {
      process.env.DATABASE_URL = 'postgres://postgres:postgres@localhost:5432/storyworth_db';
      log(`Using fallback DATABASE_URL for local development: ${process.env.DATABASE_URL}`, 'WARN');
    }
  }

  log(`Connecting to database: ${process.env.DATABASE_URL.split('@')[1] || 'DB'}`);
  let sql;
  
  try {
    sql = postgres(process.env.DATABASE_URL, { max: 1 });
    log('Database connection established successfully');
    
    // First, check if table exists
    log('Checking if recipes table exists');
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'recipes'
      );
    `;
    
    if (!tableExists[0]?.exists) {
      log('Recipes table does not exist, cannot add columns', 'ERROR');
      return false;
    }
    
    log('Checking if voice transcription fields already exist');
    const isVoiceTranscribedExists = await sql.unsafe(columnExistsQuery('recipes', 'is_voice_transcribed'));
    const voiceSourceAudioExists = await sql.unsafe(columnExistsQuery('recipes', 'voice_source_audio'));
    const voiceRawTextExists = await sql.unsafe(columnExistsQuery('recipes', 'voice_raw_text'));
    const voiceExtractedDataExists = await sql.unsafe(columnExistsQuery('recipes', 'voice_extracted_data'));
    
    log(`Column is_voice_transcribed exists: ${isVoiceTranscribedExists[0]?.exists}`);
    log(`Column voice_source_audio exists: ${voiceSourceAudioExists[0]?.exists}`);
    log(`Column voice_raw_text exists: ${voiceRawTextExists[0]?.exists}`);
    log(`Column voice_extracted_data exists: ${voiceExtractedDataExists[0]?.exists}`);
    
    if (
      isVoiceTranscribedExists[0]?.exists && 
      voiceSourceAudioExists[0]?.exists && 
      voiceRawTextExists[0]?.exists && 
      voiceExtractedDataExists[0]?.exists
    ) {
      log('All voice transcription fields already exist, migration not needed', 'INFO');
      return true;
    }
    
    log('Adding voice transcription fields to recipes table');
    await sql.unsafe(alterTableQuery);
    log('Fields added successfully');
    
    // Verify columns were added
    log('Verifying that columns were added successfully');
    const verifyIsVoiceTranscribedExists = await sql.unsafe(columnExistsQuery('recipes', 'is_voice_transcribed'));
    const verifyVoiceSourceAudioExists = await sql.unsafe(columnExistsQuery('recipes', 'voice_source_audio'));
    const verifyVoiceRawTextExists = await sql.unsafe(columnExistsQuery('recipes', 'voice_raw_text'));
    const verifyVoiceExtractedDataExists = await sql.unsafe(columnExistsQuery('recipes', 'voice_extracted_data'));
    
    log(`Verified is_voice_transcribed exists: ${verifyIsVoiceTranscribedExists[0]?.exists}`);
    log(`Verified voice_source_audio exists: ${verifyVoiceSourceAudioExists[0]?.exists}`);
    log(`Verified voice_raw_text exists: ${verifyVoiceRawTextExists[0]?.exists}`);
    log(`Verified voice_extracted_data exists: ${verifyVoiceExtractedDataExists[0]?.exists}`);
    
    if (
      !verifyIsVoiceTranscribedExists[0]?.exists || 
      !verifyVoiceSourceAudioExists[0]?.exists || 
      !verifyVoiceRawTextExists[0]?.exists || 
      !verifyVoiceExtractedDataExists[0]?.exists
    ) {
      throw new Error('One or more columns were not added successfully');
    }
    
    return true;
  } catch (error) {
    log(`Error during migration: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    log(`Error details: ${JSON.stringify(error, null, 2)}`, 'ERROR');
    
    if (attempt < maxAttempts) {
      log(`Will retry in 2 seconds (attempt ${attempt + 1} of ${maxAttempts})`, 'WARN');
      await new Promise(resolve => setTimeout(resolve, 2000));
      return runMigration(attempt + 1, maxAttempts);
    }
    
    return false;
  } finally {
    if (sql) {
      log('Closing database connection');
      await sql.end();
      log('Database connection closed');
    }
  }
};

log('Starting migration: add-voice-transcription-fields');
runMigration()
  .then((success) => {
    if (success) {
      log('Migration completed successfully');
    } else {
      log('Migration failed after all attempts', 'ERROR');
      process.exit(1);
    }
  })
  .catch((error) => {
    log(`Migration failed with uncaught error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'ERROR');
    process.exit(1);
  }); 