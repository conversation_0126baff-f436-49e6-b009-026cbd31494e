import { sql } from 'drizzle-orm';
import { pgTable, serial, text, timestamp, integer, boolean, json, varchar } from 'drizzle-orm/pg-core';

// User roles enum
export const UserRole = {
  ADMIN: 'admin',
  ORGANIZER: 'organizer',
  CONTRIBUTOR: 'contributor'
} as const;

// Users table
export const users = pgTable('users', {
  id: serial('id').primaryKey(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  name: text('name').notNull(),
  role: varchar('role', { length: 20 }).notNull().default(UserRole.CONTRIBUTOR),
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// User profiles for additional information
export const userProfiles = pgTable('user_profiles', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  phone: text('phone'),
  address: json('address'),
  preferences: json('preferences'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Projects table (Recipe Books)
export const projects = pgTable('projects', {
  id: serial('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  organizerId: integer('organizer_id').references(() => users.id),
  theme: text('theme').default('default'),
  coverImage: text('cover_image'),
  status: text('status').default('draft'), // 'draft', 'in_progress', 'completed'
  maxContributors: integer('max_contributors').notNull(),
  deadline: timestamp('deadline'),
  pricingTier: text('pricing_tier'), // For tier-based pricing
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Project Contributors table (many-to-many relationship)
export const projectContributors = pgTable('project_contributors', {
  id: serial('id').primaryKey(),
  projectId: integer('project_id').references(() => projects.id),
  userId: integer('user_id').references(() => users.id),
  status: text('status').default('pending'), // 'pending', 'accepted', 'rejected'
  role: text('role').default('contributor'), // 'contributor', 'reviewer', etc.
  joinedAt: timestamp('joined_at').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Recipes table
export const recipes = pgTable('recipes', {
  id: serial('id').primaryKey(),
  projectId: integer('project_id').references(() => projects.id),
  contributorId: integer('contributor_id').references(() => users.id),
  title: text('title').notNull(),
  description: text('description'),
  ingredients: json('ingredients').notNull(), // Array of { name, amount, unit }
  instructions: json('instructions').notNull(), // Array of steps
  prepTime: integer('prep_time'),
  cookTime: integer('cook_time'),
  servings: integer('servings'),
  difficulty: text('difficulty'), // 'easy', 'medium', 'hard'
  tags: json('tags'), // Array of strings
  images: json('images'), // Array of image URLs
  status: text('status').default('draft'), // 'draft', 'submitted', 'approved'
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}); 