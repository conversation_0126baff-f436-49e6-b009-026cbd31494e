import { sql } from 'drizzle-orm';
import { pgTable, serial, integer, text, timestamp, json } from 'drizzle-orm/pg-core';
import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export async function up(db: any) {
  console.log('Starting migration: Creating recipes table...');
  
  try {
    console.log('Checking database connection...');
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL is not set in environment variables');
    }
    console.log('Database URL found.');

    console.log('Executing CREATE TABLE statement...');
    await db.unsafe(`
      CREATE TABLE IF NOT EXISTS recipes (
        id SERIAL PRIMARY KEY,
        project_id INTEGER REFERENCES projects(id),
        contributor_id INTEGER REFERENCES users(id),
        title TEXT NOT NULL,
        description TEXT,
        category TEXT,
        ingredients JSONB NOT NULL,
        measurement_system TEXT DEFAULT 'us',
        instructions JSONB NOT NULL,
        tags JSONB,
        images JSONB DEFAULT '[]'::jsonb,
        role TEXT DEFAULT 'contributor',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('CREATE TABLE statement executed successfully.');

    // Verify the table was created
    console.log('Verifying table creation...');
    const result = await db.unsafe(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'recipes'
      );
    `);
    
    if (result[0].exists) {
      console.log('Table verification successful: recipes table exists.');
    } else {
      throw new Error('Table verification failed: recipes table was not created.');
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed with error:', error);
    throw error;
  }
}

export async function down(db: any) {
  console.log('Starting rollback: Dropping recipes table...');
  
  try {
    console.log('Executing DROP TABLE statement...');
    await db.unsafe(`
      DROP TABLE IF EXISTS recipes;
    `);
    console.log('DROP TABLE statement executed successfully.');

    // Verify the table was dropped
    console.log('Verifying table deletion...');
    const result = await db.unsafe(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'recipes'
      );
    `);
    
    if (!result[0].exists) {
      console.log('Table verification successful: recipes table no longer exists.');
    } else {
      throw new Error('Table verification failed: recipes table still exists.');
    }

    console.log('Rollback completed successfully!');
  } catch (error) {
    console.error('Rollback failed with error:', error);
    throw error;
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  console.log('Running migration script directly...');
  const sql = postgres(process.env.DATABASE_URL!, { max: 1 });
  
  up(sql)
    .then(() => {
      console.log('Migration script completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    })
    .finally(() => {
      sql.end();
    });
} 