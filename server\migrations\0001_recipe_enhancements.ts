import { sql } from 'drizzle-orm';
import { pgTable, serial, integer, text, timestamp, json } from 'drizzle-orm/pg-core';

export async function up(db: any) {
  await db.execute(sql`
    ALTER TABLE recipes
    ADD COLUMN IF NOT EXISTS category text,
    ADD COLUMN IF NOT EXISTS measurement_system text DEFAULT 'us',
    ADD COLUMN IF NOT EXISTS images jsonb DEFAULT '[]'::jsonb,
    ADD COLUMN IF NOT EXISTS role text DEFAULT 'contributor';
  `);
}

export async function down(db: any) {
  await db.execute(sql`
    ALTER TABLE recipes
    DROP COLUMN IF EXISTS category,
    DROP COLUMN IF EXISTS measurement_system,
    DROP COLUMN IF EXISTS images,
    DROP COLUMN IF EXISTS role;
  `);
} 