import { sql } from 'drizzle-orm';
import { db } from '../db.js';

export async function addRecipeSubmissionTracking() {
  console.log('Starting recipe submission tracking migration...');

  // Create an index on status for faster queries
  await db.execute(sql`
    CREATE INDEX IF NOT EXISTS idx_recipes_status
    ON recipes(status);
  `);

  // Create an index on contributor_id and project_id for faster contributor queries
  await db.execute(sql`
    CREATE INDEX IF NOT EXISTS idx_recipes_contributor_project
    ON recipes(contributor_id, project_id);
  `);

  // Add constraint to validate status values if not exists
  await db.execute(sql`
    DO $$
    BEGIN
      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'recipes_status_check'
      ) THEN
        ALTER TABLE recipes
        ADD CONSTRAINT recipes_status_check
        CHECK (status IN ('pending', 'approved', 'rejected'));
      END IF;
    END $$;
  `);

  console.log('Recipe submission tracking migration completed successfully');
}

// Run migration if this file is executed directly
if (require.main === module) {
  addRecipeSubmissionTracking()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
