CREATE TABLE "projects" (
  "id" serial PRIMARY KEY NOT NULL,
  "name" text NOT NULL,
  "description" text,
  "organizer_id" integer,
  "theme" text DEFAULT 'default',
  "cover_image" text,
  "status" text DEFAULT 'draft',
  "role" text DEFAULT 'organizer',
  "max_contributors" integer NOT NULL,
  "deadline" timestamp,
  "pricing_tier" text,
  "created_at" timestamp DEFAULT now(),
  "updated_at" timestamp DEFAULT now()
);

ALTER TABLE "projects" ADD CONSTRAINT "projects_organizer_id_users_id_fk" FOREIGN KEY ("organizer_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action; 