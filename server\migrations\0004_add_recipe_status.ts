import { sql } from 'drizzle-orm';
import { db } from '../db.js';

export async function updateRecipeStatus() {
  await db.execute(sql`
    DO $$
    BEGIN
      IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'recipes'
        AND column_name = 'status'
      ) THEN
        ALTER TABLE recipes
        ADD COLUMN status TEXT NOT NULL DEFAULT 'pending'
        CHECK (status IN ('pending', 'approved', 'rejected'));
      END IF;
    END $$;
  `);
}

// Run migration if this file is executed directly
if (require.main === module) {
  updateRecipeStatus()
    .then(() => {
      console.log('Recipe status migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
