-- Drop the table if it exists
DROP TABLE IF EXISTS "project_contributors";

-- Create the project_contributors table
CREATE TABLE "project_contributors" (
  "id" serial PRIMARY KEY NOT NULL,
  "project_id" integer,
  "user_id" integer,
  "status" text DEFAULT 'pending',
  "role" text DEFAULT 'contributor',
  "invitation_token" text,
  "invitation_sent_at" timestamp,
  "invitation_accepted_at" timestamp,
  "invitation_expires_at" timestamp,
  "joined_at" timestamp DEFAULT now(),
  "created_at" timestamp DEFAULT now(),
  "updated_at" timestamp DEFAULT now()
);

-- Add foreign key constraints
ALTER TABLE "project_contributors" 
ADD CONSTRAINT "project_contributors_project_id_projects_id_fk" 
FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "project_contributors" 
ADD CONSTRAINT "project_contributors_user_id_users_id_fk" 
FOREIG<PERSON> KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add check constraint for status values
ALTER TABLE "project_contributors" 
ADD CONSTRAINT "project_contributors_status_check" 
CHECK (status IN ('pending', 'accepted', 'rejected', 'removed'));

-- Add indexes for better performance
CREATE INDEX "project_contributors_invitation_token_idx" ON "project_contributors" ("invitation_token");
CREATE INDEX "project_contributors_status_idx" ON "project_contributors" ("status"); 