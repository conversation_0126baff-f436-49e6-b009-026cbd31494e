import { sql } from 'drizzle-orm';
import { db } from '../db.js';

async function logMigrationStep(step: string, details?: any) {
    console.log(`[${new Date().toISOString()}] ${step}`);
    if (details) {
        console.log('Details:', details);
    }
}

async function checkColumnExists() {
    const result = await db.execute(sql`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'recipes'
        AND column_name = 'status';
    `);
    return result.length > 0;
}

export async function updateRecipeStatusWithLogging() {
    try {
        await logMigrationStep('Starting migration: Add status column to recipes table');

        // Check if column exists
        const columnExists = await checkColumnExists();
        await logMigrationStep('Checked if column exists', { columnExists });

        if (!columnExists) {
            await logMigrationStep('Adding status column to recipes table');

            // Add the column
            await db.execute(sql`
                ALTER TABLE recipes
                ADD COLUMN status TEXT NOT NULL DEFAULT 'pending';
            `);
            await logMigrationStep('Added status column');

            // Add the check constraint
            await db.execute(sql`
                ALTER TABLE recipes
                ADD CONSTRAINT recipes_status_check
                CHECK (status IN ('pending', 'approved', 'rejected'));
            `);
            await logMigrationStep('Added status check constraint');

            // Verify the column was added correctly
            const verificationResult = await checkColumnExists();
            await logMigrationStep('Verified column exists', { verificationResult });
        } else {
            await logMigrationStep('Status column already exists, skipping migration');
        }

        // Log current table structure
        const tableStructure = await db.execute(sql`
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'recipes';
        `);
        await logMigrationStep('Current table structure:', tableStructure);

        await logMigrationStep('Migration completed successfully');
    } catch (error) {
        await logMigrationStep('Migration failed', { error });
        throw error;
    }
}

// Run migration if this file is executed directly
if (require.main === module) {
    updateRecipeStatusWithLogging()
        .then(() => {
            console.log('Recipe status migration completed successfully');
            process.exit(0);
        })
        .catch((error) => {
            console.error('Migration failed:', error);
            process.exit(1);
        });
}
