import { sql } from 'drizzle-orm';
import postgres from 'postgres';
import { drizzle } from 'drizzle-orm/postgres-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function runMigration() {
    console.log('Starting migration process...');

    if (!process.env.DATABASE_URL) {
        console.error('DATABASE_URL is not defined in environment variables');
        process.exit(1);
    }

    // Create the connection
    console.log('Connecting to database...');
    const client = postgres(process.env.DATABASE_URL);
    const db = drizzle(client);

    try {
        console.log('Checking if status column exists...');
        const columnCheck = await client`
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'recipes'
            AND column_name = 'status';
        `;

        console.log('Column check result:', columnCheck);

        if (columnCheck.length === 0) {
            console.log('Status column does not exist. Adding it...');

            await client`
                ALTER TABLE recipes
                ADD COLUMN status TEXT NOT NULL DEFAULT 'pending';
            `;
            console.log('Added status column');

            await client`
                ALTER TABLE recipes
                ADD CONSTRAINT recipes_status_check
                CHECK (status IN ('pending', 'approved', 'rejected'));
            `;
            console.log('Added status check constraint');
        } else {
            console.log('Status column already exists');
        }

        // Verify final table structure
        const tableStructure = await client`
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'recipes';
        `;
        console.log('Final table structure:', tableStructure);

        console.log('Migration completed successfully');
    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    } finally {
        await client.end();
        console.log('Database connection closed');
    }
}

// Run migration
runMigration()
    .then(() => {
        console.log('Migration script completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Migration script failed:', error);
        process.exit(1);
    });
