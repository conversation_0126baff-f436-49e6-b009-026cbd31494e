CREATE TABLE "recipes" (
  "id" serial PRIMARY KEY NOT NULL,
  "project_id" integer REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE,
  "contributor_id" integer REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE,
  "title" text NOT NULL,
  "description" text,
  "ingredients" json NOT NULL,
  "instructions" json NOT NULL,
  "prep_time" integer,
  "cook_time" integer,
  "servings" integer,
  "difficulty" text,
  "tags" json,
  "images" json,
  "status" text DEFAULT 'draft',
  "role" text DEFAULT 'contributor',
  "created_at" timestamp DEFAULT now(),
  "updated_at" timestamp DEFAULT now()
); 

ALTER TABLE "recipes" ADD CONSTRAINT "recipes_project_id_projects_id_fk" FOREIGN KEY ("project_id") REFERENCES "public"."projects"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "recipes" ADD CONSTRAINT "recipes_contributor_id_users_id_fk" FOREIGN KEY ("contributor_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action; 