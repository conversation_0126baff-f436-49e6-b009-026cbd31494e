import { sql } from 'drizzle-orm';
import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function runMigration() {
    console.log('Starting migration process...');
    
    if (!process.env.DATABASE_URL) {
        console.error('DATABASE_URL is not defined in environment variables');
        process.exit(1);
    }

    // Create the connection
    console.log('Connecting to database...');
    const client = postgres(process.env.DATABASE_URL);

    try {
        console.log('Adding deadline-related columns to project_contributors table...');
        
        // Add deadline column
        await client`
            ALTER TABLE project_contributors 
            ADD COLUMN IF NOT EXISTS deadline TIMESTAMP;
        `;
        console.log('Added deadline column');

        // Add last_reminder_sent_at column
        await client`
            ALTER TABLE project_contributors 
            ADD COLUMN IF NOT EXISTS last_reminder_sent_at TIMESTAMP;
        `;
        console.log('Added last_reminder_sent_at column');

        // Add reminder_frequency column with default value
        await client`
            ALTER TABLE project_contributors 
            ADD COLUMN IF NOT EXISTS reminder_frequency TEXT DEFAULT 'weekly';
        `;
        console.log('Added reminder_frequency column');

        // Add check constraint for reminder_frequency
        await client`
            ALTER TABLE project_contributors 
            ADD CONSTRAINT project_contributors_reminder_frequency_check 
            CHECK (reminder_frequency IN ('5min', '15min', '30min', '1hour', 'daily', 'weekly', 'biweekly', 'monthly'));
        `;
        console.log('Added reminder_frequency check constraint');

        // Create index on deadline column
        await client`
            CREATE INDEX IF NOT EXISTS project_contributors_deadline_idx 
            ON project_contributors (deadline);
        `;
        console.log('Created index on deadline column');

        console.log('Migration completed successfully');
    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    } finally {
        await client.end();
    }
}

runMigration().catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
}); 