import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addRecipeStatus() {
    if (!process.env.DATABASE_URL) {
        console.error('DATABASE_URL is not defined');
        process.exit(1);
    }

    console.log('Connecting to database...');
    const sql = postgres(process.env.DATABASE_URL, {
        ssl: {
            rejectUnauthorized: false
        }
    });

    try {
        // Check if column exists
        console.log('Checking if status column exists...');
        const columns = await sql`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'recipes' 
            AND column_name = 'status';
        `;
        
        if (columns.length === 0) {
            console.log('Adding status column...');
            await sql`
                ALTER TABLE recipes 
                ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'pending';
            `;
            
            console.log('Adding status check constraint...');
            await sql`
                ALTER TABLE recipes 
                ADD CONSTRAINT IF NOT EXISTS recipes_status_check 
                CHECK (status IN ('pending', 'approved', 'rejected'));
            `;
            
            console.log('Status column added successfully');
        } else {
            console.log('Status column already exists');
        }

        // Verify the structure
        console.log('\nVerifying table structure...');
        const structure = await sql`
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'recipes'
            ORDER BY ordinal_position;
        `;
        
        console.log('\nCurrent table structure:');
        console.table(structure);

    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    } finally {
        await sql.end();
        console.log('Database connection closed');
    }
}

// Run migration
addRecipeStatus()
    .then(() => {
        console.log('Migration completed successfully');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Migration failed:', error);
        process.exit(1);
    });
