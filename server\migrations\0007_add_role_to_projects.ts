import postgres from 'postgres';
import { join } from 'path';
import { config } from 'dotenv';

// Load environment variables
config();

const runMigration = async () => {
  if (!process.env.DATABASE_URL) {
    throw new Error('DATABASE_URL is not set in environment variables');
  }

  const sql = postgres(process.env.DATABASE_URL, { max: 1 });

  try {
    console.log('Running projects table migration to add role field...');
    
    // Add role column to projects table
    await sql.unsafe(`
      ALTER TABLE "projects" 
      ADD COLUMN IF NOT EXISTS "role" text DEFAULT 'organizer';
    `);
    
    // Update existing projects to have role set to 'organizer'
    await sql.unsafe(`
      UPDATE "projects" 
      SET "role" = 'organizer' 
      WHERE "role" IS NULL;
    `);
    
    console.log('Projects table migration completed successfully');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
};

// Run the migration
runMigration(); 