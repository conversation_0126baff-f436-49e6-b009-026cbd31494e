import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addRecipeStatus() {
    const { DATABASE_URL } = process.env;
    
    if (!DATABASE_URL) {
        throw new Error('DATABASE_URL environment variable is not set');
    }

    console.log('Starting migration...');
    
    // Configure postgres with SSL
    const connectionString = DATABASE_URL;
    const client = postgres(connectionString, {
        ssl: {
            rejectUnauthorized: true,
            requestCert: true
        },
        max: 1
    });

    try {
        console.log('Connected to database. Checking table structure...');
        
        // Check if the column exists
        const columnExists = await client`
            SELECT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'recipes'
                AND column_name = 'status'
            );
        `;

        console.log('Column check result:', columnExists);

        if (!columnExists[0]?.exists) {
            console.log('Status column does not exist. Adding it...');
            
            // Add the column
            await client`
                ALTER TABLE recipes 
                ADD COLUMN status TEXT NOT NULL DEFAULT 'pending';
            `;
            console.log('Status column added');

            // Add the constraint
            await client`
                ALTER TABLE recipes 
                ADD CONSTRAINT recipes_status_check 
                CHECK (status IN ('pending', 'approved', 'rejected'));
            `;
            console.log('Status constraint added');
        } else {
            console.log('Status column already exists');
        }

        // Verify the current structure
        const tableStructure = await client`
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'recipes'
            ORDER BY ordinal_position;
        `;

        console.log('\nCurrent table structure:');
        console.table(tableStructure);

        console.log('Migration completed successfully');
    } catch (error) {
        console.error('Error during migration:', error);
        throw error;
    } finally {
        console.log('Closing database connection...');
        await client.end();
        console.log('Database connection closed');
    }
}

// Run the migration
addRecipeStatus()
    .then(() => {
        console.log('Migration process completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Migration process failed:', error);
        process.exit(1);
    });
