import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import dotenv from 'dotenv';

dotenv.config();

const { DATABASE_URL } = process.env;

if (!DATABASE_URL) {
    console.error('DATABASE_URL is missing');
    process.exit(1);
}

console.log('Starting migration...');
const sql = postgres(DATABASE_URL);

async function runMigration() {
    try {
        // Check if column exists
        const check = await sql`
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'recipes' AND column_name = 'status'
            );
        `;
        
        if (!check[0].exists) {
            console.log('Adding status column...');
            await sql`
                ALTER TABLE recipes 
                ADD COLUMN status TEXT NOT NULL DEFAULT 'pending';
            `;
            
            await sql`
                ALTER TABLE recipes 
                ADD CONSTRAINT recipes_status_check 
                CHECK (status IN ('pending', 'approved', 'rejected'));
            `;
            console.log('Status column and constraint added successfully');
        } else {
            console.log('Status column already exists');
        }
        
        // Verify the column was added
        const columns = await sql`
            SELECT column_name, data_type, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'recipes' AND column_name = 'status';
        `;
        
        console.log('Column verification:', columns);
        
    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    } finally {
        await sql.end();
    }
}

runMigration();
