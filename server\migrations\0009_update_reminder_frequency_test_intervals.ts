import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function runMigration() {
    console.log('Starting migration process...');
    
    if (!process.env.DATABASE_URL) {
        console.error('DATABASE_URL is not defined in environment variables');
        process.exit(1);
    }

    // Create the connection
    console.log('Connecting to database...');
    const client = postgres(process.env.DATABASE_URL);

    try {
        console.log('Checking current constraint...');
        const currentConstraint = await client`
            SELECT con.conname, pg_get_constraintdef(con.oid) as constraint_def
            FROM pg_constraint con
            INNER JOIN pg_class rel ON rel.oid = con.conrelid
            INNER JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
            WHERE rel.relname = 'project_contributors'
            AND con.conname = 'project_contributors_reminder_frequency_check';
        `;
        console.log('Current constraint:', currentConstraint);

        console.log('Dropping existing constraint...');
        await client`
            ALTER TABLE project_contributors 
            DROP CONSTRAINT IF EXISTS project_contributors_reminder_frequency_check;
        `;
        console.log('Existing constraint dropped');

        console.log('Adding new constraint with test intervals...');
        await client`
            ALTER TABLE project_contributors 
            ADD CONSTRAINT project_contributors_reminder_frequency_check 
            CHECK (reminder_frequency IN ('1min', '2min', '5min', '15min', '30min', '1hour', 'daily', 'weekly', 'biweekly', 'monthly'));
        `;
        console.log('New constraint added');

        // Verify the new constraint
        console.log('Verifying new constraint...');
        const newConstraint = await client`
            SELECT con.conname, pg_get_constraintdef(con.oid) as constraint_def
            FROM pg_constraint con
            INNER JOIN pg_class rel ON rel.oid = con.conrelid
            INNER JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
            WHERE rel.relname = 'project_contributors'
            AND con.conname = 'project_contributors_reminder_frequency_check';
        `;
        console.log('New constraint:', newConstraint);

        console.log('Migration completed successfully');
    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    } finally {
        await client.end();
    }
}

runMigration().catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
}); 