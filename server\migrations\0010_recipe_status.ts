import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import dotenv from 'dotenv';

async function main() {
    console.log('Loading environment variables...');
    dotenv.config();

    const { DATABASE_URL } = process.env;

    if (!DATABASE_URL) {
        console.error('DATABASE_URL is missing');
        process.exit(1);
    }

    console.log('Initializing database connection...');
    const sql = postgres(DATABASE_URL, { ssl: { rejectUnauthorized: false } });

    try {
        console.log('Checking for existing status column...');
        const check = await sql`
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'recipes' AND column_name = 'status'
            );
        `;
        
        console.log('Check result:', check);
        
        if (!check[0].exists) {
            console.log('Status column does not exist, adding it...');
            await sql`
                ALTER TABLE recipes 
                ADD COLUMN status TEXT NOT NULL DEFAULT 'pending';
            `;
            console.log('Status column added');
            
            await sql`
                ALTER TABLE recipes 
                ADD CONSTRAINT recipes_status_check 
                CHECK (status IN ('pending', 'approved', 'rejected'));
            `;
            console.log('Status constraint added');
        } else {
            console.log('Status column already exists');
        }
        
        console.log('Verifying column...');
        const columns = await sql`
            SELECT column_name, data_type, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'recipes' AND column_name = 'status';
        `;
        
        console.log('Verification result:', columns);
        console.log('Migration completed successfully');
        
    } catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    } finally {
        console.log('Closing database connection...');
        await sql.end();
        console.log('Done');
    }
}

main().catch(console.error);
