import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import dotenv from 'dotenv';

function log(message: string) {
    process.stdout.write(message + '\n');
}

async function main() {
    log('Loading environment variables...');
    dotenv.config();

    const { DATABASE_URL } = process.env;

    if (!DATABASE_URL) {
        log('DATABASE_URL is missing');
        process.exit(1);
    }

    log('Initializing database connection...');
    const sql = postgres(DATABASE_URL, { ssl: { rejectUnauthorized: false } });

    try {
        log('Checking for existing status column...');
        const check = await sql`
            SELECT EXISTS (
                SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'recipes' AND column_name = 'status'
            );
        `;
        
        log(`Check result: ${JSON.stringify(check)}`);
        
        if (!check[0].exists) {
            log('Status column does not exist, adding it...');
            await sql`
                ALTER TABLE recipes 
                ADD COLUMN status TEXT NOT NULL DEFAULT 'pending';
            `;
            log('Status column added');
            
            await sql`
                ALTER TABLE recipes 
                ADD CONSTRAINT recipes_status_check 
                CHECK (status IN ('pending', 'approved', 'rejected'));
            `;
            log('Status constraint added');
        } else {
            log('Status column already exists');
        }
        
        log('Verifying column...');
        const columns = await sql`
            SELECT column_name, data_type, column_default 
            FROM information_schema.columns 
            WHERE table_name = 'recipes' AND column_name = 'status';
        `;
        
        log(`Verification result: ${JSON.stringify(columns)}`);
        log('Migration completed successfully');
        
    } catch (error) {
        log(`Migration failed: ${error}`);
        process.exit(1);
    } finally {
        log('Closing database connection...');
        await sql.end();
        log('Done');
        process.exit(0);
    }
}

main().catch((error) => {
    log(`Unhandled error: ${error}`);
    process.exit(1);
});
