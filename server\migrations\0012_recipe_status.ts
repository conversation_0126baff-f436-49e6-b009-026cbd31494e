import postgres from 'postgres';
import dotenv from 'dotenv';

dotenv.config();

const sql = postgres(process.env.DATABASE_URL!, {
    ssl: { rejectUnauthorized: false }
});

sql`
    DO $$ 
    BEGIN 
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'recipes' 
            AND column_name = 'status'
        ) THEN 
            ALTER TABLE recipes 
            ADD COLUMN status TEXT NOT NULL DEFAULT 'pending';
            
            ALTER TABLE recipes 
            ADD CONSTRAINT recipes_status_check 
            CHECK (status IN ('pending', 'approved', 'rejected'));
        END IF;
    END $$;
`.then(() => {
    console.log('Migration completed');
    process.exit(0);
}).catch(err => {
    console.error('Migration failed:', err);
    process.exit(1);
});
