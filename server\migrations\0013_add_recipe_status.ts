import { sql } from 'drizzle-orm';
import { pgTable, text } from 'drizzle-orm/pg-core';
import { recipes } from '../schema.js';

export const recipeStatus = sql`
    DO $$
    BEGIN
        -- Add status column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'recipes'
            AND column_name = 'status'
        ) THEN
            ALTER TABLE recipes
            ADD COLUMN status TEXT NOT NULL DEFAULT 'pending';

            -- Add check constraint
            ALTER TABLE recipes
            ADD CONSTRAINT recipes_status_check
            CHECK (status IN ('pending', 'approved', 'rejected'));
        END IF;
    END $$;
`;
