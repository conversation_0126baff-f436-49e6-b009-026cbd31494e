// Migration to create chapters table
export async function up(client) {
  console.log('Creating chapters table if it does not exist...');
  
  try {
    // Check if table exists
    const tableExists = await client`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'chapters'
      );
    `;
    
    if (!tableExists[0]?.exists) {
      // Create chapters table
      await client`
        CREATE TABLE IF NOT EXISTS chapters (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
          title TEXT NOT NULL,
          description TEXT,
          order_index INTEGER NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      console.log('Chapters table created successfully');
      
      // Create indexes
      await client`
        CREATE INDEX IF NOT EXISTS idx_chapters_project_id ON chapters(project_id);
      `;
      
      console.log('Chapters indexes created successfully');
    } else {
      console.log('Chapters table already exists, skipping creation');
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error creating chapters table:', error);
    throw error;
  }
}

export async function down(client) {
  console.log('Dropping chapters table...');
  
  try {
    await client`
      DROP TABLE IF EXISTS chapters;
    `;
    
    console.log('Chapters table dropped successfully');
    
    return { success: true };
  } catch (error) {
    console.error('Error dropping chapters table:', error);
    throw error;
  }
}
