import { sql } from 'drizzle-orm';
import { pgTable, text, timestamp, integer, serial } from 'drizzle-orm/pg-core';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as dotenv from 'dotenv';

dotenv.config();

console.log('Starting migration: Creating recipe_comments table');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

const db = drizzle(pool);

// Define the recipe_comments table schema - not using references in the schema definition
// since we'll use direct SQL for the actual table creation
const recipeComments = pgTable('recipe_comments', {
  id: serial('id').primaryKey(),
  recipeId: integer('recipe_id').notNull(),
  userId: integer('user_id').notNull(),
  comment: text('comment').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

async function main() {
  console.log('Connected to database, beginning migration');
  
  try {
    // Create the recipe_comments table
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS recipe_comments (
        id SERIAL PRIMARY KEY,
        recipe_id INTEGER NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        comment TEXT NOT NULL,
        created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
      );
    `);
    
    console.log('Successfully created recipe_comments table');

    // Create indexes for better query performance
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS recipe_comments_recipe_id_idx ON recipe_comments(recipe_id);
    `);
    
    console.log('Successfully created index on recipe_id');
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS recipe_comments_user_id_idx ON recipe_comments(user_id);
    `);
    
    console.log('Successfully created index on user_id');
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    // End the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

main().catch((error) => {
  console.error('Migration failed:', error);
  process.exit(1);
}); 