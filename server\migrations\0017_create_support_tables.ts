import { sql } from 'drizzle-orm';
import { pgTable, text, timestamp, integer, serial, json, boolean } from 'drizzle-orm/pg-core';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import * as dotenv from 'dotenv';

dotenv.config();

console.log('Starting migration: Creating support_tickets and support_ticket_messages tables for customer support integration');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

const db = drizzle(pool);

async function main() {
  console.log('Connected to database, beginning support tables migration');

  try {
    // Check if support_tickets table already exists
    console.log('Checking if support_tickets table already exists...');
    const ticketsTableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'support_tickets'
      );
    `);

    if (!ticketsTableExists.rows[0]?.exists) {
      console.log('support_tickets table does not exist, creating...');

      // Create the support_tickets table
      await db.execute(sql`
        CREATE TABLE support_tickets (
          id SERIAL PRIMARY KEY,
          user_id INTEGER REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          external_ticket_id TEXT,
          subject TEXT NOT NULL,
          description TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'open',
          priority TEXT DEFAULT 'normal',
          category TEXT,
          assigned_to_id INTEGER REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          tags JSONB DEFAULT '[]'::jsonb,
          metadata JSONB DEFAULT '{}'::jsonb,
          created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
          resolved_at TIMESTAMP WITHOUT TIME ZONE
        );
      `);

      console.log('Successfully created support_tickets table');

      // Create indexes for better query performance
      console.log('Creating indexes for support_tickets table...');

      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS support_tickets_user_id_idx ON support_tickets(user_id);
      `);

      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS support_tickets_status_idx ON support_tickets(status);
      `);

      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS support_tickets_assigned_to_id_idx ON support_tickets(assigned_to_id);
      `);

      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS support_tickets_created_at_idx ON support_tickets(created_at);
      `);

      console.log('Successfully created indexes for support_tickets');
    } else {
      console.log('support_tickets table already exists, skipping creation');
    }

    // Check if support_ticket_messages table already exists
    console.log('Checking if support_ticket_messages table already exists...');
    const messagesTableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'support_ticket_messages'
      );
    `);

    if (!messagesTableExists.rows[0]?.exists) {
      console.log('support_ticket_messages table does not exist, creating...');

      // Create the support_ticket_messages table
      await db.execute(sql`
        CREATE TABLE support_ticket_messages (
          id SERIAL PRIMARY KEY,
          ticket_id INTEGER NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE ON UPDATE CASCADE,
          author_id INTEGER REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
          author_type TEXT NOT NULL,
          message TEXT NOT NULL,
          is_internal BOOLEAN DEFAULT false,
          attachments JSONB DEFAULT '[]'::jsonb,
          external_message_id TEXT,
          created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
        );
      `);

      console.log('Successfully created support_ticket_messages table');

      // Create indexes for better query performance
      console.log('Creating indexes for support_ticket_messages table...');

      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS support_ticket_messages_ticket_id_idx ON support_ticket_messages(ticket_id);
      `);

      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS support_ticket_messages_author_id_idx ON support_ticket_messages(author_id);
      `);

      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS support_ticket_messages_created_at_idx ON support_ticket_messages(created_at);
      `);

      console.log('Successfully created indexes for support_ticket_messages');
    } else {
      console.log('support_ticket_messages table already exists, skipping creation');
    }

    // Verify table creation
    console.log('Verifying support_tickets table structure...');
    const ticketsTableInfo = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'support_tickets'
      ORDER BY ordinal_position;
    `);

    console.log('support_tickets table structure:');
    ticketsTableInfo.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`);
    });

    console.log('Verifying support_ticket_messages table structure...');
    const messagesTableInfo = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'support_ticket_messages'
      ORDER BY ordinal_position;
    `);

    console.log('support_ticket_messages table structure:');
    messagesTableInfo.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable}, default: ${row.column_default})`);
    });

    // Verify indexes
    console.log('Verifying support_tickets indexes...');
    const ticketsIndexInfo = await db.execute(sql`
      SELECT indexname, indexdef
      FROM pg_indexes
      WHERE tablename = 'support_tickets';
    `);

    console.log('support_tickets table indexes:');
    ticketsIndexInfo.rows.forEach(row => {
      console.log(`  - ${row.indexname}: ${row.indexdef}`);
    });

    console.log('Verifying support_ticket_messages indexes...');
    const messagesIndexInfo = await db.execute(sql`
      SELECT indexname, indexdef
      FROM pg_indexes
      WHERE tablename = 'support_ticket_messages';
    `);

    console.log('support_ticket_messages table indexes:');
    messagesIndexInfo.rows.forEach(row => {
      console.log(`  - ${row.indexname}: ${row.indexdef}`);
    });

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
    throw error;
  } finally {
    // End the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

main().catch((error) => {
  console.error('Migration failed:', error);
  process.exit(1);
});
