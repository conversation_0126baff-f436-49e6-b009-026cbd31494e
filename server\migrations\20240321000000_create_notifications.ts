import { pgTable, serial, integer, text, timestamp, boolean, json } from 'drizzle-orm/pg-core';
import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('Starting notifications table migration...');

export const notifications = pgTable('notifications', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').notNull(),
  type: text('type').notNull(), // 'recipe_rejected', 'recipe_approved', etc.
  message: text('message').notNull(),
  isRead: boolean('is_read').default(false),
  metadata: json('metadata'), // Additional data specific to the notification type
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export async function up(sql: postgres.Sql<{}>) {
  console.log('Starting notifications table migration...');
  console.log('Running migration script...');
  console.log('Creating notifications table...');
  console.log('Table schema:', notifications);

  try {
    // Drop table if exists (this will also drop the sequence)
    await sql`DROP TABLE IF EXISTS notifications CASCADE;`;
    console.log('Dropped existing notifications table if it existed');
    
    // Create the table using SQL
    await sql`
      CREATE TABLE notifications (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        type TEXT NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        metadata JSONB,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `;
    
    // Verify table was created
    const verifyTable = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'notifications'
      );
    `;
    
    if (verifyTable[0].exists) {
      console.log('Successfully created notifications table');
    } else {
      throw new Error('Table creation failed - table not found after creation');
    }
  } catch (error) {
    console.error('Error creating notifications table:', error);
    throw error;
  }
}

export async function down(sql: postgres.Sql<{}>) {
  console.log('Dropping notifications table...');
  
  try {
    // Check if table exists before dropping
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'notifications'
      );
    `;
    
    if (!tableExists[0].exists) {
      console.log('Table does not exist, nothing to drop');
      return;
    }

    // Drop the table
    await sql`DROP TABLE notifications;`;
    
    // Verify table was dropped
    const verifyDrop = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'notifications'
      );
    `;
    
    if (!verifyDrop[0].exists) {
      console.log('Successfully dropped notifications table');
    } else {
      throw new Error('Table drop failed - table still exists after drop');
    }
  } catch (error) {
    console.error('Error dropping notifications table:', error);
    throw error;
  }
}

// Run the migration
console.log('Running migration script...');

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL is not defined');
}

const sql = postgres(process.env.DATABASE_URL);

up(sql)
  .then(() => {
    console.log('Migration script completed successfully.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  }); 