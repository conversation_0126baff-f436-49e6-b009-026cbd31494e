import { db } from '../db.js';
import { sql } from 'drizzle-orm';
import { recipes } from '../schema.js';
import { boolean, json } from 'drizzle-orm/pg-core';

async function main() {
  console.log('Starting migration to add OCR fields to recipes table...');
  
  try {
    // Add is_ocr_generated field
    console.log('Adding is_ocr_generated column to recipes table...');
    await db.execute(sql`
      ALTER TABLE "recipes" 
      ADD COLUMN IF NOT EXISTS "is_ocr_generated" boolean DEFAULT false
    `);
    console.log('Successfully added is_ocr_generated column.');
    
    // Add ocr_source_image field
    console.log('Adding ocr_source_image column to recipes table...');
    await db.execute(sql`
      ALTER TABLE "recipes" 
      ADD COLUMN IF NOT EXISTS "ocr_source_image" text
    `);
    console.log('Successfully added ocr_source_image column.');
    
    // Add ocr_raw_text field
    console.log('Adding ocr_raw_text column to recipes table...');
    await db.execute(sql`
      ALTER TABLE "recipes" 
      ADD COLUMN IF NOT EXISTS "ocr_raw_text" text
    `);
    console.log('Successfully added ocr_raw_text column.');
    
    // Add ocr_extracted_data field for storing the structured data extracted from OCR
    console.log('Adding ocr_extracted_data column to recipes table...');
    await db.execute(sql`
      ALTER TABLE "recipes" 
      ADD COLUMN IF NOT EXISTS "ocr_extracted_data" jsonb
    `);
    console.log('Successfully added ocr_extracted_data column.');
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed with error:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Migration error:', e);
    process.exit(1);
  })
  .finally(() => {
    console.log('Migration process completed.');
  }); 