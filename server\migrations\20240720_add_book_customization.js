/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export const up = async function(knex) {
  console.log('Starting migration: add_book_customization');

  try {
    // Check if the table exists
    const tableExists = await knex.schema.hasTable('projects');
    if (!tableExists) {
      console.error('Table "projects" does not exist');
      throw new Error('Table "projects" does not exist');
    }

    console.log('Table "projects" exists, proceeding with migration');

    // Check if any of the columns already exist to avoid duplicate column errors
    const columnInfo = await knex('projects').columnInfo();
    const columnsToAdd = [
      'theme', 'font', 'chapter_style', 'cover', 'cover_title', 'cover_subtitle',
      'cover_image', 'use_custom_cover_image', 'dedication', 'include_dedication',
      'include_quotes', 'family_quotes'
    ];

    const existingColumns = columnsToAdd.filter(column => columnInfo[column]);
    if (existingColumns.length > 0) {
      console.log(`Some columns already exist: ${existingColumns.join(', ')}`);
      console.log('Will only add missing columns');
    }

    // Alter the table
    await knex.schema.alterTable('projects', (table) => {
      console.log('Altering table "projects"');

      // Theme and styling
      if (!columnInfo['theme']) {
        console.log('Adding column: theme');
        table.string('theme').defaultTo('classic');
      }

      if (!columnInfo['font']) {
        console.log('Adding column: font');
        table.string('font').defaultTo('elegant');
      }

      if (!columnInfo['chapter_style']) {
        console.log('Adding column: chapter_style');
        table.string('chapter_style').defaultTo('simple');
      }

      // Cover customization
      if (!columnInfo['cover']) {
        console.log('Adding column: cover');
        table.string('cover').defaultTo('classic');
      }

      if (!columnInfo['cover_title']) {
        console.log('Adding column: cover_title');
        table.string('cover_title');
      }

      if (!columnInfo['cover_subtitle']) {
        console.log('Adding column: cover_subtitle');
        table.string('cover_subtitle');
      }

      if (!columnInfo['cover_image']) {
        console.log('Adding column: cover_image');
        table.text('cover_image', 'longtext'); // For storing base64 image data
      }

      if (!columnInfo['use_custom_cover_image']) {
        console.log('Adding column: use_custom_cover_image');
        table.boolean('use_custom_cover_image').defaultTo(false);
      }

      // Dedication and quotes
      if (!columnInfo['dedication']) {
        console.log('Adding column: dedication');
        table.text('dedication');
      }

      if (!columnInfo['include_dedication']) {
        console.log('Adding column: include_dedication');
        table.boolean('include_dedication').defaultTo(true);
      }

      if (!columnInfo['include_quotes']) {
        console.log('Adding column: include_quotes');
        table.boolean('include_quotes').defaultTo(true);
      }

      // Family quotes as JSON array
      if (!columnInfo['family_quotes']) {
        console.log('Adding column: family_quotes');
        table.json('family_quotes');
      }
    });

    console.log('Migration completed successfully');
  } catch (error) {
    console.error(`Migration failed: ${error.message}`);
    console.error(`Stack trace: ${error.stack}`);
    throw error; // Re-throw to allow the runner to handle retries
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export const down = async function(knex) {
  console.log('Starting rollback: add_book_customization');

  try {
    // Check if the table exists
    const tableExists = await knex.schema.hasTable('projects');
    if (!tableExists) {
      console.error('Table "projects" does not exist, nothing to roll back');
      return;
    }

    console.log('Table "projects" exists, proceeding with rollback');

    // Check which columns exist before trying to drop them
    const columnInfo = await knex('projects').columnInfo();
    const columnsToRemove = [
      'theme', 'font', 'chapter_style', 'cover', 'cover_title', 'cover_subtitle',
      'cover_image', 'use_custom_cover_image', 'dedication', 'include_dedication',
      'include_quotes', 'family_quotes'
    ];

    const existingColumns = columnsToRemove.filter(column => columnInfo[column]);
    console.log(`Columns to remove: ${existingColumns.join(', ')}`);

    if (existingColumns.length === 0) {
      console.log('No columns to remove, rollback complete');
      return;
    }

    // Alter the table to drop columns
    await knex.schema.alterTable('projects', (table) => {
      console.log('Altering table "projects" to drop columns');

      // Drop columns one by one, checking if they exist
      existingColumns.forEach(column => {
        console.log(`Dropping column: ${column}`);
        table.dropColumn(column);
      });
    });

    console.log('Rollback completed successfully');
  } catch (error) {
    console.error(`Rollback failed: ${error.message}`);
    console.error(`Stack trace: ${error.stack}`);
    throw error;
  }
};
