import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { sql } from 'drizzle-orm';

// Detailed logging function
const log = (message: string) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
};

async function main() {
  log('Starting migration: add-voice-transcription-fields');
  
  // Initialize Postgres client
  const connectionString = process.env.DATABASE_URL;
  
  if (!connectionString) {
    log('ERROR: DATABASE_URL environment variable is not set');
    process.exit(1);
  }
  
  log(`Connecting to database: ${connectionString.split('@')[1] || 'DB'}`);
  
  const client = postgres(connectionString, { max: 1 });
  const db = drizzle(client);
  
  log('Database connection established successfully');
  
  try {
    // Add new fields to the recipes table
    log('Adding voice transcription fields to recipes table');
    await db.execute(sql`
      ALTER TABLE recipes 
      ADD COLUMN IF NOT EXISTS is_voice_transcribed BOOLEAN DEFAULT FALSE,
      ADD COLUMN IF NOT EXISTS voice_source_audio TEXT,
      ADD COLUMN IF NOT EXISTS voice_raw_text TEXT,
      ADD COLUMN IF NOT EXISTS voice_extracted_data JSONB
    `);
    
    log('Fields added successfully');
    log('Migration completed successfully');
  } catch (error) {
    log(`ERROR during migration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    log(`Error details: ${JSON.stringify(error)}`);
    throw error;
  } finally {
    await client.end();
    log('Database connection closed');
  }
}

// Execute the migration
main().catch((error) => {
  log(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  process.exit(1);
}); 