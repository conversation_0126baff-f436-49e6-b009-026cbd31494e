import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function checkTableStructure() {
    if (!process.env.DATABASE_URL) {
        console.error('DATABASE_URL is not defined');
        process.exit(1);
    }

    console.log('Connecting to database...');
    const sql = postgres(process.env.DATABASE_URL, { ssl: true });

    try {
        console.log('Checking recipes table structure...');
        const result = await sql`
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'recipes'
            ORDER BY ordinal_position;
        `;

        console.log('\nRecipes Table Structure:');
        console.table(result);

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await sql.end();
        console.log('Connection closed');
    }
}

checkTableStructure();
