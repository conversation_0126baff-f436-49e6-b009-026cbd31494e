{"id": "e56a2abb-78a6-4ab2-bef1-f27cb12731ad", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.project_contributors": {"name": "project_contributors", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "default": "'contributor'"}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_contributors_project_id_projects_id_fk": {"name": "project_contributors_project_id_projects_id_fk", "tableFrom": "project_contributors", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_contributors_user_id_users_id_fk": {"name": "project_contributors_user_id_users_id_fk", "tableFrom": "project_contributors", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "organizer_id": {"name": "organizer_id", "type": "integer", "primaryKey": false, "notNull": false}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": false, "default": "'default'"}, "cover_image": {"name": "cover_image", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "max_contributors": {"name": "max_contributors", "type": "integer", "primaryKey": false, "notNull": true}, "deadline": {"name": "deadline", "type": "timestamp", "primaryKey": false, "notNull": false}, "pricing_tier": {"name": "pricing_tier", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"projects_organizer_id_users_id_fk": {"name": "projects_organizer_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["organizer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recipes": {"name": "recipes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "project_id": {"name": "project_id", "type": "integer", "primaryKey": false, "notNull": false}, "contributor_id": {"name": "contributor_id", "type": "integer", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "ingredients": {"name": "ingredients", "type": "json", "primaryKey": false, "notNull": true}, "instructions": {"name": "instructions", "type": "json", "primaryKey": false, "notNull": true}, "prep_time": {"name": "prep_time", "type": "integer", "primaryKey": false, "notNull": false}, "cook_time": {"name": "cook_time", "type": "integer", "primaryKey": false, "notNull": false}, "servings": {"name": "servings", "type": "integer", "primaryKey": false, "notNull": false}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "json", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"recipes_project_id_projects_id_fk": {"name": "recipes_project_id_projects_id_fk", "tableFrom": "recipes", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "recipes_contributor_id_users_id_fk": {"name": "recipes_contributor_id_users_id_fk", "tableFrom": "recipes", "tableTo": "users", "columnsFrom": ["contributor_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "json", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_profiles_user_id_users_id_fk": {"name": "user_profiles_user_id_users_id_fk", "tableFrom": "user_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'contributor'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}