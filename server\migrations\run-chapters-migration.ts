import postgres from 'postgres';
import { up } from './0015_create_chapters.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('Connecting to database...');

const client = postgres(process.env.DATABASE_URL!);

console.log('Starting chapters migration...');

up(client)
  .then(() => {
    console.log('Chapters migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Chapters migration failed:', error);
    process.exit(1);
  }); 