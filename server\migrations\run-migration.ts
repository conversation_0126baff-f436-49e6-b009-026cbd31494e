import postgres from 'postgres';
import { up } from './20240321000000_create_notifications.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('Connecting to database...');

const client = postgres(process.env.DATABASE_URL!);

console.log('Starting migration...');

up(client)
  .then(() => {
    console.log('Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    process.exit(1);
  }); 