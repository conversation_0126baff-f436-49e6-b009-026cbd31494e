{"name": "server", "version": "1.0.0", "description": "backend of storyworth", "main": "index.ts", "type": "module", "scripts": {"dev": "tsx watch index.ts", "build": "npm install && tsc", "start": "cross-env NODE_ENV=production node dist/index.js", "start:dev": "tsx index.ts", "configure-s3": "tsx scripts/configure-s3-cors.ts", "migrate": "drizzle-kit push:pg"}, "author": "", "license": "UNLICENSED", "dependencies": {"@aws-sdk/client-s3": "^3.798.0", "@aws-sdk/s3-request-presigner": "^3.798.0", "@google-cloud/vision": "^5.1.0", "@react-pdf/renderer": "^4.3.0", "@sendgrid/mail": "^8.1.5", "@types/node-cron": "^3.0.11", "@types/pg": "^8.11.14", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "node-cron": "^3.0.3", "openai": "^4.98.0", "path-to-regexp": "^6.2.1", "pg": "^8.15.6", "postgres": "^3.4.5", "puppeteer": "^24.9.0", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.0.0", "drizzle-kit": "^0.30.4", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.7.1", "typescript": "^5.3.3"}}