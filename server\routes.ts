import type { Express } from "express";
import { createServer, type Server } from "http";
import storage from "./storage.js";

export async function registerRoutes(app: Express): Promise<Server> {
  // Simple API endpoint for health check
  app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', message: 'Recipe Cookbook API is running' });
  });

  const httpServer = createServer(app);

  return httpServer;
}
