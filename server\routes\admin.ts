import { Router, Request, Response, NextFunction } from 'express';
import { db } from '../db.js';
import { users, projects, projectContributors, recipes, userProfiles, notifications } from '../schema.js';
import { eq, and, desc } from 'drizzle-orm';
import { isAdmin, canManageAdmins } from '../middleware/admin.js';
import { authMiddleware } from '../middleware/auth.js';
import crypto from 'crypto';
import { sendInviteEmail } from '../services/email.js';
import { sendEmail } from '../services/email.js';
import { deleteObject } from '../services/s3.js';

interface AuthRequest extends Request {
  user?: {
    id: number;
    email: string;
    role: string;
  };
}

const router = Router();

// Get all users (admin only)
router.get('/users', authMiddleware, isAdmin, async (req, res) => {
  try {
    const allUsers = await db.query.users.findMany({
      columns: {
        password: false // Exclude password from response
      }
    });
    res.json({ users: allUsers });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Update user role (admin only)
router.patch('/users/:userId/role', authMiddleware, canManageAdmins, async (req, res) => {
  try {
    const { userId } = req.params;
    const { role } = req.body;

    // Validate role
    if (!['admin', 'organizer', 'contributor'].includes(role)) {
      return res.status(400).json({ message: 'Invalid role' });
    }

    // Update user role
    const [updatedUser] = await db.update(users)
      .set({ role })
      .where(eq(users.id, parseInt(userId)))
      .returning({
        id: users.id,
        name: users.name,
        email: users.email,
        role: users.role
      });

    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ user: updatedUser });
  } catch (error) {
    console.error('Error updating user role:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Delete user (admin only)
router.delete('/users/:userId', authMiddleware, isAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const userIdNum = parseInt(userId);
    const { force } = req.query; // Add force parameter to allow forced deletion

    // Verify user exists
    const user = await db.query.users.findFirst({
      where: eq(users.id, userIdNum)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check for associated recipes and projects if not force deleting
    if (!force) {
      const userRecipes = await db.query.recipes.findMany({
        where: eq(recipes.contributorId, userIdNum)
      });

      const userProjects = await db.query.projects.findMany({
        where: eq(projects.organizerId, userIdNum)
      });

      if (userRecipes.length > 0 || userProjects.length > 0) {
        return res.status(409).json({
          message: 'Cannot delete user with associated data',
          details: {
            hasRecipes: userRecipes.length > 0,
            recipeCount: userRecipes.length,
            hasProjects: userProjects.length > 0,
            projectCount: userProjects.length
          }
        });
      }
    }

    // Start a transaction to delete all related data
    await db.transaction(async (tx) => {
      // Delete user's profile first
      await tx.delete(userProfiles).where(eq(userProfiles.userId, userIdNum));

      // Delete user's recipes
      await tx.delete(recipes).where(eq(recipes.contributorId, userIdNum));

      // Delete user's project contributions
      await tx.delete(projectContributors).where(eq(projectContributors.userId, userIdNum));

      // Delete user's projects
      await tx.delete(projects).where(eq(projects.organizerId, userIdNum));

      // Finally, delete the user
      const [deletedUser] = await tx.delete(users)
        .where(eq(users.id, userIdNum))
        .returning({
          id: users.id,
          name: users.name,
          email: users.email,
          role: users.role
        });

      if (!deletedUser) {
        throw new Error('Failed to delete user');
      }

      return deletedUser;
    });

    res.json({ message: 'User and associated data deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    if (error instanceof Error) {
      return res.status(500).json({ message: error.message });
    }
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get all projects (admin only)
router.get('/projects', authMiddleware, isAdmin, async (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const allProjects = await db.query.projects.findMany({
      with: {
        organizer: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        },
        contributors: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    // Process projects to ensure no duplicate contributors
    const processedProjects = allProjects.map(project => {
      // Create a map to track unique contributors by user ID
      const uniqueContributors = new Map();

      // Add each contributor to the map, using user ID as the key
      project.contributors.forEach(contributor => {
        // Skip if user is null or if user.id is undefined
        const user = contributor.user;
        if (!user?.id) return;

        const userId = user.id;
        // If this user is already in the map, only keep the most recent record
        if (uniqueContributors.has(userId)) {
          const existingContributor = uniqueContributors.get(userId);

          // Convert dates safely, defaulting to epoch start if null/invalid
          const getDate = (dateValue: Date | string | null): Date => {
            if (!dateValue) return new Date(0);
            return dateValue instanceof Date ? dateValue : new Date(dateValue);
          };

          const currentDate = getDate(contributor.updatedAt);
          const existingDate = getDate(existingContributor.updatedAt);

          if (currentDate > existingDate) {
            uniqueContributors.set(userId, contributor);
          }
        } else {
          uniqueContributors.set(userId, contributor);
        }
      });

      // Convert the map back to an array
      return {
        ...project,
        contributors: Array.from(uniqueContributors.values())
      };
    });

    res.json({ projects: processedProjects });
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get recipes for a specific project (admin only)
router.get('/projects/:projectId/recipes', authMiddleware, isAdmin, async (req, res) => {
  try {
    const { projectId } = req.params;

    // Verify project exists
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId))
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Get all recipes for the project
    const projectRecipes = await db.query.recipes.findMany({
      where: eq(recipes.projectId, parseInt(projectId)),
      columns: {
        id: true,
        projectId: true,
        contributorId: true,
        title: true,
        description: true,
        category: true,
        ingredients: true,
        instructions: true,
        measurementSystem: true,
        tags: true,
        images: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true
      },
      with: {
        contributor: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [desc(recipes.createdAt)]
    });

    res.json({ recipes: projectRecipes });
  } catch (error) {
    console.error('Error fetching project recipes:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Delete project (admin only)
router.delete('/projects/:projectId', authMiddleware, isAdmin, async (req, res) => {
  try {
    const { projectId } = req.params;
    const projectIdNum = parseInt(projectId);

    // Verify project exists
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, projectIdNum)
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Delete project's recipes
    await db.delete(recipes).where(eq(recipes.projectId, projectIdNum));

    // Delete project's contributors
    await db.delete(projectContributors).where(eq(projectContributors.projectId, projectIdNum));

    // Finally, delete the project
    const [deletedProject] = await db.delete(projects)
      .where(eq(projects.id, projectIdNum))
      .returning();

    if (!deletedProject) {
      throw new Error('Failed to delete project');
    }

    res.json({ message: 'Project and associated data deleted successfully' });
  } catch (error) {
    console.error('Error deleting project:', error);
    if (error instanceof Error) {
      return res.status(500).json({ message: error.message });
    }
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Update project role (admin only)
router.patch('/projects/:projectId/role', authMiddleware, isAdmin, async (req, res) => {
  try {
    const { projectId } = req.params;
    const { role } = req.body;

    if (!role || !['admin', 'organizer', 'contributor'].includes(role)) {
      return res.status(400).json({ message: 'Invalid role specified' });
    }

    const [updatedProject] = await db.update(projects)
      .set({
        role,
        updatedAt: new Date()
      })
      .where(eq(projects.id, parseInt(projectId)))
      .returning();

    if (!updatedProject) {
      return res.status(404).json({ message: 'Project not found' });
    }

    res.json({ project: updatedProject });
  } catch (error) {
    console.error('Error updating project role:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Invite contributor to a project (admin only)
router.post('/projects/:projectId/invite', authMiddleware, isAdmin, async (req: AuthRequest, res: Response) => {
  try {
    const { projectId } = req.params;
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Verify project exists
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId)),
      with: {
        organizer: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Find user by email
    const user = await db.query.users.findFirst({
      where: eq(users.email, email)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is already a contributor
    const existingContributor = await db.query.projectContributors.findFirst({
      where: and(
        eq(projectContributors.projectId, parseInt(projectId)),
        eq(projectContributors.userId, user.id)
      )
    });

    if (existingContributor) {
      return res.status(400).json({ message: 'User is already a contributor to this project' });
    }

    // Generate invitation token
    const invitationToken = crypto.randomBytes(32).toString('hex');
    const now = new Date();
    const invitationExpiresAt = new Date(now);
    invitationExpiresAt.setDate(invitationExpiresAt.getDate() + 7); // Expires in 7 days

    // Add user as contributor with invitation details
    const [contributor] = await db.insert(projectContributors).values({
      projectId: parseInt(projectId),
      userId: user.id,
      status: 'pending',
      invitationToken,
      invitationSentAt: now,
      invitationExpiresAt,
      createdAt: now,
      updatedAt: now
    }).returning();

    // Send invitation email
    await sendInviteEmail({
      to: user.email,
      projectName: project.name,
      invitationToken,
      deadline: invitationExpiresAt,
      reminderFrequency: 'weekly',
      projectId: parseInt(projectId)
    });

    res.status(201).json({
      contributor: {
        id: user.id,
        name: user.name,
        email: user.email,
        status: contributor.status,
        invitationToken: contributor.invitationToken,
        invitationSentAt: contributor.invitationSentAt,
        invitationExpiresAt: contributor.invitationExpiresAt
      }
    });
  } catch (error) {
    console.error('Error inviting contributor:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get a single project by ID (admin only)
router.get('/projects/:projectId', authMiddleware, isAdmin, async (req, res) => {
  try {
    const { projectId } = req.params;

    // Validate projectId is a number
    const projectIdNum = parseInt(projectId);
    if (isNaN(projectIdNum)) {
      return res.status(400).json({ message: 'Invalid project ID format' });
    }

    // Find the project by ID
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, projectIdNum),
      with: {
        organizer: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        },
        contributors: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Process contributors to remove duplicates (same as in the get all projects route)
    const uniqueContributors = new Map();
    project.contributors.forEach(contributor => {
      // Skip if user is null or if user.id is undefined
      const user = contributor.user;
      if (!user?.id) return;

      const userId = user.id;
      // If this user is already in the map, only keep the most recent record
      if (uniqueContributors.has(userId)) {
        const existingContributor = uniqueContributors.get(userId);

        // Convert dates safely, defaulting to epoch start if null/invalid
        const getDate = (dateValue: Date | string | null): Date => {
          if (!dateValue) return new Date(0);
          return dateValue instanceof Date ? dateValue : new Date(dateValue);
        };

        const currentDate = getDate(contributor.updatedAt);
        const existingDate = getDate(existingContributor.updatedAt);

        if (currentDate > existingDate) {
          uniqueContributors.set(userId, contributor);
        }
      } else {
        uniqueContributors.set(userId, contributor);
      }
    });

    // Return the processed project
    const processedProject = {
      ...project,
      contributors: Array.from(uniqueContributors.values())
    };

    res.json({ project: processedProject });
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Get all pending recipes for approval
router.get('/pending-recipes', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is admin or organizer
    const user = await db.select({ role: users.role })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user[0] || (user[0].role !== 'admin' && user[0].role !== 'organizer')) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // If user is admin, they can see all pending recipes
    // If user is organizer, they can only see recipes from their projects
    const pendingRecipes = await db.select({
      id: recipes.id,
      title: recipes.title,
      description: recipes.description,
      status: recipes.status,
      createdAt: recipes.createdAt,
      project: {
        id: projects.id,
        name: projects.name
      },
      contributor: {
        id: users.id,
        name: users.name,
        email: users.email
      }
    })
    .from(recipes)
    .leftJoin(projects, eq(recipes.projectId, projects.id))
    .leftJoin(users, eq(recipes.contributorId, users.id))
    .where(
      user[0].role === 'admin'
        ? eq(recipes.status, 'pending')
        : and(
            eq(recipes.status, 'pending'),
            eq(projects.organizerId, userId)
          )
    )
    .orderBy(desc(recipes.createdAt));

    res.json({ recipes: pendingRecipes });
  } catch (error) {
    console.error('Error fetching pending recipes:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update recipe status (approve/reject)
router.put('/recipes/:recipeId/status', authMiddleware, async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { recipeId } = req.params;
    const { status, rejectionMessage } = req.body;

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status' });
    }

    // Require rejection message when rejecting
    if (status === 'rejected' && !rejectionMessage?.trim()) {
      return res.status(400).json({ error: 'Rejection message is required' });
    }

    // Check if user is admin or organizer
    const user = await db.select({ role: users.role })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user[0] || (user[0].role !== 'admin' && user[0].role !== 'organizer')) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Check if recipe exists and get contributor info
    const recipe = await db.select({
      id: recipes.id,
      contributorId: recipes.contributorId,
      title: recipes.title,
      images: recipes.images,
      contributor: {
        email: users.email,
        name: users.name
      }
    })
    .from(recipes)
    .leftJoin(users, eq(recipes.contributorId, users.id))
    .where(eq(recipes.id, parseInt(recipeId)))
    .limit(1);

    if (!recipe[0]) {
      return res.status(404).json({ error: 'Recipe not found' });
    }

    if (status === 'rejected') {
      // Delete associated S3 objects (images) if they exist
      const s3DeletionErrors: Array<{ key: string; error: string }> = [];
      const recipeImages = recipe[0].images as string[] || [];

      if (recipeImages.length > 0) {
        console.log('[Recipe Reject] Starting deletion of associated S3 objects:', recipeImages);

        for (const imageKey of recipeImages) {
          try {
            // Extract just the key if it's a full URL
            const key = imageKey.includes('http')
              ? imageKey.split('/recipes/')[1]
              : imageKey.startsWith('recipes/')
                ? imageKey
                : `recipes/${imageKey}`;

            console.log(`[Recipe Reject] Attempting to delete S3 object with key: ${key}`);
            await deleteObject(key);
            console.log(`[Recipe Reject] Successfully deleted S3 object: ${key}`);
          } catch (error) {
            console.error(`[Recipe Reject] Error deleting S3 object: ${imageKey}`, error);
            s3DeletionErrors.push({ key: imageKey, error: error instanceof Error ? error.message : 'Unknown error' });
          }
        }
      }

      // Store rejection message in notifications table
      await db.insert(notifications).values({
        userId: recipe[0].contributorId,
        type: 'recipe_rejected',
        message: `Your recipe "${recipe[0].title}" was rejected. Reason: ${rejectionMessage}`,
        metadata: {
          recipeId: parseInt(recipeId),
          recipeTitle: recipe[0].title,
          rejectionReason: rejectionMessage
        },
        isRead: false
      });

      // Delete the recipe
      await db.delete(recipes)
        .where(eq(recipes.id, parseInt(recipeId)));

      // Send email notification to contributor
      if (recipe[0].contributor?.email) {
        await sendEmail({
          to: recipe[0].contributor.email,
          subject: 'Your Recipe Has Been Rejected',
          text: `Dear ${recipe[0].contributor.name},\n\nYour recipe "${recipe[0].title}" has been rejected.\n\nReason: ${rejectionMessage}\n\nBest regards,\nThe Admin Team`,
          html: `
            <p>Dear ${recipe[0].contributor.name},</p>
            <p>Your recipe "${recipe[0].title}" has been rejected.</p>
            <p><strong>Reason:</strong> ${rejectionMessage}</p>
            <p>Best regards,<br>The Admin Team</p>
          `
        });
      }

      return res.json({ message: 'Recipe rejected successfully' });
    } else {
      // Update recipe status to approved
      const updatedRecipe = await db.update(recipes)
        .set({ status })
        .where(eq(recipes.id, parseInt(recipeId)))
        .returning();

      // Create notification for approval
      await db.insert(notifications).values({
        userId: recipe[0].contributorId,
        type: 'recipe_approved',
        message: `Your recipe "${recipe[0].title}" has been approved!`,
        metadata: {
          recipeId: parseInt(recipeId),
          recipeTitle: recipe[0].title
        },
        isRead: false
      });

      // Send email notification to contributor
      if (recipe[0].contributor?.email) {
        await sendEmail({
          to: recipe[0].contributor.email,
          subject: 'Your Recipe Has Been Approved',
          text: `Dear ${recipe[0].contributor.name},\n\nYour recipe "${recipe[0].title}" has been approved!\n\nBest regards,\nThe Admin Team`,
          html: `
            <p>Dear ${recipe[0].contributor.name},</p>
            <p>Your recipe "${recipe[0].title}" has been approved!</p>
            <p>Best regards,<br>The Admin Team</p>
          `
        });
      }

      return res.json({ recipe: updatedRecipe[0] });
    }
  } catch (error) {
    console.error('Error updating recipe status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;