import { Router } from 'express';
import { authMiddleware } from '../../middleware/auth.js';
import { db } from '../../db.js';
import { recipeComments, recipes, users, projects } from '../../schema.js';
import { eq, desc } from 'drizzle-orm';

const router = Router();

// Get comments for a recipe
router.get('/recipe/:recipeId', authMiddleware, async (req, res) => {
  try {
    const { recipeId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the recipe exists
    const recipe = await db.query.recipes.findFirst({
      where: eq(recipes.id, parseInt(recipeId))
    });

    if (!recipe) {
      return res.status(404).json({ error: 'Recipe not found' });
    }

    // Get all comments for the recipe with user information
    const comments = await db.query.recipeComments.findMany({
      where: eq(recipeComments.recipeId, parseInt(recipeId)),
      columns: {
        id: true,
        userId: true,
        recipeId: true,
        comment: true,
        createdAt: true,
        updatedAt: true
      },
      with: {
        user: {
          columns: {
            id: true,
            name: true,
            role: true
          }
        }
      },
      orderBy: [desc(recipeComments.createdAt)]
    });


    res.json({ comments });
  } catch (error) {
    console.error('Error fetching recipe comments:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add a comment to a recipe
router.post('/recipe/:recipeId', authMiddleware, async (req, res) => {
  try {
    const { recipeId } = req.params;
    const { comment } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (!comment) {
      return res.status(400).json({ error: 'Comment is required' });
    }

    // Check if the recipe exists
    const recipe = await db.query.recipes.findFirst({
      where: eq(recipes.id, parseInt(recipeId))
    });

    if (!recipe) {
      return res.status(404).json({ error: 'Recipe not found' });
    }

    // Insert the comment
    const [newComment] = await db.insert(recipeComments)
      .values({
        recipeId: parseInt(recipeId),
        userId,
        comment,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    // Get the user information for the response
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: {
        id: true,
        name: true,
        role: true
      }
    });

    // Include user information in the response
    const commentWithUser = {
      ...newComment,
      user
    };

    res.status(201).json({ comment: commentWithUser });
  } catch (error) {
    console.error('Error adding recipe comment:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update a comment
router.put('/:commentId', authMiddleware, async (req, res) => {
  try {
    const { commentId } = req.params;
    const { comment } = req.body;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (!comment) {
      return res.status(400).json({ error: 'Comment is required' });
    }

    // Get the comment to ensure it exists and get the recipeId
    const existingComment = await db.query.recipeComments.findFirst({
      where: eq(recipeComments.id, parseInt(commentId)),
      columns: {
        id: true,
        userId: true,
        recipeId: true,
        comment: true,
        createdAt: true,
        updatedAt: true
      }
    });

    console.log('Comment found:', existingComment);

    if (!existingComment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    // Check permissions based on role
    let hasPermission = false;

    // Admin can modify any comment
    if (userRole === 'admin') {
      hasPermission = true;
      console.log('Admin permission granted for comment update');
    }
    // Comment owner can modify their own comment
    else if (existingComment.userId === userId) {
      hasPermission = true;
      console.log('Comment owner permission granted for comment update');
    }
    // Organizer can modify any comments on recipes in their books
    else if (userRole === 'organizer') {
      // Get the recipe associated with the comment
      const recipe = await db.query.recipes.findFirst({
        where: eq(recipes.id, existingComment.recipeId),
        columns: {
          id: true,
          projectId: true
        }
      });

      if (recipe && recipe.projectId) {
        // Get the project to check if the user is the organizer
        const project = await db.query.projects.findFirst({
          where: eq(projects.id, recipe.projectId),
          columns: {
            id: true,
            organizerId: true
          }
        });

        console.log('Checking organizer permission for comment update:', {
          recipeId: existingComment.recipeId,
          recipeProjectId: recipe.projectId,
          projectOrganizerId: project?.organizerId,
          userId: userId
        });

        // Check if the project exists and is owned by this organizer
        if (project && project.organizerId === userId) {
          hasPermission = true;
          console.log('Organizer permission granted for comment update');
        }
      }
    }

    if (!hasPermission) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Update the comment
    const [updatedComment] = await db.update(recipeComments)
      .set({
        comment,
        updatedAt: new Date()
      })
      .where(eq(recipeComments.id, parseInt(commentId)))
      .returning();

    // Get the user information for the response
    const user = await db.query.users.findFirst({
      where: eq(users.id, updatedComment.userId),
      columns: {
        id: true,
        name: true,
        role: true
      }
    });

    // Include user information in the response
    const commentWithUser = {
      ...updatedComment,
      user
    };

    res.json({ comment: commentWithUser });
  } catch (error) {
    console.error('Error updating recipe comment:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete a comment
router.delete('/:commentId', authMiddleware, async (req, res) => {
  try {
    const { commentId } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the comment to ensure it exists and get the recipeId
    const existingComment = await db.query.recipeComments.findFirst({
      where: eq(recipeComments.id, parseInt(commentId)),
      columns: {
        id: true,
        userId: true,
        recipeId: true,
        comment: true,
        createdAt: true,
        updatedAt: true
      }
    });


    if (!existingComment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    // Check permissions based on role
    let hasPermission = false;

    // Admin can delete any comment
    if (userRole === 'admin') {
      hasPermission = true;
      console.log('Admin permission granted for comment deletion');
    }
    // Comment owner can delete their own comment
    else if (existingComment.userId === userId) {
      hasPermission = true;
      console.log('Comment owner permission granted for comment deletion');
    }
    // Organizer can delete any comments on recipes in their books
    else if (userRole === 'organizer') {
      // Get the recipe associated with the comment
      const recipe = await db.query.recipes.findFirst({
        where: eq(recipes.id, existingComment.recipeId),
        columns: {
          id: true,
          projectId: true
        }
      });

      if (recipe && recipe.projectId) {
        // Get the project to check if the user is the organizer
        const project = await db.query.projects.findFirst({
          where: eq(projects.id, recipe.projectId),
          columns: {
            id: true,
            organizerId: true
          }
        });

        console.log('Checking organizer permission for comment deletion:', {
          recipeId: existingComment.recipeId,
          recipeProjectId: recipe.projectId,
          projectOrganizerId: project?.organizerId,
          userId: userId
        });

        // Check if the project exists and is owned by this organizer
        if (project && project.organizerId === userId) {
          hasPermission = true;
          console.log('Organizer permission granted for comment deletion');
        }
      }
    }

    if (!hasPermission) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Delete the comment
    await db.delete(recipeComments)
      .where(eq(recipeComments.id, parseInt(commentId)));

    res.json({ message: 'Comment deleted successfully' });
  } catch (error) {
    console.error('Error deleting recipe comment:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;