import { Router } from 'express';
import sgMail from '@sendgrid/mail';

const router = Router();

// Initialize SendGrid with API key
sgMail.setApiKey(process.env.SENDGRID_API_KEY || '');

// Changed from '/send-data-request' to match the client's call
router.post('/send-data-request', async (req, res) => {
  console.log('Received data protection request:', req.body);
  
  try {
    const { to, subject, text } = req.body;

    if (!process.env.SENDGRID_API_KEY) {
      console.error('SendGrid API key is not configured');
      return res.status(500).json({ error: 'Email service is not configured' });
    }

    const msg = {
      to,
      from: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      subject,
      text,
    };

    console.log('Attempting to send email with config:', {
      to: msg.to,
      from: msg.from,
      subject: msg.subject
    });

    await sgMail.send(msg);
    console.log('Email sent successfully');

    res.status(200).json({ message: 'Email sent successfully' });
  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({ 
      error: 'Failed to send email',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router; 