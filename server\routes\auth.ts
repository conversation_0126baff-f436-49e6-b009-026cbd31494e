import { <PERSON>r, Request<PERSON><PERSON><PERSON> } from 'express';
import { db } from '../db.js';
import { users, userProfiles, projects, projectContributors, recipes } from '../schema.js';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { authMiddleware, requireAdmin, requireOrganizer, AuthRequest } from '../middleware/auth.js';
import { UserRole } from '../schema.js';
import { z } from 'zod';

const router = Router();

// Register new user
router.post('/register', async (req, res) => {
  try {
    const { email, password, name, role = UserRole.CONTRIBUTOR } = req.body;

    // Check if user already exists
    const existingUser = await db.query.users.findFirst({
      where: eq(users.email, email)
    });

    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create user
    const [user] = await db.insert(users).values({
      email,
      password: hashedPassword,
      name,
      role
    }).returning();

    // Create user profile
    await db.insert(userProfiles).values({
      userId: user.id
    });

    // Generate token
    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Error registering user' });
  }
});

// Login
router.post('/login', async (req, res) => {
  try {
    console.log('Login attempt received:', { email: req.body.email });
    const { email, password } = req.body;

    // Find user
    const user = await db.query.users.findFirst({
      where: eq(users.email, email)
    });

    console.log('User lookup result:', user ? { 
      id: user.id, 
      email: user.email, 
      role: user.role 
    } : 'User not found');

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check password
    const validPassword = await bcrypt.compare(password, user.password);
    console.log('Password validation:', validPassword ? 'success' : 'failed');
    
    if (!validPassword) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Update last login
    await db.update(users)
      .set({ lastLogin: new Date() })
      .where(eq(users.id, user.id));

    // Generate token
    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );

    console.log('Login successful:', { 
      userId: user.id, 
      email: user.email, 
      role: user.role 
    });

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Error logging in' });
  }
});

// Get current user
router.get('/me', authMiddleware, (async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    // Set cache control headers to prevent caching
    res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
    res.set('Expires', '0');
    res.set('Pragma', 'no-cache');

    const user = await db.query.users.findFirst({
      where: eq(users.id, authReq.user?.id || 0),
      with: {
        profile: true
      }
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        profile: user.profile
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Error fetching user' });
  }
}) as RequestHandler);

// Update user role (admin only)
router.patch('/users/:userId/role', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { role } = req.body;

    if (!Object.values(UserRole).includes(role)) {
      return res.status(400).json({ message: 'Invalid role' });
    }

    await db.update(users)
      .set({ role })
      .where(eq(users.id, parseInt(userId)));

    res.json({ message: 'User role updated successfully' });
  } catch (error) {
    console.error('Update role error:', error);
    res.status(500).json({ message: 'Error updating user role' });
  }
});

// List users (admin only)
router.get('/users', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const allUsers = await db.query.users.findMany({
      with: {
        profile: true
      }
    });

    res.json({
      users: allUsers.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        profile: user.profile
      }))
    });
  } catch (error) {
    console.error('List users error:', error);
    res.status(500).json({ message: 'Error fetching users' });
  }
});

// Delete own account
router.delete('/me', authMiddleware, (async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    const userId = authReq.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Start a transaction to delete all related data
    await db.transaction(async (tx) => {
      // Delete user's recipes
      await tx.delete(recipes).where(eq(recipes.contributorId, userId));

      // Delete user's project contributions
      await tx.delete(projectContributors).where(eq(projectContributors.userId, userId));

      // Delete user's projects
      await tx.delete(projects).where(eq(projects.organizerId, userId));

      // Finally, delete the user
      const [deletedUser] = await tx.delete(users)
        .where(eq(users.id, userId))
        .returning({
          id: users.id,
          name: users.name,
          email: users.email,
          role: users.role
        });

      if (!deletedUser) {
        throw new Error('User not found');
      }

      return deletedUser;
    });

    res.json({ message: 'Account and associated data deleted successfully' });
  } catch (error) {
    console.error('Error deleting account:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}) as RequestHandler);

export default router; 