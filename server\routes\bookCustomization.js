import express from 'express';
import { authMiddleware } from '../middleware/auth.js';
import * as bookCustomizationController from '../controllers/bookCustomization.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authMiddleware);

// Get book customization options
router.get('/projects/:id/customization', bookCustomizationController.getBookCustomization);

// Save book customization options
router.post('/projects/:id/customization', bookCustomizationController.saveBookCustomization);

export default router;
