import { Router, Request, Response, NextFunction, RequestHandler } from 'express';
import { db } from '../db.js';
import { projects, projectContributors, recipes, UserRole, users } from '../schema.js';
import { eq, and, desc, or } from 'drizzle-orm';
import { authMiddleware } from '../middleware/auth.js';
import { isContributor } from '../middleware/contributor.js';
import { z } from 'zod';
import { getAllProjects } from './organizer.js';
import { performOcr } from '../services/vision.js';
import { performTranscription } from '../services/whisper.js';
import { hasReachedRecipeLimit, getMaxRecipes } from '../constants/pricing.js';

const router = Router();

// Define the User type based on the schema
interface User {
  id: number;
  email: string;
  name: string;
  role: string;
  isActive: boolean;
  lastLogin: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// Define the authenticated user interface
interface AuthenticatedUser {
  id: number;
  role: string;
  email: string;
  name?: string;
}

// Extend Request to include user
interface AuthRequest extends Request {
  user?: AuthenticatedUser;
}

// Get all projects for the contributor
const getProjects: RequestHandler = async (req, res) => {
  try {
    const contributorId = (req as AuthRequest).user?.id;

    if (!contributorId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    // Get all projects where the user is a contributor
    const contributorProjects = await db.query.projectContributors.findMany({
      where: eq(projectContributors.userId, contributorId),
      with: {
        project: {
          with: {
            organizer: true,
            contributors: {
              with: {
                user: true
              }
            }
          }
        }
      }
    });

    // Extract and format the projects
    const formattedProjects = contributorProjects
      .map(cp => cp.project)
      .filter((project): project is NonNullable<typeof project> => project !== null)
      .map(project => ({
        ...project,
        contributors: project.contributors
          .filter(c => c.user !== null)
          .map(c => ({
            id: c.user!.id,
            name: c.user!.name,
            email: c.user!.email,
            role: c.role
          }))
      }));

    return res.json({ projects: formattedProjects });
  } catch (error) {
    console.error('Error fetching contributor projects:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Get all projects for the contributor (including those they've been invited to)
const getAllProjectsHandler: RequestHandler = async (req, res) => {
  try {
    const contributorId = (req as AuthRequest).user?.id;

    if (!contributorId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    console.log('Fetching all projects for contributor:', contributorId);

    // Get all projects where the user is a contributor with status 'accepted'
    const contributorProjects = await db.query.projectContributors.findMany({
      where: and(
        eq(projectContributors.userId, contributorId),
        eq(projectContributors.status, 'accepted')
      ),
      with: {
        project: {
          with: {
            organizer: true,
            contributors: {
              with: {
                user: true
              }
            }
          }
        }
      }
    });

    console.log('Found contributor projects:', contributorProjects.length);

    // Extract and format the projects
    const formattedProjects = contributorProjects
      .map(cp => cp.project)
      .filter((project): project is NonNullable<typeof project> => project !== null)
      .map(project => ({
        ...project,
        contributors: project.contributors
          .filter(c => c.user !== null)
          .map(c => ({
            id: c.user!.id,
            name: c.user!.name,
            email: c.user!.email,
            role: c.role
          }))
      }));

    return res.json({ projects: formattedProjects });
  } catch (error) {
    console.error('Error fetching all projects for contributor:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Get recipes for a specific project
const getProjectRecipes: RequestHandler = async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = (req as AuthRequest).user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    // Check if project exists
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId)),
      with: {
        organizer: {
          columns: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        contributors: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                email: true,
                role: true
              }
            }
          }
        }
      }
    });

    if (!project || !project.organizer) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Check if user is a contributor to this project
    const isContributor = project.contributors.some(c => c.user?.id === userId);
    const isOrganizer = project.organizer.id === userId;
    const isAdmin = (req as AuthRequest).user?.role === UserRole.ADMIN;
    const hasContributorRole = (req as AuthRequest).user?.role === UserRole.CONTRIBUTOR;
    const isOrganizerRole = (req as AuthRequest).user?.role === UserRole.ORGANIZER;

    // Get all recipes for the project
    const projectRecipes = await db.query.recipes.findMany({
      where: eq(recipes.projectId, parseInt(projectId)),
      columns: {
        id: true,
        projectId: true,
        contributorId: true,
        title: true,
        description: true,
        category: true,
        ingredients: true,
        instructions: true,
        measurementSystem: true,
        tags: true,
        images: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true
      },
      with: {
        contributor: {
          columns: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      orderBy: [desc(recipes.createdAt)]
    });

    // Add role information to each recipe
    const recipesWithRole = projectRecipes.map(recipe => ({
      ...recipe,
      createdByRole: recipe.role || recipe.contributor?.role || 'contributor'
    }));

    return res.json({ recipes: recipesWithRole });
  } catch (error) {
    console.error('Error fetching project recipes:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Get invitation status
const getInvitationStatus: RequestHandler = async (req, res) => {
  try {
    const { projectId } = req.params;
    const contributorId = (req as AuthRequest).user?.id;
    const token = req.query.token as string; // Get token from query parameters and cast to string

    console.log('Checking invitation status:', { projectId, contributorId, token });

    if (!contributorId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    // First check if the project exists
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId))
    });

    if (!project) {
      console.log('Project not found:', projectId);
      return res.status(404).json({ message: 'Project not found' });
    }

    // Get project details with contributors
    const projectWithContributors = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId)),
      with: {
        contributors: {
          with: {
            user: true
          }
        }
      }
    });

    if (!projectWithContributors) {
      console.log('Project with contributors not found:', projectId);
      return res.status(404).json({ message: 'Project not found' });
    }

    // Check if user is already a contributor
    const existingContributor = projectWithContributors.contributors.find(
      c => c.user?.id === contributorId
    );

    if (existingContributor) {
      console.log('User is already a contributor:', existingContributor);
      return res.json({
        status: existingContributor.status,
        message: `You are already ${existingContributor.status} as a contributor`
      });
    }

    // If no token provided, return not found
    if (!token) {
      console.log('No token provided');
      return res.status(404).json({ message: 'Invitation not found' });
    }

    // Verify invitation exists by token and project ID
    const invitation = await db.query.projectContributors.findFirst({
      where: and(
        eq(projectContributors.projectId, parseInt(projectId)),
        eq(projectContributors.invitationToken, token)
      )
    });

    console.log('Found invitation:', invitation);

    if (!invitation) {
      console.log('No invitation found for token:', token);
      return res.status(404).json({ message: 'Invitation not found' });
    }

    // Check if invitation has expired
    if (invitation.invitationExpiresAt && new Date() > invitation.invitationExpiresAt) {
      console.log('Invitation expired at:', invitation.invitationExpiresAt);
      return res.status(400).json({ message: 'Invitation has expired' });
    }

    // Get the current user's information
    const currentUser = await db.query.users.findFirst({
      where: eq(users.id, contributorId)
    });

    if (!currentUser) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Get the project to check if the current user is the organizer
    const projectInfo = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId))
    });

    const isOrganizer = projectInfo && projectInfo.organizerId === contributorId;
    const isAdmin = currentUser.role === 'admin';

    // If the user is the organizer or admin, they can view any invitation
    if (isOrganizer || isAdmin) {
      console.log('Organizer or admin viewing invitation:', {
        userId: contributorId,
        userEmail: currentUser.email,
        isOrganizer,
        isAdmin
      });
      // Continue with viewing
    }
    // Otherwise, check if the invitation was sent to this user's email
    else {
      // Find the invited user's email from the project contributors
      const invitedContributor = await db.query.projectContributors.findFirst({
        where: and(
          eq(projectContributors.projectId, parseInt(projectId)),
          eq(projectContributors.invitationToken, token)
        ),
        with: {
          user: true
        }
      });

      // If the invitation was sent to a different email, return an error
      if (invitedContributor?.user && invitedContributor.user.email !== currentUser.email) {
        console.log('Invitation was sent to a different email:', {
          invitedEmail: invitedContributor.user.email,
          currentUserEmail: currentUser.email
        });
        return res.status(403).json({ message: 'This invitation was sent to a different email address' });
      }

      // Update the userId to match the current user
      if (invitation.userId !== contributorId) {
        console.log('Updating invitation to match current user ID:', {
          oldUserId: invitation.userId,
          newUserId: contributorId
        });

        await db.update(projectContributors)
          .set({ userId: contributorId })
          .where(eq(projectContributors.id, invitation.id));
      }
    }

    // Get project details to include in the response
    const projectDetails = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId)),
      with: {
        organizer: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return res.json({
      status: invitation.status,
      message: `Invitation is ${invitation.status}`,
      projectName: projectDetails?.name || 'Unknown Project',
      organizerName: projectDetails?.organizer?.name || 'Unknown Organizer'
    });
  } catch (error) {
    console.error('Error checking invitation status:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Get project details for a contributor
const getProjectDetails: RequestHandler = async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = (req as AuthRequest).user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId)),
      with: {
        organizer: true,
        contributors: {
          with: {
            user: true
          }
        }
      }
    });

    if (!project || !project.organizer) {
      return res.status(404).json({ message: 'Project or organizer not found' });
    }

    const isContributor = project.contributors.some(c => c.user?.id === userId);
    const isOrganizer = project.organizer.id === userId;
    const isAdmin = (req as AuthRequest).user?.role === UserRole.ADMIN;
    const hasContributorRole = (req as AuthRequest).user?.role === UserRole.CONTRIBUTOR;

    return res.json({ project });
  } catch (error) {
    console.error('Error fetching project details:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Accept project invitation
const acceptInvitation: RequestHandler = async (req, res) => {
  try {
    const { projectId } = req.params;
    const { token } = req.body; // Get token from request body
    const contributorId = (req as AuthRequest).user?.id;

    if (!contributorId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    console.log('Accepting invitation:', { projectId, token, contributorId });

    // First check if the project exists
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId))
    });

    if (!project) {
      console.log('Project not found:', projectId);
      return res.status(404).json({ message: 'Project not found' });
    }

    // Verify invitation exists by token and project ID
    const invitation = await db.query.projectContributors.findFirst({
      where: and(
        eq(projectContributors.projectId, parseInt(projectId)),
        eq(projectContributors.invitationToken, token)
      )
    });

    console.log('Found invitation:', invitation);

    if (!invitation) {
      console.log('No invitation found for token:', token);
      return res.status(404).json({ message: 'Invitation not found' });
    }

    // If invitation is already accepted, return success without trying to update
    if (invitation.status === 'accepted') {
      console.log('Invitation already accepted');
      return res.json({
        message: 'Invitation already accepted',
        status: 'accepted'
      });
    }

    if (invitation.status !== 'pending') {
      console.log('Invitation is not pending:', invitation.status);
      return res.status(400).json({ message: `Invitation is already ${invitation.status}` });
    }

    // Check if invitation has expired
    if (invitation.invitationExpiresAt && new Date() > invitation.invitationExpiresAt) {
      console.log('Invitation expired at:', invitation.invitationExpiresAt);
      return res.status(400).json({ message: 'Invitation has expired' });
    }

    // Get the current user's information
    const currentUser = await db.query.users.findFirst({
      where: eq(users.id, contributorId)
    });

    if (!currentUser) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Get the project to check if the current user is the organizer
    const projectData = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId))
    });

    const isOrganizer = projectData && projectData.organizerId === contributorId;
    const isAdmin = currentUser.role === 'admin';

    // If the user is the organizer or admin, they can accept any invitation
    if (isOrganizer || isAdmin) {
      console.log('Organizer or admin accepting invitation:', {
        userId: contributorId,
        userEmail: currentUser.email,
        isOrganizer,
        isAdmin
      });
      // Continue with acceptance
    }
    // Otherwise, check if the invitation was sent to this user's email
    else {
      // Find the invited user's email from the project contributors
      const invitedContributor = await db.query.projectContributors.findFirst({
        where: and(
          eq(projectContributors.projectId, parseInt(projectId)),
          eq(projectContributors.invitationToken, token)
        ),
        with: {
          user: true
        }
      });

      // If the invitation was sent to a different email, return an error
      if (invitedContributor?.user && invitedContributor.user.email !== currentUser.email) {
        console.log('Invitation was sent to a different email:', {
          invitedEmail: invitedContributor.user.email,
          currentUserEmail: currentUser.email
        });
        return res.status(403).json({ message: 'This invitation was sent to a different email address' });
      }
    }

    // Update invitation status, always use the current user's ID, and set acceptance timestamp
    const [updatedInvitation] = await db.update(projectContributors)
      .set({
        status: 'accepted',
        userId: contributorId, // Always use the current user's ID
        invitationAcceptedAt: new Date(),
        updatedAt: new Date()
      })
      .where(and(
        eq(projectContributors.id, invitation.id),
        eq(projectContributors.status, 'pending')
      ))
      .returning();

    console.log('Updated invitation:', updatedInvitation);

    if (!updatedInvitation) {
      console.log('Failed to update invitation status');
      return res.status(500).json({ message: 'Failed to update invitation status' });
    }

    // Update project status to in_progress if it's in draft
    if (project.status === 'draft') {
      console.log('Updating project status to in_progress');
      await db.update(projects)
        .set({ status: 'in_progress' })
        .where(eq(projects.id, parseInt(projectId)));
    }

    return res.json({
      message: 'Invitation accepted successfully',
      status: 'accepted'
    });
  } catch (error) {
    console.error('Error accepting invitation:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Submit a recipe
const submitRecipe: RequestHandler = async (req, res) => {
  try {
    const {
      title,
      description,
      category,
      ingredients,
      instructions,
      projectId,
      measurementSystem,
      tags,
      images
    } = req.body;
    const contributorId = (req as AuthRequest).user?.id;
    const userRole = (req as AuthRequest).user?.role;

    if (!contributorId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    // Get the project to check its pricing tier and current recipe count
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId)),
      with: {
        recipes: true
      }
    });

    if (!project) {
      return res.status(404).json({ error: "Project not found" });
    }

    // Only admin users can bypass recipe limits
    const isAdmin = userRole === UserRole.ADMIN;
    const isOrganizer = project.organizerId === contributorId;

    // Count current recipes in the project
    const currentRecipeCount = project.recipes?.length || 0;
    const maxRecipes = getMaxRecipes(project.pricingTier || 'small');

    console.log('[Recipe Limit Check]', {
      projectId,
      contributorId,
      pricingTier: project.pricingTier || 'small',
      currentRecipeCount,
      maxRecipes,
      isAdmin,
      isOrganizer,
      hasReachedLimit: hasReachedRecipeLimit(currentRecipeCount, project.pricingTier || 'small')
    });

    // If not admin, check recipe limits (organizers are also subject to limits)
    if (!isAdmin) {
      // Check if the project has reached its recipe limit based on pricing tier
      if (hasReachedRecipeLimit(currentRecipeCount, project.pricingTier || 'small')) {
        return res.status(403).json({
          error: `You've reached the recipe limit for this tier. Please upgrade to add more recipes.`
        });
      }
    }

    const [recipe] = await db.insert(recipes).values({
      title,
      description,
      category,
      ingredients,
      instructions,
      contributorId,
      projectId: parseInt(projectId),
      role: userRole || 'contributor',
      measurementSystem,
      tags,
      images,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();

    return res.status(201).json(recipe);
  } catch (error) {
    console.error('Error submitting recipe:', error);
    return res.status(500).json({ error: "Failed to submit recipe" });
  }
};

// OCR Recipe Scan Upload
router.post('/recipes/ocr', authMiddleware as RequestHandler, async (req, res) => {
  try {
    const { user } = req as AuthRequest;
    const { imageKey, projectId } = req.body;

    if (!imageKey) {
      return res.status(400).json({ error: 'Image key is required' });
    }

    if (!projectId) {
      return res.status(400).json({ error: 'Project ID is required' });
    }

    // Admin users can add recipes to any project
    if (user?.role !== UserRole.ADMIN) {
      // Check if non-admin user has access to this project
      const projectContributor = await db
        .select()
        .from(projectContributors)
        .where(and(
          eq(projectContributors.userId, user?.id || 0),
          eq(projectContributors.projectId, projectId),
          or(
            eq(projectContributors.status, 'accepted'),
            eq(projectContributors.role, 'organizer'),
            eq(projectContributors.role, 'admin')
          )
        ))
        .execute();

      if (projectContributor.length === 0) {
        return res.status(403).json({ error: 'You do not have permission to add recipes to this project' });
      }

      // Get the project to check its pricing tier and current recipe count
      const project = await db.query.projects.findFirst({
        where: eq(projects.id, projectId),
        with: {
          recipes: true
        }
      });

      if (project) {
        // Check if the user is admin or organizer
        const isAdmin = user?.role === UserRole.ADMIN;
        const isOrganizer = project.organizerId === user?.id;

        // Count current recipes in the project
        const currentRecipeCount = project.recipes?.length || 0;
        const maxRecipes = getMaxRecipes(project.pricingTier || 'small');

        console.log('[OCR Recipe Limit Check]', {
          projectId,
          userId: user?.id,
          userRole: user?.role,
          pricingTier: project.pricingTier || 'small',
          currentRecipeCount,
          maxRecipes,
          isAdmin,
          isOrganizer,
          hasReachedLimit: hasReachedRecipeLimit(currentRecipeCount, project.pricingTier || 'small')
        });

        // Only admin users can bypass recipe limits (organizers are also subject to limits)
        if (!isAdmin) {
          // Check if the project has reached its recipe limit based on pricing tier
          if (hasReachedRecipeLimit(currentRecipeCount, project.pricingTier || 'small')) {
            return res.status(403).json({
              error: `You've reached the recipe limit for this tier. Please upgrade to add more recipes.`
            });
          }
        }
      }
    }

    console.log(`[OCR] Processing image ${imageKey} for project ${projectId} by user ${user?.id}`);

    // Process the image with OCR
    const ocrResult = await performOcr(imageKey);

    if (!ocrResult.success) {
      return res.status(422).json({
        error: 'Failed to process image with OCR',
        details: ocrResult.error
      });
    }

    // Create a new recipe record with OCR data
    const recipeData = {
      projectId,
      contributorId: user?.id,
      title: ocrResult.structuredData?.title || 'Untitled Recipe',
      description: ocrResult.structuredData?.description || '',
      status: 'pending',
      ingredients: ocrResult.structuredData?.ingredients || [],
      instructions: ocrResult.structuredData?.instructions || [],
      tags: [],
      images: [imageKey],
      role: user?.role,
      isOcrGenerated: true,
      ocrSourceImage: imageKey,
      ocrRawText: ocrResult.rawText,
      ocrExtractedData: ocrResult.structuredData
    };

    const [recipe] = await db.insert(recipes).values(recipeData).returning();

    return res.status(201).json({
      message: 'OCR recipe created successfully',
      recipe,
      ocrResult
    });
  } catch (error) {
    console.error('Error processing OCR recipe:', error);
    return res.status(500).json({
      error: 'Failed to process OCR recipe',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Voice Transcription Recipe Upload
router.post('/recipes/transcribe', authMiddleware as RequestHandler, async (req, res) => {
  try {
    const { user } = req as AuthRequest;
    const { audioKey, projectId } = req.body;

    if (!audioKey) {
      return res.status(400).json({ error: 'Audio file key is required' });
    }

    if (!projectId) {
      return res.status(400).json({ error: 'Project ID is required' });
    }

    // Admin users can add recipes to any project
    if (user?.role !== UserRole.ADMIN) {
      // Check if non-admin user has access to this project
      const projectContributor = await db
        .select()
        .from(projectContributors)
        .where(and(
          eq(projectContributors.userId, user?.id || 0),
          eq(projectContributors.projectId, projectId),
          or(
            eq(projectContributors.status, 'accepted'),
            eq(projectContributors.role, 'organizer'),
            eq(projectContributors.role, 'admin')
          )
        ))
        .execute();

      if (projectContributor.length === 0) {
        return res.status(403).json({ error: 'You do not have permission to add recipes to this project' });
      }

      // Get the project to check its pricing tier and current recipe count
      const project = await db.query.projects.findFirst({
        where: eq(projects.id, projectId),
        with: {
          recipes: true
        }
      });

      if (project) {
        // Check if the user is admin or organizer
        const isAdmin = user?.role === UserRole.ADMIN;
        const isOrganizer = project.organizerId === user?.id;

        // Count current recipes in the project
        const currentRecipeCount = project.recipes?.length || 0;
        const maxRecipes = getMaxRecipes(project.pricingTier || 'small');

        console.log('[Voice Transcription Recipe Limit Check]', {
          projectId,
          userId: user?.id,
          userRole: user?.role,
          pricingTier: project.pricingTier || 'small',
          currentRecipeCount,
          maxRecipes,
          isAdmin,
          isOrganizer,
          hasReachedLimit: hasReachedRecipeLimit(currentRecipeCount, project.pricingTier || 'small')
        });

        // Only admin users can bypass recipe limits (organizers are also subject to limits)
        if (!isAdmin) {
          // Check if the project has reached its recipe limit based on pricing tier
          if (hasReachedRecipeLimit(currentRecipeCount, project.pricingTier || 'small')) {
            return res.status(403).json({
              error: `You've reached the recipe limit for this tier. Please upgrade to add more recipes.`
            });
          }
        }
      }
    }

    console.log(`[Voice Transcription] Processing audio ${audioKey} for project ${projectId} by user ${user?.id}`);

    // Process the audio with Whisper API
    const transcriptionResult = await performTranscription(audioKey);

    if (!transcriptionResult.success) {
      return res.status(422).json({
        error: 'Failed to transcribe audio',
        details: transcriptionResult.error
      });
    }

    console.log(`[Voice Transcription] Transcription successful. Raw text length: ${transcriptionResult.rawText.length} characters`);
    console.log(`[Voice Transcription] Title: "${transcriptionResult.structuredData?.title || 'Untitled'}"`);
    console.log(`[Voice Transcription] Description: "${transcriptionResult.structuredData?.description?.substring(0, 100) || ''}${(transcriptionResult.structuredData?.description?.length || 0) > 100 ? '...' : ''}"`);
    console.log(`[Voice Transcription] Ingredients count: ${transcriptionResult.structuredData?.ingredients?.length || 0}`);
    console.log(`[Voice Transcription] Instructions count: ${transcriptionResult.structuredData?.instructions?.length || 0}`);

    // Create a new recipe record with transcription data
    const recipeData = {
      projectId,
      contributorId: user?.id,
      title: transcriptionResult.structuredData?.title || 'Untitled Recipe',
      description: transcriptionResult.structuredData?.description || '',
      status: 'pending',
      ingredients: transcriptionResult.structuredData?.ingredients || [],
      instructions: transcriptionResult.structuredData?.instructions || [],
      tags: [],
      images: [],
      role: user?.role,
      isVoiceTranscribed: true,
      voiceSourceAudio: audioKey,
      voiceRawText: transcriptionResult.rawText,
      voiceExtractedData: transcriptionResult.structuredData
    };

    const [recipe] = await db.insert(recipes).values(recipeData).returning();

    return res.status(201).json({
      message: 'Voice transcription recipe created successfully',
      recipe,
      transcriptionResult
    });
  } catch (error) {
    console.error('Error processing voice transcription:', error);
    return res.status(500).json({
      error: 'Failed to process voice transcription',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Route definitions
router.get('/projects', authMiddleware as RequestHandler, isContributor as RequestHandler, getProjects);
router.get('/all-projects', authMiddleware as RequestHandler, getAllProjectsHandler as RequestHandler);
router.get('/projects/:projectId/recipes', authMiddleware as RequestHandler, getProjectRecipes as RequestHandler);
router.get('/projects/:projectId/invite-status', authMiddleware as RequestHandler, isContributor as RequestHandler, getInvitationStatus as RequestHandler);
router.get('/projects/:projectId', authMiddleware as RequestHandler, isContributor as RequestHandler, getProjectDetails as RequestHandler);
router.post('/projects/:projectId/accept-invite', authMiddleware as RequestHandler, isContributor as RequestHandler, acceptInvitation as RequestHandler);
router.post('/recipes', authMiddleware as RequestHandler, isContributor as RequestHandler, submitRecipe as RequestHandler);

// Get all recipes submitted by the contributor
router.get('/recipes', authMiddleware as RequestHandler, isContributor as RequestHandler, (async (req, res) => {
  try {
    const userId = (req as AuthRequest).user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    console.log('Fetching recipes for user:', userId);

    // First, check if the user exists
    const user = await db.select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    console.log('User found:', user[0]);

    const userRecipes = await db.select({
      id: recipes.id,
      title: recipes.title,
      description: recipes.description,
      status: recipes.status,
      createdAt: recipes.createdAt,
      projectId: recipes.projectId,
      project: {
        id: projects.id,
        name: projects.name,
        description: projects.description
      }
    })
    .from(recipes)
    .innerJoin(projects, eq(recipes.projectId, projects.id))
    .where(eq(recipes.contributorId, userId))
    .orderBy(desc(recipes.createdAt));

    console.log('Found recipes:', userRecipes);

    // Return the recipes wrapped in a recipes property
    return res.json({ recipes: userRecipes });
  } catch (error) {
    console.error('Error fetching recipes:', error);
    return res.status(500).json({ error: 'Failed to fetch recipes' });
  }
}) as RequestHandler);

export default router;