import { eq, and, lt } from 'drizzle-orm';
import { db } from '../db.js';
import { notifications, users } from '../schema.js';
import express from 'express';
import { authMiddleware } from '../middleware/auth.js';
import { sql } from 'drizzle-orm';

const router = express.Router();

// Get user's notifications
const getNotifications = async (req: any, res: any) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get only notifications for this user
    const userNotifications = await db.query.notifications.findMany({
      where: eq(notifications.userId, userId),
      orderBy: (notifications, { desc }) => [desc(notifications.createdAt)],
    });

    res.json({ notifications: userNotifications });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    res.status(500).json({ message: 'Failed to fetch notifications' });
  }
};

// Mark notification as read
const markAsRead = async (req: any, res: any) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // First check if the notification belongs to this user
    const notification = await db.query.notifications.findFirst({
      where: and(
        eq(notifications.id, parseInt(notificationId)),
        eq(notifications.userId, userId)
      ),
    });

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    const [updatedNotification] = await db
      .update(notifications)
      .set({ isRead: true })
      .where(eq(notifications.id, parseInt(notificationId)))
      .returning();

    if (!updatedNotification) {
      return res.status(500).json({ message: 'Failed to mark notification as read' });
    }

    res.json({ notification: updatedNotification });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ message: 'Failed to mark notification as read' });
  }
};

// Clean up old notifications (can be called by a cron job)
const cleanupOldNotifications = async () => {
  try {
    // Delete notifications older than 14 days
    const fourteenDaysAgo = new Date();
    fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

    console.log('Cleaning up notifications older than:', fourteenDaysAgo);

    // Get count before cleanup
    const beforeCount = await db.select({ count: sql`count(*)` })
      .from(notifications);
    console.log('Notifications before cleanup:', beforeCount[0].count);

    // Delete old notifications
    await db
      .delete(notifications)
      .where(lt(notifications.createdAt, fourteenDaysAgo));

    // Get count after cleanup
    const afterCount = await db.select({ count: sql`count(*)` })
      .from(notifications);
    console.log('Notifications after cleanup:', afterCount[0].count);
    console.log('Deleted notifications:', Number(beforeCount[0].count) - Number(afterCount[0].count));

    console.log('Old notifications cleaned up successfully');
  } catch (error) {
    console.error('Error cleaning up old notifications:', error);
  }
};

// Manual cleanup endpoint (for testing)
const manualCleanup = async (req: any, res: any) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Check if user is admin
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: { role: true }
    });

    if (!user || user.role !== 'admin') {
      return res.status(403).json({ message: 'Forbidden' });
    }

    // Get count of notifications before cleanup
    const beforeCount = await db.select({ count: sql`count(*)` })
      .from(notifications);

    // Perform cleanup
    await cleanupOldNotifications();

    // Get count of notifications after cleanup
    const afterCount = await db.select({ count: sql`count(*)` })
      .from(notifications);

    res.json({
      message: 'Manual cleanup completed',
      stats: {
        before: Number(beforeCount[0].count),
        after: Number(afterCount[0].count),
        deleted: Number(beforeCount[0].count) - Number(afterCount[0].count)
      }
    });
  } catch (error) {
    console.error('Error in manual cleanup:', error);
    res.status(500).json({ message: 'Failed to perform manual cleanup' });
  }
};

// Register routes
router.get('/', authMiddleware, getNotifications);
router.patch('/:notificationId/read', authMiddleware, markAsRead);
router.post('/cleanup', authMiddleware, manualCleanup);

// Export cleanup function for cron job
export { cleanupOldNotifications };

export default router;