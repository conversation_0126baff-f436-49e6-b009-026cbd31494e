import { Router, Response, Request, RequestHandler } from 'express';
import { db } from '../db.js';
import { projects, projectContributors, users, recipes, notifications } from '../schema.js';
import { eq, and, desc } from 'drizzle-orm';
import { authMiddleware, AuthRequest } from '../middleware/auth.js';
import { z } from 'zod';
import { sendInviteEmail } from '../services/email.js';
import crypto from 'crypto';
import { requireOrganizer } from '../middleware/requireOrganizer.js';
import { deleteObject } from '../services/s3.js';
import { hasReachedContributorLimit } from '../constants/pricing.js';

const router = Router();

// Get all projects for the authenticated user
const getProjects: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?.id;
  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    // Get all projects where user is either organizer or contributor
    const userProjects = await db.query.projects.findMany({
      with: {
        contributors: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    // Process projects to ensure no duplicate contributors
    const processedProjects = userProjects.map(project => {
      // Create a map to track unique contributors by user ID
      const uniqueContributors = new Map();

      // Add each contributor to the map, using user ID as the key
      project.contributors.forEach(contributor => {
        // Skip if user is null or if user.id is undefined
        const user = contributor.user;
        if (!user?.id) return;

        const userId = user.id;
        // If this user is already in the map, only keep the most recent record
        if (uniqueContributors.has(userId)) {
          const existingContributor = uniqueContributors.get(userId);

          // Convert dates safely, defaulting to epoch start if null/invalid
          const getDate = (dateValue: Date | string | null): Date => {
            if (!dateValue) return new Date(0);
            return dateValue instanceof Date ? dateValue : new Date(dateValue);
          };

          const currentDate = getDate(contributor.updatedAt);
          const existingDate = getDate(existingContributor.updatedAt);

          if (currentDate > existingDate) {
            uniqueContributors.set(userId, contributor);
          }
        } else {
          uniqueContributors.set(userId, contributor);
        }
      });

      // Convert the map back to an array
      return {
        ...project,
        contributors: Array.from(uniqueContributors.values())
      };
    });

    res.json({ projects: processedProjects });
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get all projects for all users
export const getAllProjects: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    console.log('Fetching all projects for user:', authReq.user?.id, 'with role:', authReq.user?.role);

    const allProjects = await db.query.projects.findMany({
      with: {
        organizer: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        },
        contributors: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    // Process projects to ensure no duplicate contributors
    const processedProjects = allProjects.map(project => {
      // Create a map to track unique contributors by user ID
      const uniqueContributors = new Map();

      // Add each contributor to the map, using user ID as the key
      project.contributors.forEach(contributor => {
        // Skip if user is null or if user.id is undefined
        const user = contributor.user;
        if (!user?.id) return;

        const userId = user.id;
        // If this user is already in the map, only keep the most recent record
        if (uniqueContributors.has(userId)) {
          const existingContributor = uniqueContributors.get(userId);

          // Convert dates safely, defaulting to epoch start if null/invalid
          const getDate = (dateValue: Date | string | null): Date => {
            if (!dateValue) return new Date(0);
            return dateValue instanceof Date ? dateValue : new Date(dateValue);
          };

          const currentDate = getDate(contributor.updatedAt);
          const existingDate = getDate(existingContributor.updatedAt);

          if (currentDate > existingDate) {
            uniqueContributors.set(userId, contributor);
          }
        } else {
          uniqueContributors.set(userId, contributor);
        }
      });

      // Convert the map back to an array
      return {
        ...project,
        contributors: Array.from(uniqueContributors.values())
      };
    });

    console.log('Found projects:', processedProjects.length);
    res.json({ projects: processedProjects });
  } catch (error) {
    console.error('Error fetching all projects:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get all recipes for a project
const getProjectRecipes: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  try {
    const { projectId } = req.params;
    const userId = authReq.user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Verify project exists
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId))
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Get all recipes for the project
    const projectRecipes = await db.query.recipes.findMany({
      where: eq(recipes.projectId, parseInt(projectId)),
      columns: {
        id: true,
        projectId: true,
        contributorId: true,
        title: true,
        description: true,
        category: true,
        ingredients: true,
        instructions: true,
        measurementSystem: true,
        tags: true,
        images: true,
        role: true,
        status: true,
        createdAt: true,
        updatedAt: true
      },
      with: {
        contributor: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [desc(recipes.createdAt)]
    });

    res.json({ recipes: projectRecipes });
  } catch (error) {
    console.error('Error fetching project recipes:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create a new project
const createProject: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?.id;
  const userRole = authReq.user?.role;

  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const { name, description, pricingTier, maxContributors } = req.body;
    console.log('Creating project with data:', { name, description, pricingTier, maxContributors, userId, userRole });

    if (!name) {
      return res.status(400).json({ message: 'Project name is required' });
    }

    // Create only the project record
    const [newProject] = await db.insert(projects)
      .values({
        name,
        description,
        organizerId: userId,
        status: 'active',
        role: userRole || 'contributor', // Set role based on user's role
        maxContributors: maxContributors || 10, // Use provided value or default
        pricingTier: pricingTier || 'small' // Use provided tier or default
      })
      .returning();

    console.log('Project created successfully:', newProject);

    // Check if any contributor records were created
    const contributors = await db.query.projectContributors.findMany({
      where: eq(projectContributors.projectId, newProject.id)
    });
    console.log('Contributors after project creation:', contributors);

    res.status(201).json({ project: newProject });
  } catch (error) {
    console.error('Error creating project:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update project
const updateProject: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?.id;
  const userRole = authReq.user?.role;

  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const { projectId } = req.params;
    const { name, description, status, role } = req.body;

    // First, get the existing project
    const existingProject = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId))
    });

    if (!existingProject) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Only allow role updates if user is admin
    const updatedRole = userRole === 'admin' ? role : existingProject.role;

    const [updatedProject] = await db.update(projects)
      .set({
        name,
        description,
        status,
        role: updatedRole,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(projects.id, parseInt(projectId)),
          eq(projects.organizerId, userId)
        )
      )
      .returning();

    if (!updatedProject) {
      return res.status(404).json({ message: 'Project not found' });
    }

    res.json({ project: updatedProject });
  } catch (error) {
    console.error('Error updating project:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete project
const deleteProject: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?.id;
  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const { projectId } = req.params;

    const [deletedProject] = await db.delete(projects)
      .where(
        and(
          eq(projects.id, parseInt(projectId)),
          eq(projects.organizerId, userId)
        )
      )
      .returning();

    if (!deletedProject) {
      return res.status(404).json({ message: 'Project not found' });
    }

    res.json({ message: 'Project deleted successfully' });
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Add contributor to project
const addContributor: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?.id;
  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const { projectId } = req.params;
    const { contributorId } = req.body;

    // Verify project exists and user is organizer
    const project = await db.query.projects.findFirst({
      where: and(
        eq(projects.id, parseInt(projectId)),
        eq(projects.organizerId, userId)
      )
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Check if contributor already exists
    const existingContributor = await db.query.projectContributors.findFirst({
      where: and(
        eq(projectContributors.projectId, parseInt(projectId)),
        eq(projectContributors.userId, contributorId)
      )
    });

    if (existingContributor) {
      // Update existing contributor
      const [updatedContributor] = await db.update(projectContributors)
        .set({
          status: 'accepted',
          updatedAt: new Date()
        })
        .where(eq(projectContributors.id, existingContributor.id))
        .returning();

      return res.status(200).json({ contributor: updatedContributor });
    }

    // Add new contributor
    const [newContributor] = await db.insert(projectContributors)
      .values({
        projectId: parseInt(projectId),
        userId: contributorId,
        status: 'accepted',
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    res.status(201).json({ contributor: newContributor });
  } catch (error) {
    console.error('Error adding contributor:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Remove contributor from project
const removeContributor: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  const userId = authReq.user?.id;
  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const { projectId, contributorId } = req.params;

    // Verify project exists and user is organizer
    const project = await db.query.projects.findFirst({
      where: and(
        eq(projects.id, parseInt(projectId)),
        eq(projects.organizerId, userId)
      )
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    const [deletedContributor] = await db.delete(projectContributors)
      .where(
        and(
          eq(projectContributors.projectId, parseInt(projectId)),
          eq(projectContributors.userId, parseInt(contributorId))
        )
      )
      .returning();

    if (!deletedContributor) {
      return res.status(404).json({ message: 'Contributor not found' });
    }

    res.json({ message: 'Contributor removed successfully' });
  } catch (error) {
    console.error('Error removing contributor:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Invite a contributor to a project
const inviteContributor: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  try {
    const { projectId } = req.params;
    const { email, deadline, reminderFrequency } = z.object({
      email: z.string().email(),
      deadline: z.string().datetime().optional(),
      reminderFrequency: z.enum(['1min', '2min', '5min', '15min', '30min', '1hour', 'daily', 'weekly', 'biweekly', 'monthly']).optional()
    }).parse(req.body);

    const organizerId = authReq.user?.id;
    if (!organizerId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    console.log('Inviting to project:', {
      projectId,
      organizerId: organizerId,
      userRole: authReq.user?.role,
      deadline,
      reminderFrequency
    });

    // Verify project exists with relations
    const project = await db.query.projects.findFirst({
      where: eq(projects.id, parseInt(projectId)),
      with: {
        organizer: {
          columns: {
            id: true,
            name: true,
            email: true
          }
        },
        contributors: {
          with: {
            user: {
              columns: {
                id: true,
                name: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!project) {
      return res.status(404).json({ message: 'Project not found' });
    }

    // Check if the current user is the creator of the recipe book
    if (project.organizerId !== organizerId && project.role !== 'admin') {
      return res.status(403).json({ message: `Only the creator of the recipe book can send invitations: ${project.role}` });
    }

    // Count current contributors (both accepted and pending)
    const currentContributors = project.contributors.length;

    // Check if the project has reached its contributor limit based on pricing tier
    if (hasReachedContributorLimit(currentContributors, project.pricingTier || 'small')) {
      return res.status(403).json({
        message: `You have reached the maximum number of contributors for your current pricing tier (${project.pricingTier || 'small'}). Please upgrade to a higher tier to add more contributors.`
      });
    }

    // Find user by email
    const user = await db.query.users.findFirst({
      where: eq(users.email, email)
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is already a contributor with any status
    const existingContributor = await db.query.projectContributors.findFirst({
      where: and(
        eq(projectContributors.projectId, parseInt(projectId)),
        eq(projectContributors.userId, user.id)
      )
    });

    const now = new Date();
    const invitationExpiresAt = new Date(now);
    invitationExpiresAt.setDate(invitationExpiresAt.getDate() + 7); // Expires in 7 days

    if (existingContributor) {
      // If the contributor already exists, update the invitation details
      const invitationToken = crypto.randomBytes(32).toString('hex');

      const [updatedContributor] = await db.update(projectContributors)
        .set({
          status: 'pending',
          invitationToken,
          invitationSentAt: now,
          invitationExpiresAt,
          deadline: deadline ? new Date(deadline) : null,
          reminderFrequency: reminderFrequency || 'weekly',
          updatedAt: now
        })
        .where(eq(projectContributors.id, existingContributor.id))
        .returning();

      // Send invitation email
      await sendInviteEmail({
        to: user.email,
        projectName: project.name,
        invitationToken,
        deadline: deadline ? new Date(deadline) : null,
        reminderFrequency: reminderFrequency || 'weekly',
        projectId: parseInt(projectId)
      });

      return res.status(200).json({
        contributor: {
          id: user.id,
          name: user.name,
          email: user.email,
          status: updatedContributor.status,
          invitationToken: updatedContributor.invitationToken,
          invitationSentAt: updatedContributor.invitationSentAt,
          invitationExpiresAt: updatedContributor.invitationExpiresAt,
          deadline: updatedContributor.deadline,
          reminderFrequency: updatedContributor.reminderFrequency
        }
      });
    }

    // Generate invitation token
    const invitationToken = crypto.randomBytes(32).toString('hex');

    // Add user as contributor with invitation details
    const [contributor] = await db.insert(projectContributors).values({
      projectId: parseInt(projectId),
      userId: user.id,
      status: 'pending',
      invitationToken,
      invitationSentAt: now,
      invitationExpiresAt,
      deadline: deadline ? new Date(deadline) : null,
      reminderFrequency: reminderFrequency || 'weekly',
      createdAt: now,
      updatedAt: now
    }).returning();

    // Send invitation email
    await sendInviteEmail({
      to: user.email,
      projectName: project.name,
      invitationToken,
      deadline: deadline ? new Date(deadline) : null,
      reminderFrequency: reminderFrequency || 'weekly',
      projectId: parseInt(projectId)
    });

    res.status(201).json({
      contributor: {
        id: user.id,
        name: user.name,
        email: user.email,
        status: contributor.status,
        invitationToken: contributor.invitationToken,
        invitationSentAt: contributor.invitationSentAt,
        invitationExpiresAt: contributor.invitationExpiresAt,
        deadline: contributor.deadline,
        reminderFrequency: contributor.reminderFrequency
      }
    });
  } catch (error) {
    console.error('Error inviting contributor:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get a single recipe
const getRecipe: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  try {
    const { recipeId } = req.params;
    const userId = authReq.user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the recipe with its project and contributor information
    const recipe = await db.query.recipes.findFirst({
      where: eq(recipes.id, parseInt(recipeId)),
      with: {
        project: {
          columns: {
            id: true,
            organizerId: true
          }
        },
        contributor: {
          columns: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!recipe) {
      return res.status(404).json({ message: 'Recipe not found' });
    }

    res.json({ recipe });
  } catch (error) {
    console.error('Error fetching recipe:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update a recipe
const updateRecipe: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  try {
    const { recipeId } = req.params;
    const userId = authReq.user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    console.log('[Recipe Update] Starting update for recipe:', recipeId);
    console.log('[Recipe Update] Request body:', JSON.stringify(req.body, null, 2));

    // Get the recipe with its project and contributor information
    const recipe = await db.query.recipes.findFirst({
      where: eq(recipes.id, parseInt(recipeId)),
      with: {
        project: {
          columns: {
            id: true,
            organizerId: true
          }
        },
        contributor: {
          columns: {
            id: true
          }
        }
      }
    });

    if (!recipe) {
      console.log('[Recipe Update] Recipe not found:', recipeId);
      return res.status(404).json({ message: 'Recipe not found' });
    }

    // Check if user is either the recipe contributor or the project organizer
    const isContributor = recipe.contributor?.id === userId;
    const isOrganizer = recipe.project?.organizerId === userId;

    if (!isContributor && !isOrganizer) {
      console.log('[Recipe Update] Unauthorized update attempt:', { userId, recipeId, contributorId: recipe.contributor?.id, organizerId: recipe.project?.organizerId });
      return res.status(403).json({ message: 'Not authorized to update this recipe' });
    }

    // Handle image updates
    const currentImages = (recipe.images as string[]) || [];
    const newImages = (req.body.images as string[]) || [];
    console.log('[Recipe Update] Image comparison:', {
      current: currentImages,
      new: newImages,
      removed: currentImages.filter(img => !newImages.includes(img))
    });

    const s3DeletionErrors: Array<{ key: string; error: string }> = [];

    // Find images that were removed
    const removedImages = currentImages.filter(img => !newImages.includes(img));

    // Delete removed images from S3
    if (removedImages.length > 0) {
      console.log('[Recipe Update] Deleting removed images:', removedImages);

      for (const imageKey of removedImages) {
        try {
          // Extract just the key if it's a full URL
          const key = imageKey.includes('http')
            ? imageKey.split('/recipes/')[1]
            : imageKey.startsWith('recipes/')
              ? imageKey
              : `recipes/${imageKey}`;

          console.log(`[Recipe Update] Attempting to delete S3 object with key: ${key}`);
          await deleteObject(key);
          console.log(`[Recipe Update] Successfully deleted S3 object: ${key}`);
        } catch (error) {
          console.error(`[Recipe Update] Error deleting S3 object: ${imageKey}`, error);
          s3DeletionErrors.push({ key: imageKey, error: error instanceof Error ? error.message : 'Unknown error' });
        }
      }
    }

    // Update the recipe
    // Filter out any date fields from req.body to prevent timestamp conversion issues
    const { createdAt, updatedAt, images, ...recipeData } = req.body;

    try {
      // Ensure images array is properly formatted - remove 'recipes/' prefix if it exists
      const formattedImages = (images || []).map((img: string) => {
        // Remove 'recipes/' prefix if it exists
        return img.replace('recipes/', '');
      });

      const [updatedRecipe] = await db.update(recipes)
        .set({
          ...recipeData,
          images: formattedImages,
          updatedAt: new Date()
        })
        .where(eq(recipes.id, parseInt(recipeId)))
        .returning();

      if (!updatedRecipe) {
        console.error('[Recipe Update] Failed to update recipe in database:', recipeId);
        return res.status(500).json({ message: 'Failed to update recipe' });
      }

      console.log('[Recipe Update] Successfully updated recipe:', updatedRecipe.id);
      console.log('[Recipe Update] Updated images:', formattedImages);
      res.json({
        recipe: updatedRecipe,
        s3Errors: s3DeletionErrors.length > 0 ? s3DeletionErrors : undefined
      });
    } catch (dbError) {
      console.error('[Recipe Update] Database error:', dbError);
      return res.status(500).json({
        message: 'Failed to update recipe in database',
        error: dbError instanceof Error ? dbError.message : 'Unknown database error'
      });
    }
  } catch (error) {
    console.error('[Recipe Update] Error updating recipe:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete recipe
const deleteRecipe: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  try {
    const { recipeId } = req.params;
    const userId = authReq.user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the recipe with its project and contributor information
    const recipe = await db.query.recipes.findFirst({
      where: eq(recipes.id, parseInt(recipeId)),
      with: {
        project: {
          columns: {
            id: true,
            organizerId: true
          }
        },
        contributor: {
          columns: {
            id: true
          }
        }
      }
    });

    if (!recipe) {
      return res.status(404).json({ message: 'Recipe not found' });
    }

    // Check if user is either the recipe contributor or the project organizer
    const isContributor = recipe.contributor?.id === userId;
    const isOrganizer = recipe.project?.organizerId === userId;

    if (!isContributor && !isOrganizer) {
      return res.status(403).json({ message: 'Not authorized to delete this recipe' });
    }

    // Delete associated S3 objects (images) if they exist
    const s3DeletionErrors: Array<{ key: string; error: string }> = [];
    const recipeImages = recipe.images as string[] || [];

    if (recipeImages.length > 0) {
      console.log('[Recipe Delete] Starting deletion of associated S3 objects:', recipeImages);

      for (const imageKey of recipeImages) {
        try {
          // Extract just the key if it's a full URL
          const key = imageKey.includes('http')
            ? imageKey.split('/recipes/')[1]
            : imageKey.startsWith('recipes/')
              ? imageKey
              : `recipes/${imageKey}`;

          console.log(`[Recipe Delete] Attempting to delete S3 object with key: ${key}`);
          await deleteObject(key);
          console.log(`[Recipe Delete] Successfully deleted S3 object: ${key}`);
        } catch (error) {
          console.error(`[Recipe Delete] Error deleting S3 object: ${imageKey}`, error);
          s3DeletionErrors.push({ key: imageKey, error: error instanceof Error ? error.message : 'Unknown error' });
        }
      }

      if (s3DeletionErrors.length > 0) {
        console.error('[Recipe Delete] Some S3 objects failed to delete:', s3DeletionErrors);
      }
    }

    // Delete the recipe from the database
    const [deletedRecipe] = await db.delete(recipes)
      .where(eq(recipes.id, parseInt(recipeId)))
      .returning();

    if (!deletedRecipe) {
      return res.status(500).json({ message: 'Failed to delete recipe' });
    }

    res.json({
      message: 'Recipe deleted successfully',
      s3Errors: s3DeletionErrors.length > 0 ? s3DeletionErrors : undefined
    });
  } catch (error) {
    console.error('[Recipe Delete] Error deleting recipe:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Reject recipe
const rejectRecipe: RequestHandler = async (req, res) => {
  const authReq = req as AuthRequest;
  try {
    const { recipeId } = req.params;
    const { rejectionMessage } = req.body;
    const userId = authReq.user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the recipe with its project and contributor information
    const recipe = await db.query.recipes.findFirst({
      where: eq(recipes.id, parseInt(recipeId)),
      with: {
        project: true,
        contributor: true
      }
    });

    if (!recipe) {
      return res.status(404).json({ message: 'Recipe not found' });
    }

    // Check if user is either the project organizer or admin
    const isOrganizer = recipe.project?.organizerId === userId;
    const isAdmin = (await db.query.users.findFirst({
      where: eq(users.id, userId)
    }))?.role === 'admin';

    if (!isOrganizer && !isAdmin) {
      return res.status(403).json({ message: 'Not authorized to reject this recipe' });
    }

    // Delete associated S3 objects (images) if they exist
    const s3DeletionErrors: Array<{ key: string; error: string }> = [];
    const recipeImages = recipe.images as string[] || [];

    if (recipeImages.length > 0) {
      console.log('[Recipe Reject] Starting deletion of associated S3 objects:', recipeImages);

      for (const imageKey of recipeImages) {
        try {
          // Extract just the key if it's a full URL
          const key = imageKey.includes('http')
            ? imageKey.split('/recipes/')[1]
            : imageKey.startsWith('recipes/')
              ? imageKey
              : `recipes/${imageKey}`;

          console.log(`[Recipe Reject] Attempting to delete S3 object with key: ${key}`);
          await deleteObject(key);
          console.log(`[Recipe Reject] Successfully deleted S3 object: ${key}`);
        } catch (error) {
          console.error(`[Recipe Reject] Error deleting S3 object: ${imageKey}`, error);
          s3DeletionErrors.push({ key: imageKey, error: error instanceof Error ? error.message : 'Unknown error' });
        }
      }
    }

    // Create notification for the contributor
    if (recipe.contributorId) {
      await db.insert(notifications).values({
        userId: recipe.contributorId,
        type: 'recipe_rejected',
        message: rejectionMessage || 'Your recipe has been rejected.',
        isRead: false,
        metadata: {
          recipeTitle: recipe.title,
          rejectionMessage: rejectionMessage
        }
      });
    }

    // Delete the recipe from the database
    const [deletedRecipe] = await db.delete(recipes)
      .where(eq(recipes.id, parseInt(recipeId)))
      .returning();

    if (!deletedRecipe) {
      return res.status(500).json({ message: 'Failed to delete recipe' });
    }

    res.json({
      message: 'Recipe rejected successfully',
      s3Errors: s3DeletionErrors.length > 0 ? s3DeletionErrors : undefined
    });
  } catch (error) {
    console.error('[Recipe Reject] Error rejecting recipe:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get organizer's own recipe books
router.get('/my-projects', authMiddleware as RequestHandler, (async (req, res) => {
  try {
    const userId = (req as AuthRequest).user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const userProjects = await db.query.projects.findMany({
      where: eq(projects.organizerId, userId),
      orderBy: (projects, { desc }) => [desc(projects.createdAt)]
    });

    res.json({ projects: userProjects });
  } catch (error) {
    console.error('Error fetching organizer projects:', error);
    res.status(500).json({ message: 'Failed to fetch recipe books' });
  }
}) as RequestHandler);

// Register routes
router.get('/projects', authMiddleware as RequestHandler, getProjects);
router.get('/all-projects', authMiddleware as RequestHandler, getAllProjects);
router.post('/projects', authMiddleware as RequestHandler, createProject);
router.patch('/projects/:projectId', authMiddleware as RequestHandler, updateProject);
router.delete('/projects/:projectId', authMiddleware as RequestHandler, deleteProject);
router.post('/projects/:projectId/contributors', authMiddleware as RequestHandler, addContributor);
router.delete('/projects/:projectId/contributors/:contributorId', authMiddleware as RequestHandler, removeContributor);
router.post('/projects/:projectId/invite', authMiddleware as RequestHandler, requireOrganizer as RequestHandler, inviteContributor);
router.get('/projects/:projectId/recipes', authMiddleware as RequestHandler, getProjectRecipes);
router.get('/recipes/:recipeId', authMiddleware as RequestHandler, getRecipe);
router.patch('/recipes/:recipeId', authMiddleware as RequestHandler, updateRecipe);
router.delete('/recipes/:recipeId', authMiddleware as RequestHandler, deleteRecipe);
router.post('/recipes/:recipeId/reject', authMiddleware as RequestHandler, rejectRecipe);

export default router;