import express from 'express';
import { authMiddleware } from '../middleware/auth.js';
import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { getPresignedUrl } from '../services/s3.js';

const router = express.Router();

// Debug endpoint to check S3 configuration and credentials
router.get('/debug-s3', authMiddleware, async (req, res) => {
  const bucketName = process.env.AWS_BUCKET_NAME || 'recipe-book-images-bucket';
  const region = process.env.AWS_REGION || 'us-east-1';

  // Test URL generation
  const testFilename = '439a7dcc-39ab-4707-8e4c-e6f339814ba6-Chocolate_Chip_Cookies.jfif';
  const testUrl = `https://${bucketName}.s3.${region}.amazonaws.com/recipes/${testFilename}`;

  // Test presigned URL generation
  let presignedUrlTest = null;
  let presignedUrlError = null;

  try {
    console.log('Testing presigned URL generation...');
    const testKey = `recipes/${testFilename}`;
    presignedUrlTest = await getPresignedUrl(testKey);
    console.log('Presigned URL test successful');
  } catch (error) {
    console.error('Presigned URL test failed:', error);
    presignedUrlError = error.message;
  }

  res.json({
    message: 'S3 Configuration Debug',
    config: {
      bucketName,
      region,
      hasAwsBucketName: !!process.env.AWS_BUCKET_NAME,
      hasAwsRegion: !!process.env.AWS_REGION,
      hasAwsAccessKeyId: !!process.env.AWS_ACCESS_KEY_ID,
      hasAwsSecretAccessKey: !!process.env.AWS_SECRET_ACCESS_KEY
    },
    testUrl,
    presignedUrlTest: presignedUrlTest ? presignedUrlTest.substring(0, 200) + '...' : null,
    presignedUrlError,
    instructions: [
      'Check if the test URL above works in your browser',
      'Verify the bucket name and region are correct',
      'Ensure AWS credentials are properly configured',
      'Check if presigned URL generation works'
    ]
  });
});

// Test endpoint to check image accessibility
router.post('/test-images', authMiddleware, async (req, res) => {
  try {
    const { recipes } = req.body;

    if (!recipes || !Array.isArray(recipes)) {
      return res.status(400).json({ message: 'Recipes are required' });
    }

    const imageTests = [];

    // Generate presigned URLs for testing
    console.log('Generating presigned URLs for image testing...');
    const presignedUrlMap = await generatePresignedUrls(recipes);
    console.log(`Generated ${Object.keys(presignedUrlMap).length} presigned URLs for testing`);
    console.log('Presigned URL map contents:', Object.keys(presignedUrlMap).map(key => ({
      key,
      url: presignedUrlMap[key].substring(0, 100) + '...'
    })));

    for (const recipe of recipes) {
      if (recipe.images && recipe.images.length > 0) {
        for (const imageFilename of recipe.images) {
          try {
            console.log('Testing image filename:', imageFilename);

            // Get presigned URL
            console.log('Looking up presigned URL for filename:', imageFilename);
            console.log('Available keys in presignedUrlMap:', Object.keys(presignedUrlMap));
            console.log('Direct lookup result:', presignedUrlMap[imageFilename]);
            console.log('With recipes/ prefix lookup:', presignedUrlMap[`recipes/${imageFilename}`]);

            const presignedUrl = getImageUrl(imageFilename, presignedUrlMap);

            console.log('Final presigned URL from getImageUrl:', presignedUrl);
            console.log('Is this a presigned URL?', presignedUrl.includes('X-Amz-Signature'));

            const response = await fetch(presignedUrl, {
              method: 'GET',
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'image/*,*/*;q=0.8'
              }
            });

            imageTests.push({
              filename: imageFilename,
              url: presignedUrl,
              status: response.status,
              statusText: response.statusText,
              headers: Object.fromEntries(response.headers.entries()),
              accessible: response.ok,
              isPresigned: presignedUrl.includes('X-Amz-Signature')
            });

            console.log(`Image ${presignedUrl}: ${response.status} ${response.statusText}`);

          } catch (error) {
            console.error(`Error testing image ${imageFilename}:`, error.message);
            const presignedUrl = getImageUrl(imageFilename, presignedUrlMap);

            imageTests.push({
              filename: imageFilename,
              url: presignedUrl,
              error: error.message,
              accessible: false,
              isPresigned: presignedUrl.includes('X-Amz-Signature')
            });
          }
        }
      }
    }

    res.json({
      message: 'Image accessibility test completed',
      results: imageTests,
      summary: {
        total: imageTests.length,
        accessible: imageTests.filter(t => t.accessible).length,
        failed: imageTests.filter(t => !t.accessible).length
      }
    });

  } catch (error) {
    console.error('Image test error:', error);
    res.status(500).json({
      message: 'Failed to test images',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Image proxy endpoint to handle CORS and authentication issues
router.get('/proxy-image', async (req, res) => {
  try {
    const { url } = req.query;

    if (!url) {
      return res.status(400).json({ message: 'Image URL is required' });
    }

    console.log('Proxying image:', url);

    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; PDF-Generator/1.0)',
        'Accept': 'image/*'
      }
    });

    if (!response.ok) {
      console.error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      return res.status(response.status).json({
        message: `Failed to fetch image: ${response.statusText}`
      });
    }

    // Set appropriate headers
    const contentType = response.headers.get('content-type') || 'image/jpeg';
    const contentLength = response.headers.get('content-length');

    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    res.setHeader('Access-Control-Allow-Origin', '*');

    if (contentLength) {
      res.setHeader('Content-Length', contentLength);
    }

    // Stream the image data
    const buffer = await response.arrayBuffer();
    res.send(Buffer.from(buffer));

  } catch (error) {
    console.error('Image proxy error:', error);
    res.status(500).json({
      message: 'Failed to proxy image',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper function to generate presigned URLs using the same logic as the upload endpoint
async function generatePresignedUrls(recipes) {
  const allImageFilenames = [];
  const presignedUrlMap = {};

  // Collect all unique image filenames from recipes
  recipes.forEach(recipe => {
    if (recipe.images && Array.isArray(recipe.images)) {
      recipe.images.forEach(filename => {
        if (filename && !allImageFilenames.includes(filename)) {
          allImageFilenames.push(filename);
        }
      });
    }
  });

  console.log(`Found ${allImageFilenames.length} unique images to generate presigned URLs for`);

  if (allImageFilenames.length === 0) {
    return presignedUrlMap;
  }

  try {
    // Prepare image keys with recipes/ prefix (same as frontend)
    const imageKeys = allImageFilenames.map(filename =>
      filename.startsWith('recipes/') ? filename : `recipes/${filename}`
    );

    console.log('Generating presigned URLs for keys:', imageKeys);

    // Use the same logic as the upload endpoint
    const presignedUrls = await Promise.all(
      imageKeys.map(async (key) => {
        try {
          console.log(`Attempting to generate presigned URL for key: ${key}`);
          const url = await getPresignedUrl(key);
          console.log(`Successfully generated presigned URL for ${key}: ${url.substring(0, 100)}...`);
          return { key, url };
        } catch (error) {
          console.error(`Failed to generate presigned URL for ${key}:`, error.message);
          console.error(`Error details:`, error);
          return null;
        }
      })
    );

    // Filter out failed URLs
    const validPresignedUrls = presignedUrls.filter(item => item !== null);

    console.log(`Successfully generated ${validPresignedUrls.length} out of ${imageKeys.length} presigned URLs`);

    // Create a map of image key to presigned URL (same as frontend)
    validPresignedUrls.forEach((item) => {
      // Store both with and without recipes/ prefix for easier lookup
      presignedUrlMap[item.key] = item.url;
      if (item.key.startsWith('recipes/')) {
        presignedUrlMap[item.key.replace('recipes/', '')] = item.url;
      }
      console.log(`Mapped presigned URL for ${item.key}`);
    });

  } catch (error) {
    console.error('Failed to generate presigned URLs:', error);
    // Continue without presigned URLs - will use fallback
  }

  return presignedUrlMap;
}

// Helper function to get image URL from presigned URL map
function getImageUrl(filename, presignedUrlMap) {
  console.log(`getImageUrl called with filename: ${filename}`);
  console.log(`presignedUrlMap has ${Object.keys(presignedUrlMap || {}).length} keys`);

  if (!filename) return null;

  // If it's already a full URL (http/https), return as-is
  if (filename.startsWith('http://') || filename.startsWith('https://')) {
    console.log(`Filename is already a full URL: ${filename}`);
    return filename;
  }

  // Try to find presigned URL
  const presignedUrl = presignedUrlMap[filename] ||
                      presignedUrlMap[`recipes/${filename}`];

  console.log(`Lookup results: direct=${!!presignedUrlMap[filename]}, withPrefix=${!!presignedUrlMap[`recipes/${filename}`]}`);

  if (presignedUrl) {
    console.log(`Found presigned URL for ${filename}: ${presignedUrl.substring(0, 100)}...`);
    return presignedUrl;
  }

  // Fallback to direct S3 URL (won't work for private buckets, but better than nothing)
  console.warn(`No presigned URL found for ${filename}, using fallback`);
  const bucketName = process.env.AWS_BUCKET_NAME || 'recipe-book-images-bucket';
  const region = process.env.AWS_REGION || 'us-east-1';

  const fallbackUrl = `https://${bucketName}.s3.${region}.amazonaws.com/recipes/${filename}`;
  console.log(`Returning fallback URL: ${fallbackUrl}`);
  return fallbackUrl;
}

// Helper function to generate HTML content that matches the preview exactly
function generateBookHTML(recipes, options, metadata, presignedUrlMap = {}) {
  const themes = {
    classic: { colors: { primary: '#8B4513', accent: '#D2691E', heading: '#2C1810', text: '#333333' } },
    modern: { colors: { primary: '#2563EB', accent: '#3B82F6', heading: '#1E40AF', text: '#374151' } },
    rustic: { colors: { primary: '#92400E', accent: '#D97706', heading: '#451A03', text: '#1F2937' } },
    elegant: { colors: { primary: '#7C2D12', accent: '#EA580C', heading: '#431407', text: '#374151' } },
    minimalist: { colors: { primary: '#374151', accent: '#6B7280', heading: '#111827', text: '#4B5563' } }
  };

  const selectedTheme = themes[options.theme] || themes.classic;
  const coverDesigns = {
    classic: { backgroundColor: '#F5F5DC', textColor: '#8B4513' },
    modern: { backgroundColor: '#F8FAFC', textColor: '#1E40AF' },
    rustic: { backgroundColor: '#FEF3C7', textColor: '#92400E' },
    elegant: { backgroundColor: '#FDF2F8', textColor: '#7C2D12' },
    minimalist: { backgroundColor: '#F9FAFB', textColor: '#374151' }
  };
  const selectedCover = coverDesigns[options.cover] || coverDesigns.classic;

  // Helper functions
  const splitIngredients = (ingredients) => {
    const midpoint = Math.ceil(ingredients.length / 2);
    return [ingredients.slice(0, midpoint), ingredients.slice(midpoint)];
  };

  const getStepTitle = (index, instruction) => {
    const stepTitles = [
      'Prepare Ingredients', 'Start Cooking', 'Mix & Combine', 'Cook & Simmer',
      'Add Seasonings', 'Final Touches', 'Plate & Serve', 'Garnish & Enjoy'
    ];
    return stepTitles[index] || `Step ${index + 1}`;
  };

  // Generate cover page
  const generateCoverPage = () => `
    <div class="page cover-page">
      <div class="cover-content">
        <h1 class="cover-title">${options.coverTitle || metadata.title || 'Family Recipe Collection'}</h1>
        <p class="cover-subtitle">${options.coverSubtitle || 'Treasured Recipes'}</p>
      </div>
    </div>
  `;

  // Generate dedication page
  const generateDedicationPage = () => {
    if (!options.includeDedication || !options.dedication) return '';
    return `
      <div class="page dedication-page">
        <div class="dedication-content">
          <h2 class="dedication-title">Dedication</h2>
          <p class="dedication-text">${options.dedication}</p>
        </div>
      </div>
    `;
  };

  // Generate quote pages
  const generateQuotePages = () => {
    if (!options.includeQuotes || !options.familyQuotes || options.familyQuotes.length === 0) return '';
    return options.familyQuotes.map(quote => `
      <div class="page quote-page">
        <div class="quote-content">
          <p class="quote-text">"${quote}"</p>
        </div>
      </div>
    `).join('');
  };

  // Generate recipe pages with smart pagination
  const generateRecipePages = () => {
    return recipes.map((recipe, index) => {
      const [leftIngredients, rightIngredients] = splitIngredients(recipe.ingredients || []);
      const recipeCategory = recipe.tags && recipe.tags.length > 0
        ? `MEALS WITH ${recipe.tags[0].toUpperCase()}`
        : "FAMILY RECIPES";

      const instructions = recipe.instructions || [];

      // Simple content-based pagination (like Test HTML)
      const ingredientCount = (recipe.ingredients || []).length;
      const instructionCount = instructions.length;

      // Calculate total content "weight" - much simpler approach
      const totalContentWeight = ingredientCount + (instructionCount * 2); // Instructions weigh more
      const hasLongInstructions = instructions.some(inst => inst.length > 200);
      const hasVeryLongInstructions = instructions.some(inst => inst.length > 400);

      // Only split if we have truly excessive content
      const needsMultiplePages = (
        (instructionCount > 12) || // Many instructions
        (instructionCount > 8 && hasLongInstructions) || // Medium instructions but long text
        (instructionCount > 6 && hasVeryLongInstructions) || // Fewer but very long instructions
        (totalContentWeight > 25) // High total content weight
      );

      if (needsMultiplePages) {
        // Simple instruction splitting - put most instructions on first page
        let firstPageInstructionCount;

        if (instructionCount > 12) {
          firstPageInstructionCount = Math.floor(instructionCount * 0.7); // 70% on first page
        } else if (instructionCount > 8) {
          firstPageInstructionCount = instructionCount - 4; // Leave 4 for second page
        } else {
          firstPageInstructionCount = instructionCount - 3; // Leave 3 for second page
        }

        // Ensure reasonable split
        firstPageInstructionCount = Math.max(3, Math.min(firstPageInstructionCount, instructionCount - 2));

        const firstPageInstructions = instructions.slice(0, firstPageInstructionCount);
        const remainingInstructions = instructions.slice(firstPageInstructionCount);

        let pages = `
          <div class="page recipe-page">
            <div class="chapter-title">${recipeCategory}</div>
            <h2 class="recipe-title">${recipe.title || 'Untitled Recipe'}</h2>

            <div class="recipe-header">
              <div class="recipe-image-container">
                ${recipe.images && recipe.images.length > 0
                  ? `<img src="${getImageUrl(recipe.images[0], presignedUrlMap)}" alt="${recipe.title}" class="recipe-image" loading="eager" />`
                  : '<div class="no-image-placeholder">No image available</div>'
                }
              </div>

              <div class="recipe-info">
                <p class="recipe-description">${recipe.description || 'A delicious family recipe'}</p>
                <div class="recipe-details">
                  <p><strong>Serves:</strong> ${recipe.servings || 4}</p>
                  <p><strong>Prep Time:</strong> ${recipe.prepTime || 15} minutes</p>
                  <p><strong>Cook Time:</strong> ${recipe.cookTime || 30} minutes</p>
                </div>
              </div>
            </div>

            <h3 class="section-title">Ingredients:</h3>
            <div class="ingredients-container">
              <div class="ingredient-column">
                ${leftIngredients.map(ing => `<div class="ingredient-item">• ${ing.amount || ''} ${ing.unit || ''} ${ing.name || 'Ingredient'}</div>`).join('')}
              </div>
              <div class="ingredient-column">
                ${rightIngredients.map(ing => `<div class="ingredient-item">• ${ing.amount || ''} ${ing.unit || ''} ${ing.name || 'Ingredient'}</div>`).join('')}
              </div>
            </div>

            <h3 class="section-title">Instructions:</h3>
            <div class="instructions-container">
              ${firstPageInstructions.map((instruction, idx) => `
                <div class="instruction-item">
                  <div class="step-number">${idx + 1}</div>
                  <div class="instruction-content">
                    <p class="instruction-text">${instruction}</p>
                  </div>
                </div>
              `).join('')}
            </div>

            <p class="footer">Continued on next page...</p>
          </div>
        `;

        // Add continuation pages for remaining instructions
        // Simple approach - put all remaining instructions on one page unless too many
        if (remainingInstructions.length <= 8) {
          // Single continuation page
          const pageInstructions = remainingInstructions;
          const isLastPage = true;

          pages += `
            <div class="page recipe-page">
              <h2 class="recipe-title">${recipe.title || 'Untitled Recipe'} (Continued)</h2>

              <h3 class="section-title">Instructions (Continued):</h3>
              <div class="instructions-container">
                ${pageInstructions.map((instruction, idx) => `
                  <div class="instruction-item">
                    <div class="step-number">${firstPageInstructions.length + idx + 1}</div>
                    <div class="instruction-content">
                      <p class="instruction-text">${instruction}</p>
                    </div>
                  </div>
                `).join('')}
              </div>

              ${recipe.images && recipe.images.length > 1 ? `
                <h3 class="section-title">Additional Images:</h3>
                <div class="additional-images">
                  ${recipe.images.slice(1, 3).map(image => `
                    <div class="additional-image">
                      <img src="${getImageUrl(image, presignedUrlMap)}" alt="${recipe.title} additional" loading="eager" />
                    </div>
                  `).join('')}
                </div>
              ` : ''}

              <p class="footer">Enjoy your delicious meal!</p>
            </div>
          `;
        } else {
          // Multiple continuation pages for very long recipes
          const instructionsPerPage = 6;
          for (let i = 0; i < remainingInstructions.length; i += instructionsPerPage) {
            const pageInstructions = remainingInstructions.slice(i, i + instructionsPerPage);
            const isLastPage = i + instructionsPerPage >= remainingInstructions.length;

            pages += `
              <div class="page recipe-page">
                <h2 class="recipe-title">${recipe.title || 'Untitled Recipe'} (Continued)</h2>

                <h3 class="section-title">Instructions (Continued):</h3>
                <div class="instructions-container">
                  ${pageInstructions.map((instruction, idx) => `
                    <div class="instruction-item">
                      <div class="step-number">${firstPageInstructions.length + i + idx + 1}</div>
                      <div class="instruction-content">
                        <p class="instruction-text">${instruction}</p>
                      </div>
                    </div>
                  `).join('')}
                </div>

                ${recipe.images && recipe.images.length > 1 && isLastPage ? `
                  <h3 class="section-title">Additional Images:</h3>
                  <div class="additional-images">
                    ${recipe.images.slice(1, 3).map(image => `
                      <div class="additional-image">
                        <img src="${getImageUrl(image, presignedUrlMap)}" alt="${recipe.title} additional" loading="eager" />
                      </div>
                    `).join('')}
                  </div>
                ` : ''}

                <p class="footer">${isLastPage ? 'Enjoy your delicious meal!' : 'Continued on next page...'}</p>
              </div>
            `;
          }
        }

        return pages;
      } else {
        // Single page recipe
        return `
          <div class="page recipe-page">
            <div class="chapter-title">${recipeCategory}</div>
            <h2 class="recipe-title">${recipe.title || 'Untitled Recipe'}</h2>

            <div class="recipe-header">
              <div class="recipe-image-container">
                ${recipe.images && recipe.images.length > 0
                  ? `<img src="${getImageUrl(recipe.images[0], presignedUrlMap)}" alt="${recipe.title}" class="recipe-image" loading="eager" />`
                  : '<div class="no-image-placeholder">No image available</div>'
                }
              </div>

              <div class="recipe-info">
                <p class="recipe-description">${recipe.description || 'A delicious family recipe'}</p>
                <div class="recipe-details">
                  <p><strong>Serves:</strong> ${recipe.servings || 4}</p>
                  <p><strong>Prep Time:</strong> ${recipe.prepTime || 15} minutes</p>
                  <p><strong>Cook Time:</strong> ${recipe.cookTime || 30} minutes</p>
                </div>
              </div>
            </div>

            <h3 class="section-title">Ingredients:</h3>
            <div class="ingredients-container">
              <div class="ingredient-column">
                ${leftIngredients.map(ing => `<div class="ingredient-item">• ${ing.amount || ''} ${ing.unit || ''} ${ing.name || 'Ingredient'}</div>`).join('')}
              </div>
              <div class="ingredient-column">
                ${rightIngredients.map(ing => `<div class="ingredient-item">• ${ing.amount || ''} ${ing.unit || ''} ${ing.name || 'Ingredient'}</div>`).join('')}
              </div>
            </div>

            <h3 class="section-title">Instructions:</h3>
            <div class="instructions-container">
              ${instructions.map((instruction, idx) => `
                <div class="instruction-item">
                  <div class="step-number">${idx + 1}</div>
                  <div class="instruction-content">
                    <p class="instruction-text">${instruction}</p>
                  </div>
                </div>
              `).join('')}
            </div>

            <p class="footer">Enjoy your delicious meal!</p>
          </div>
        `;
      }
    }).join('');
  };

  // Generate complete HTML document
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${metadata.title || 'Family Recipe Collection'}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Arial', sans-serif;
          line-height: 1.6;
          color: ${selectedTheme.colors.text};
          background: white;
        }

        .page {
          page-break-after: always;
          min-height: 100vh;
          padding: 40px;
          position: relative;
          break-after: page;
        }

        .page:last-child {
          page-break-after: avoid;
          break-after: avoid;
        }

        /* Cover Page Styles */
        .cover-page {
          background-color: ${selectedCover.backgroundColor};
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
        }

        .cover-content {
          max-width: 600px;
        }

        .cover-title {
          font-size: 48px;
          font-weight: bold;
          color: ${selectedCover.textColor};
          margin-bottom: 30px;
          line-height: 1.2;
        }

        .cover-subtitle {
          font-size: 24px;
          color: ${selectedCover.textColor};
          opacity: 0.8;
        }

        /* Dedication Page Styles */
        .dedication-page {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
        }

        .dedication-content {
          max-width: 500px;
        }

        .dedication-title {
          font-size: 32px;
          font-weight: bold;
          color: ${selectedTheme.colors.heading};
          margin-bottom: 40px;
        }

        .dedication-text {
          font-size: 18px;
          font-style: italic;
          line-height: 1.8;
          color: ${selectedTheme.colors.text};
        }

        /* Quote Page Styles */
        .quote-page {
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
        }

        .quote-content {
          max-width: 500px;
        }

        .quote-text {
          font-size: 20px;
          font-style: italic;
          line-height: 1.8;
          color: ${selectedTheme.colors.text};
          border-top: 3px solid ${selectedTheme.colors.accent};
          border-bottom: 3px solid ${selectedTheme.colors.accent};
          padding: 40px 0;
        }

        /* Recipe Page Styles */
        .recipe-page {
          padding: 30px;
        }

        .chapter-title {
          background-color: ${selectedTheme.colors.accent};
          color: white;
          text-align: center;
          padding: 15px;
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 30px;
          text-transform: uppercase;
        }

        .recipe-title {
          font-size: 24px;
          font-weight: bold;
          color: ${selectedTheme.colors.heading};
          margin-bottom: 25px;
          text-transform: uppercase;
        }

        .recipe-header {
          display: flex;
          gap: 30px;
          margin-bottom: 30px;
        }

        .recipe-image-container {
          flex: 1;
          max-width: 50%;
        }

        .recipe-image {
          width: 100%;
          height: 250px;
          object-fit: cover;
          border-radius: 8px;
          display: block;
          background-color: #f0f0f0;
        }

        .no-image-placeholder {
          width: 100%;
          height: 250px;
          background-color: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
          border-radius: 8px;
          font-size: 14px;
        }

        .recipe-info {
          flex: 1;
          max-width: 50%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        .recipe-description {
          font-size: 14px;
          line-height: 1.6;
          margin-bottom: 20px;
        }

        .recipe-details p {
          margin-bottom: 8px;
          font-size: 14px;
        }

        .recipe-details strong {
          font-weight: bold;
          color: ${selectedTheme.colors.heading};
        }

        .section-title {
          font-size: 18px;
          font-weight: bold;
          color: ${selectedTheme.colors.heading};
          margin: 20px 0 12px 0;
        }

        .ingredients-container {
          display: flex;
          gap: 40px;
          margin-bottom: 20px;
        }

        .ingredient-column {
          flex: 1;
        }

        .ingredient-item {
          font-size: 12px;
          margin-bottom: 6px;
          padding-left: 15px;
        }

        .instructions-container {
          margin-bottom: 20px;
        }

        .instruction-item {
          display: flex;
          gap: 15px;
          margin-bottom: 15px;
          align-items: flex-start;
          page-break-inside: avoid;
          break-inside: avoid;
        }

        .step-number {
          width: 30px;
          height: 30px;
          background-color: ${selectedTheme.colors.accent};
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 12px;
          flex-shrink: 0;
        }

        .instruction-content {
          flex: 1;
        }

        .instruction-text {
          font-size: 13px;
          line-height: 1.6;
        }

        .footer {
          text-align: center;
          margin-top: 30px;
          font-size: 12px;
          font-style: italic;
          color: ${selectedTheme.colors.accent};
        }

        .additional-images {
          display: flex;
          gap: 15px;
          margin: 25px 0;
          justify-content: center;
        }

        .additional-image {
          flex: 1;
          max-width: 200px;
          height: 150px;
        }

        .additional-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
          display: block;
          background-color: #f0f0f0;
        }

        @media print {
          .page {
            page-break-after: always;
            break-after: page;
            page-break-inside: avoid;
            break-inside: avoid;
          }
          .page:last-child {
            page-break-after: avoid;
            break-after: avoid;
          }

          /* Ensure content doesn't get cut off */
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
        }
      </style>
    </head>
    <body>
      ${generateCoverPage()}
      ${generateDedicationPage()}
      ${generateQuotePages()}
      ${generateRecipePages()}
    </body>
    </html>
  `;
}

// Test endpoint to debug HTML generation
router.post('/test-html', authMiddleware, async (req, res) => {
  try {
    const { recipes, options, metadata } = req.body;

    if (!recipes || !Array.isArray(recipes) || recipes.length === 0) {
      return res.status(400).json({ message: 'Recipes are required' });
    }

    console.log('Generating test HTML...');
    console.log('Recipe images found:');
    recipes.forEach((recipe, idx) => {
      if (recipe.images && recipe.images.length > 0) {
        console.log(`Recipe ${idx + 1} (${recipe.title}):`, recipe.images);
      } else {
        console.log(`Recipe ${idx + 1} (${recipe.title}): No images`);
      }
    });

    // Generate presigned URLs for all images
    console.log('Generating presigned URLs for images...');
    const presignedUrlMap = await generatePresignedUrls(recipes);
    console.log(`Generated ${Object.keys(presignedUrlMap).length} presigned URLs`);

    const htmlContent = generateBookHTML(recipes, options, metadata, presignedUrlMap);

    // Add debugging script to the HTML
    const htmlWithDebug = htmlContent.replace('</body>', `
      <script>
        console.log('HTML loaded, checking images...');

        // Log all image elements
        const images = document.querySelectorAll('img');
        console.log('Found', images.length, 'image elements');

        images.forEach((img, idx) => {
          console.log('Image', idx + 1, ':', {
            src: img.src,
            alt: img.alt,
            complete: img.complete,
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight
          });

          img.onload = function() {
            console.log('Image loaded successfully:', img.src);
          };

          img.onerror = function() {
            console.error('Image failed to load:', img.src);
            console.error('Error details:', this);
          };
        });

        // Check for CORS issues
        fetch(images[0]?.src, { method: 'HEAD', mode: 'cors' })
          .then(response => {
            console.log('CORS test response:', response.status, response.headers);
          })
          .catch(error => {
            console.error('CORS test failed:', error);
          });
      </script>
    </body>`);

    res.setHeader('Content-Type', 'text/html');
    res.send(htmlWithDebug);
  } catch (error) {
    console.error('HTML generation error:', error);
    res.status(500).json({
      message: 'Failed to generate HTML',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate PDF using Puppeteer (server-side rendering)
router.post('/generate', authMiddleware, async (req, res) => {
  let browser = null;

  try {
    const { recipes, options, metadata } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    if (!recipes || !Array.isArray(recipes) || recipes.length === 0) {
      return res.status(400).json({ message: 'Recipes are required' });
    }

    console.log('Starting PDF generation with Puppeteer...');
    console.log(`Generating PDF for ${recipes.length} recipes`);
    console.log('Options:', JSON.stringify(options, null, 2));

    // Launch Puppeteer
    browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();

    // Set viewport for consistent rendering
    await page.setViewport({ width: 1200, height: 1600 });

    // Generate presigned URLs for all images
    console.log('Generating presigned URLs for PDF images...');
    const presignedUrlMap = await generatePresignedUrls(recipes);
    console.log(`Generated ${Object.keys(presignedUrlMap).length} presigned URLs for PDF`);

    // Generate HTML content that matches the preview exactly
    const htmlContent = generateBookHTML(recipes, options, metadata, presignedUrlMap);

    console.log('Generated HTML length:', htmlContent.length);
    console.log('HTML preview:', htmlContent.substring(0, 500) + '...');

    console.log('Setting HTML content...');
    await page.setContent(htmlContent, {
      waitUntil: ['load', 'domcontentloaded', 'networkidle2'],
      timeout: 30000
    });

    // Additional wait for content to fully render
    console.log('Waiting for content to render...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Wait for images to load
    console.log('Waiting for images to load...');
    await page.evaluate(() => {
      return Promise.all(
        Array.from(document.images, img => {
          if (img.complete) return Promise.resolve();
          return new Promise((resolve, reject) => {
            img.addEventListener('load', resolve);
            img.addEventListener('error', resolve); // Resolve even on error to continue
            setTimeout(resolve, 5000); // Timeout after 5 seconds
          });
        })
      );
    });

    // Check if content was loaded
    const bodyContent = await page.evaluate(() => document.body.innerHTML);
    console.log('Body content length:', bodyContent.length);

    const pageCount = await page.evaluate(() => document.querySelectorAll('.page').length);
    console.log('Number of pages in DOM:', pageCount);

    const imageCount = await page.evaluate(() => document.querySelectorAll('img').length);
    console.log('Number of images in DOM:', imageCount);

    // Log page details
    const pageDetails = await page.evaluate(() => {
      const pages = document.querySelectorAll('.page');
      return Array.from(pages).map((page, idx) => ({
        index: idx,
        className: page.className,
        hasContent: page.innerHTML.length > 100,
        contentLength: page.innerHTML.length
      }));
    });
    console.log('Page details:', pageDetails);

    console.log('Images loaded:', await page.evaluate(() =>
      Array.from(document.images).map(img => ({ src: img.src, complete: img.complete, naturalWidth: img.naturalWidth }))
    ));

    // Check if the page has any visible content
    const pageHeight = await page.evaluate(() => document.body.scrollHeight);
    const pageWidth = await page.evaluate(() => document.body.scrollWidth);
    console.log(`Page dimensions: ${pageWidth}x${pageHeight}`);

    // Check if there are any CSS issues
    const hasCSS = await page.evaluate(() => document.styleSheets.length > 0);
    console.log('Has CSS stylesheets:', hasCSS);

    // Take a screenshot for debugging
    console.log('Taking screenshot for debugging...');
    const screenshot = await page.screenshot({
      fullPage: true,
      type: 'png',
      encoding: 'base64'
    });
    console.log('Screenshot taken, size:', screenshot.length, 'characters');

    console.log('Generating PDF...');
    const pdfData = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20px',
        right: '20px',
        bottom: '20px',
        left: '20px'
      },
      preferCSSPageSize: false, // Disable CSS page size to fix 0 pages issue
      timeout: 60000 // Increase timeout
    });

    console.log('PDF generated, size:', pdfData.length, 'bytes');
    console.log('PDF buffer type:', typeof pdfData);
    console.log('PDF is Buffer:', Buffer.isBuffer(pdfData));
    console.log('PDF first 10 bytes:', pdfData.slice(0, 10));

    // Convert to Buffer if it's a Uint8Array
    let pdf;
    if (!Buffer.isBuffer(pdfData)) {
      console.log('Converting Uint8Array to Buffer...');
      pdf = Buffer.from(pdfData);
      console.log('Converted to Buffer, size:', pdf.length, 'bytes');
    } else {
      pdf = pdfData;
    }

    // If PDF is very small, it might be empty
    if (pdf.length < 1000) {
      console.error('PDF is suspiciously small, might be empty');
      console.log('Body content preview:', bodyContent.substring(0, 1000));
      console.log('Page count in DOM was:', pageCount);
      console.log('Image count in DOM was:', imageCount);

      // Try to get more debugging info
      const debugInfo = await page.evaluate(() => {
        return {
          documentReady: document.readyState,
          bodyHeight: document.body.scrollHeight,
          bodyWidth: document.body.scrollWidth,
          hasPages: document.querySelectorAll('.page').length > 0,
          firstPageContent: document.querySelector('.page')?.innerHTML?.substring(0, 200) || 'No first page found'
        };
      });
      console.log('Debug info:', debugInfo);

      throw new Error(`Generated PDF is too small (${pdf.length} bytes), likely empty. Check logs for details.`);
    }

    // Verify PDF header
    const pdfHeader = pdf.slice(0, 4).toString('ascii');
    console.log('PDF header:', pdfHeader);
    if (pdfHeader !== '%PDF') {
      console.error('Invalid PDF header:', pdfHeader);
      console.error('Expected: %PDF, Got:', pdfHeader);
      throw new Error('Generated file is not a valid PDF');
    }

    console.log('PDF header verified:', pdfHeader);

    await browser.close();
    browser = null;

    console.log('PDF generated successfully');

    // Send PDF as response
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="family-recipe-collection.pdf"');
    res.setHeader('Content-Length', pdf.length);
    res.setHeader('Cache-Control', 'no-cache');

    // Send as buffer to ensure binary data integrity
    res.end(pdf, 'binary');

  } catch (error) {
    console.error('PDF generation error:', error);

    // Clean up browser if it's still running
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
      }
    }

    res.status(500).json({
      message: 'Failed to generate PDF',
      error: error instanceof Error ? error.message : 'Unknown error',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

export default router;
