import express from 'express';
import { and, eq, desc } from 'drizzle-orm';
import { db } from '../db.js';
import { recipes as recipesTable, projectContributors, users, projects } from '../schema.js';
import { authMiddleware } from '../middleware/auth.js';

const router = express.Router();

// Get all recipes for a project
router.get('/project/:projectId', authMiddleware, async (req, res) => {
  try {
    const { projectId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if the user has access to this project
    const userAccess = await db.query.projectContributors.findFirst({
      where: and(
        eq(projectContributors.projectId, parseInt(projectId)),
        eq(projectContributors.userId, userId),
        eq(projectContributors.status, 'accepted')
      )
    });

    // If not a contributor, check if user is the organizer of the project
    const isOrganizer = await db.query.projects.findFirst({
      where: and(
        eq(projects.id, parseInt(projectId)),
        eq(projects.organizerId, userId)
      )
    });

    if (!userAccess && !isOrganizer) {
      return res.status(403).json({ error: 'You do not have access to this project' });
    }

    // Get all recipes for the project
    const projectRecipes = await db.select({
      id: recipesTable.id,
      projectId: recipesTable.projectId,
      contributorId: recipesTable.contributorId,
      title: recipesTable.title,
      description: recipesTable.description,
      images: recipesTable.images,
      ingredients: recipesTable.ingredients,
      instructions: recipesTable.instructions,
      tags: recipesTable.tags,
      category: recipesTable.category,
      measurementSystem: recipesTable.measurementSystem,
      createdAt: recipesTable.createdAt,
      updatedAt: recipesTable.updatedAt,
      status: recipesTable.status,
      role: recipesTable.role,
      contributor: {
        id: users.id,
        name: users.name,
        email: users.email,
        role: users.role
      }
    })
    .from(recipesTable)
    .leftJoin(users, eq(recipesTable.contributorId, users.id))
    .where(
      and(
        eq(recipesTable.projectId, parseInt(projectId)),
        eq(recipesTable.status, 'approved')
      )
    )
    .orderBy(desc(recipesTable.createdAt));

    // Log the raw SQL query
    console.log('SQL Query:', db.select().from(recipesTable).toSQL());

    // Log the first recipe with all its fields
    if (projectRecipes.length > 0) {
      console.log('First recipe raw data:', JSON.stringify(projectRecipes[0], null, 2));
      console.log('Recipe status:', projectRecipes[0].status);
    } else {
      console.log('No recipes found');
    }

    res.json({ recipes: projectRecipes });
  } catch (error) {
    console.error('Error fetching recipes:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get a single recipe by ID
router.get('/:recipeId', authMiddleware, async (req, res) => {
  try {
    const { recipeId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the recipe with contributor info
    const recipe = await db.query.recipes.findFirst({
      where: eq(recipesTable.id, parseInt(recipeId)),
      with: {
        contributor: {
          columns: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        project: true
      }
    });

    if (!recipe) {
      return res.status(404).json({ error: 'Recipe not found' });
    }

    // Check if user has access to this recipe
    const userAccess = await db.query.projectContributors.findFirst({
      where: and(
        eq(projectContributors.projectId, recipe.projectId || 0),
        eq(projectContributors.userId, userId),
        eq(projectContributors.status, 'accepted')
      )
    });

    // If not a contributor, check if user is the organizer of the project
    const isOrganizer = await db.query.projects.findFirst({
      where: and(
        eq(projects.id, recipe.projectId || 0),
        eq(projects.organizerId, userId)
      )
    });

    const isContributor = recipe.contributorId === userId;

    if (!userAccess && !isOrganizer && !isContributor) {
      return res.status(403).json({ error: 'You do not have access to this recipe' });
    }

    res.json({ recipe });
  } catch (error) {
    console.error('Error fetching recipe:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
