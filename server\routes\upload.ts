import { Router, Response, RequestHandler } from 'express';
import { authMiddleware, AuthRequest } from '../middleware/auth.js';
import { getUploadUrl, getPresignedUrl } from '../services/s3.js';

const router = Router();

// Handler function for generating signed URL for upload
const generateSignedUrl: RequestHandler = async (req, res) => {
  try {
    const { user } = req as AuthRequest;
    const { fileName, fileType } = req.body;

    if (!fileName || !fileType) {
      return res.status(400).json({ error: 'fileName and fileType are required' });
    }

    const signedUrl = await getUploadUrl(fileName, fileType);
    const key = signedUrl.split('?')[0].split('/').pop();

    return res.json({ signedUrl, key });
  } catch (error) {
    console.error('Error generating signed URL:', error);
    return res.status(500).json({ error: 'Failed to generate upload URL' });
  }
};

// Handler function for getting presigned URLs for viewing
const getPresignedUrls: RequestHandler = async (req, res) => {
  try {
    const { keys } = req.body;

    if (!Array.isArray(keys)) {
      return res.status(400).json({ error: 'keys must be an array of strings' });
    }

    const presignedUrls = await Promise.all(
      keys.map(async (key) => ({
        key,
        url: await getPresignedUrl(key)
      }))
    );

    return res.json({ presignedUrls });
  } catch (error) {
    console.error('Error generating presigned URLs:', error);
    return res.status(500).json({ error: 'Failed to generate presigned URLs' });
  }
};

// Handler function for generating signed URL for audio upload
const generateAudioUploadUrl: RequestHandler = async (req, res) => {
  try {
    const { user } = req as AuthRequest;
    const { fileName, fileType } = req.body;

    if (!fileName || !fileType) {
      return res.status(400).json({ error: 'fileName and fileType are required' });
    }

    if (!fileType.startsWith('audio/')) {
      return res.status(400).json({ error: 'fileType must be an audio format' });
    }

    console.log(`[UPLOAD] Generating upload URL for audio file: ${fileName} (${fileType})`);
    
    // Use a different prefix for audio files
    const uploadUrl = await getUploadUrl(`audio/${fileName}`, fileType);
    
    // Extract the key from the URL (everything after the bucket name in the path)
    const urlParts = uploadUrl.split('?')[0].split('/');
    const bucketIndex = urlParts.findIndex(part => part.includes('recipe-book-images-bucket'));
    const key = urlParts.slice(bucketIndex + 1).join('/');
    
    console.log(`[UPLOAD] Generated upload URL with key: ${key}`);

    return res.json({ uploadUrl, key });
  } catch (error) {
    console.error('Error generating audio upload URL:', error);
    return res.status(500).json({ error: 'Failed to generate audio upload URL' });
  }
};

// Generate signed URL for S3 upload (images)
router.post('/signed-url', authMiddleware as RequestHandler, generateSignedUrl);

// Generate signed URL for S3 upload (audio)
router.post('/audio-url', authMiddleware as RequestHandler, generateAudioUploadUrl);

// Get presigned URLs for viewing images
router.post('/presigned-urls', authMiddleware as RequestHandler, getPresignedUrls);

export default router; 