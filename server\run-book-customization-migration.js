import postgres from 'postgres';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get current file directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

// Configure logging
const LOG_FILE = path.join(__dirname, 'book-customization-migration.log');
const MAX_RETRIES = 5;
const RETRY_DELAY = 3000; // 3 seconds

/**
 * Write a log message to both console and log file
 */
function log(message, isError = false) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;

  // Log to console
  if (isError) {
    console.error(logMessage);
  } else {
    console.log(logMessage);
  }

  // Log to file
  try {
    fs.appendFileSync(LOG_FILE, logMessage);
  } catch (error) {
    console.error(`Failed to write to log file: ${error.message}`);
  }
}

/**
 * Initialize the log file
 */
function initLogFile() {
  try {
    fs.writeFileSync(LOG_FILE, `Book Customization Migration Log - Started at ${new Date().toISOString()}\n\n`);
    log('Log file initialized');
  } catch (error) {
    console.error(`Failed to initialize log file: ${error.message}`);
  }
}

/**
 * Run the migration with retries
 */
async function runMigration(retryCount = 0) {
  try {
    log(`Attempting to run migration (Attempt ${retryCount + 1})`);

    // Check if DATABASE_URL is defined
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL is not defined');
    }

    log(`Connecting to database: ${process.env.DATABASE_URL.includes('@') ? process.env.DATABASE_URL.split('@')[1] : 'DB'}`);

    // Create postgres client with SSL options
    // The error message indicates we need to use sslmode=require
    let connectionString = process.env.DATABASE_URL;

    // Add sslmode=require if not already present
    if (!connectionString.includes('sslmode=require')) {
      connectionString += connectionString.includes('?')
        ? '&sslmode=require'
        : '?sslmode=require';
    }

    log(`Using connection string with SSL: ${connectionString.includes('@') ? connectionString.split('@')[1] : 'DB'}`);

    const client = postgres(connectionString, {
      ssl: { rejectUnauthorized: false },
      max: 1
    });

    log('Database connection established');

    // Check if the table exists
    log('Checking if projects table exists');
    const tableExists = await client`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'projects'
      );
    `;

    if (!tableExists[0].exists) {
      throw new Error('Table "projects" does not exist');
    }

    log('Table "projects" exists, proceeding with migration');

    // Check if any of the columns already exist to avoid duplicate column errors
    log('Checking existing columns');
    const columnInfo = await client`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'projects';
    `;

    const existingColumns = columnInfo.map(col => col.column_name);
    log(`Existing columns: ${existingColumns.join(', ')}`);

    const columnsToAdd = [
      'theme', 'font', 'chapter_style', 'cover', 'cover_title', 'cover_subtitle',
      'cover_image', 'use_custom_cover_image', 'dedication', 'include_dedication',
      'include_quotes', 'family_quotes'
    ];

    const columnsToAddFiltered = columnsToAdd.filter(col => !existingColumns.includes(col));
    log(`Columns to add: ${columnsToAddFiltered.join(', ')}`);

    if (columnsToAddFiltered.length === 0) {
      log('All columns already exist, migration complete');
      return true;
    }

    // Build the ALTER TABLE statement dynamically
    let alterTableQuery = 'ALTER TABLE projects';

    columnsToAddFiltered.forEach((column, index) => {
      if (index > 0) {
        alterTableQuery += ',';
      }

      alterTableQuery += ' ADD COLUMN ';

      switch (column) {
        case 'theme':
          alterTableQuery += `${column} TEXT DEFAULT 'classic'`;
          break;
        case 'font':
          alterTableQuery += `${column} TEXT DEFAULT 'elegant'`;
          break;
        case 'chapter_style':
          alterTableQuery += `${column} TEXT DEFAULT 'simple'`;
          break;
        case 'cover':
          alterTableQuery += `${column} TEXT DEFAULT 'classic'`;
          break;
        case 'cover_title':
        case 'cover_subtitle':
          alterTableQuery += `${column} TEXT`;
          break;
        case 'cover_image':
        case 'dedication':
          alterTableQuery += `${column} TEXT`;
          break;
        case 'use_custom_cover_image':
        case 'include_dedication':
        case 'include_quotes':
          alterTableQuery += `${column} BOOLEAN DEFAULT false`;
          break;
        case 'family_quotes':
          alterTableQuery += `${column} JSONB DEFAULT '[]'`;
          break;
        default:
          alterTableQuery += `${column} TEXT`;
      }
    });

    alterTableQuery += ';';

    log(`Executing query: ${alterTableQuery}`);

    // Execute the ALTER TABLE statement
    await client.unsafe(alterTableQuery);

    log('Migration completed successfully!');

    // Close the database connection
    await client.end();
    log('Database connection closed');

    return true;
  } catch (error) {
    log(`Migration failed: ${error.message}`, true);
    log(`Stack trace: ${error.stack}`, true);

    // Check if we should retry
    if (retryCount < MAX_RETRIES) {
      const nextRetry = retryCount + 1;
      log(`Will retry in ${RETRY_DELAY / 1000} seconds (Retry ${nextRetry}/${MAX_RETRIES})`);

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));

      // Retry recursively
      return runMigration(nextRetry);
    } else {
      log(`Maximum retries (${MAX_RETRIES}) reached. Migration failed.`, true);
      return false;
    }
  }
}

/**
 * Main function to run the migration
 */
async function main() {
  initLogFile();
  log('Starting book customization migration process');

  const success = await runMigration();

  if (success) {
    log('Migration process completed successfully');
    process.exit(0);
  } else {
    log('Migration process failed after maximum retries', true);
    process.exit(1);
  }
}

// Run the main function
main().catch(async (error) => {
  log(`Unhandled error in main process: ${error.message}`, true);
  log(`Stack trace: ${error.stack}`, true);
  process.exit(1);
});
