import { db } from '../db.js';
import { recipes, users } from '../schema.js';
import { eq } from 'drizzle-orm';

async function checkRecipes() {
  try {
    // Get all recipes
    const allRecipes = await db.select().from(recipes);
    console.log('All recipes:', allRecipes);

    // Get all users
    const allUsers = await db.select().from(users);
    console.log('All users:', allUsers);

    // Get recipes for each user
    for (const user of allUsers) {
      const userRecipes = await db.select()
        .from(recipes)
        .where(eq(recipes.contributorId, user.id));
      console.log(`Recipes for user ${user.name} (${user.id}):`, userRecipes);
    }
  } catch (error) {
    console.error('Error checking recipes:', error);
  }
}

checkRecipes(); 