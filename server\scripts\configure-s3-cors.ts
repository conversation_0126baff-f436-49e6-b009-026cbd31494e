import { S3Client, PutBucketCorsCommand } from "@aws-sdk/client-s3";
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

dotenv.config();

const s3Client = new S3Client({
    region: process.env.AWS_REGION || 'eu-north-1',
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
    }
});

async function configureBucketCors() {
    try {
        const corsConfig = JSON.parse(
            fs.readFileSync(
                path.join(__dirname, '..', 's3-cors-config.json'),
                'utf-8'
            )
        );

        const command = new PutBucketCorsCommand({
            Bucket: process.env.AWS_BUCKET_NAME || 'recipe-book-images-bucket',
            CORSConfiguration: corsConfig
        });

        await s3Client.send(command);
        console.log('Successfully updated CORS configuration for bucket');
    } catch (error) {
        console.error('Error updating CORS configuration:', error);
        process.exit(1);
    }
}

configureBucketCors(); 