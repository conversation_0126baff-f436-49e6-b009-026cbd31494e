import sgMail from '@sendgrid/mail';
import { z } from 'zod';

// Initialize SendGrid with API key
if (!process.env.SENDGRID_API_KEY) {
  console.error('SENDGRID_API_KEY is not set in environment variables');
  process.exit(1);
}

sgMail.setApiKey(process.env.SENDGRID_API_KEY);

interface EmailOptions {
  to: string;
  subject: string;
  text: string;
  html: string;
}

export async function sendEmail({ to, subject, text, html }: EmailOptions) {
  try {
    if (!process.env.SENDGRID_FROM_EMAIL) {
      throw new Error('SENDGRID_FROM_EMAIL is not set in environment variables');
    }

    const msg = {
      to,
      from: process.env.SENDGRID_FROM_EMAIL,
      subject,
      text,
      html,
    };

    await sgMail.send(msg);
    console.log(`Email sent successfully to ${to}`);
  } catch (error) {
    console.error('Error sending email:', error);
    if (error instanceof Error && 'response' in error) {
      console.error('SendGrid API Error:', (error as any).response?.body);
    } else {
      console.error('SendGrid Error:', error);
    }
    throw error;
  }
}

export async function sendInviteEmail({ to, projectName, invitationToken, deadline, reminderFrequency, projectId }: {
  to: string;
  projectName: string;
  invitationToken: string;
  deadline?: Date | null;
  reminderFrequency?: string;
  projectId: number;
}) {
  const subject = `You've been invited to contribute to ${projectName}`;
  const deadlineText = deadline ? `\n\nYour recipe submission is due by: ${deadline.toLocaleDateString()}` : '';
  const reminderText = reminderFrequency ? `\n\nYou will receive reminders ${reminderFrequency} until the deadline.` : '';
  const text = `Hello,\n\nYou have been invited to contribute to the recipe book "${projectName}". Please click the link below to accept the invitation and start contributing.${deadlineText}${reminderText}\n\nBest regards,\nThe RecipeBook Team`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2E4B7A;">You've been invited to contribute!</h2>
      <p>Hello,</p>
      <p>You have been invited to contribute to the recipe book "${projectName}".</p>
      <p>Please click the button below to accept the invitation and start contributing.</p>
      ${deadline ? `<p><strong>Your recipe submission is due by:</strong> ${deadline.toLocaleDateString()}</p>` : ''}
      ${reminderFrequency ? `<p><strong>Reminder frequency:</strong> ${reminderFrequency}</p>` : ''}
      <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.CLIENT_URL}/accept-invite?projectId=${projectId}&token=${invitationToken}" style="background-color: #9B7A5D; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Accept Invitation</a>
      </div>
      <p>This invitation will expire in 7 days.</p>
      <p>Best regards,<br>The RecipeBook Team</p>
    </div>
  `;

  await sendEmail({ to, subject, text, html });
}

export async function sendNotificationEmail(to: string, subject: string, message: string) {
  const text = `Hello,\n\n${message}\n\nBest regards,\nThe RecipeBook Team`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #2E4B7A;">${subject}</h2>
      <p>Hello,</p>
      <p>${message}</p>
      <p>Best regards,<br>The RecipeBook Team</p>
    </div>
  `;

  await sendEmail({ to, subject, text, html });
} 