import fetch from 'node-fetch';
import crypto from 'crypto';

interface IntercomUser {
  type: 'user';
  id?: string;
  user_id?: string;
  email?: string;
  name?: string;
  phone?: string;
  avatar?: {
    type: 'avatar';
    image_url: string;
  };
  signed_up_at?: number;
  last_seen_at?: number;
  owner_id?: number;
  unsubscribed_from_emails?: boolean;
  custom_attributes?: Record<string, any>;
}

interface IntercomContact {
  type: 'contact';
  id?: string;
  external_id?: string;
  email?: string;
  name?: string;
  phone?: string;
  avatar?: {
    type: 'avatar';
    image_url: string;
  };
  signed_up_at?: number;
  last_seen_at?: number;
  owner_id?: number;
  unsubscribed_from_emails?: boolean;
  custom_attributes?: Record<string, any>;
}

interface IntercomConversation {
  type: 'admin_initiated_conversation' | 'user_initiated_conversation';
  id?: string;
  from: {
    type: 'user' | 'admin' | 'contact';
    id: string;
  };
  body: string;
  message_type?: 'comment' | 'note';
}

class IntercomService {
  private accessToken: string;
  private baseUrl = 'https://api.intercom.io';

constructor() {
   this.accessToken = process.env.INTERCOM_ACCESS_TOKEN || '';
   if (!this.accessToken) {
    throw new Error('Intercom access token not found. Set INTERCOM_ACCESS_TOKEN environment variable.');
   }
 }

  private async makeRequest(endpoint: string, options: any = {}) {
    if (!this.accessToken) {
      throw new Error('Intercom access token not configured');
    }

    const url = `${this.baseUrl}${endpoint}`;

    const requestOptions = {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('Making Intercom API request:');
      console.log('URL:', url);
      console.log('Method:', requestOptions.method || 'GET');
      // Sanitize headers to hide auth token
      const sanitizedHeaders = { ...requestOptions.headers };
      if (sanitizedHeaders.Authorization) {
        sanitizedHeaders.Authorization = 'Bearer [REDACTED]';
      }
      console.log('Headers:', sanitizedHeaders);
      console.log('Body:', requestOptions.body);
    }

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Intercom API error response:', errorText);
      throw new Error(`Intercom API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = response.status === 204 ? undefined : await response.json();
    if (process.env.NODE_ENV === 'development') {
      console.log('Intercom API success response:', result);
    }
    return result;
  }

  // User Management
  async createOrUpdateUser(userData: Partial<IntercomUser>) {
    try {
      return await this.makeRequest('/users', {
        method: 'POST',
        body: JSON.stringify(userData),
      });
    } catch (error) {
      console.error('Error creating/updating Intercom user:', error);
      throw error;
    }
  }

  async getUser(userId: string) {
    try {
      return await this.makeRequest(`/users/${userId}`);
    } catch (error) {
      console.error('Error fetching Intercom user:', error);
      throw error;
    }
  }

  async getUserByEmail(email: string) {
    try {
      return await this.makeRequest(`/users?email=${encodeURIComponent(email)}`);
    } catch (error) {
      console.error('Error fetching Intercom user by email:', error);
      throw error;
    }
  }

  async deleteUser(userId: string) {
    try {
      return await this.makeRequest(`/users/${userId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Error deleting Intercom user:', error);
      throw error;
    }
  }

  // Contact Management
  async createOrUpdateContact(contactData: Partial<IntercomContact>) {
    try {
      return await this.makeRequest('/contacts', {
        method: 'POST',
        body: JSON.stringify(contactData),
      });
    } catch (error) {
      console.error('Error creating/updating Intercom contact:', error);
      throw error;
    }
  }

  // Admin Management
  async getCurrentAdmin() {
    try {
      return await this.makeRequest('/me');
    } catch (error) {
      console.error('Error fetching current admin:', error);
      throw error;
    }
  }

  async getAdmins() {
    try {
      return await this.makeRequest('/admins');
    } catch (error) {
      console.error('Error fetching admins:', error);
      throw error;
    }
  }

  // Conversation Management
  async createConversation(conversationData: Partial<IntercomConversation>) {
    try {
      return await this.makeRequest('/conversations', {
        method: 'POST',
        body: JSON.stringify(conversationData),
      });
    } catch (error) {
      console.error('Error creating Intercom conversation:', error);
      throw error;
    }
  }

  async getConversations(userId?: string, page?: number, per_page?: number) {
    try {
      // Build endpoint with pagination parameters
      let endpoint = userId
        ? `/conversations?intercom_user_id=${userId}`
        : '/conversations';

      // Add pagination parameters if provided
      if (page && per_page) {
        const separator = endpoint.includes('?') ? '&' : '?';
        endpoint += `${separator}page=${page}&per_page=${per_page}`;
      }

      const response = await this.makeRequest(endpoint);

// Fetch detailed conversation data for each conversation
 if (response.conversations) {
  // Limit concurrent requests to avoid rate limiting
  const batchSize = 5;
  const detailedConversations: any[] = [];

  for (let i = 0; i < response.conversations.length; i += batchSize) {
    const batch = response.conversations.slice(i, i + batchSize);
    const batchResults = await Promise.all(
      batch.map(async (conv: any) => {
       try {
         const detailed = await this.getConversationDetails(conv.id);
         return detailed;
       } catch (error) {
         console.error(`Error fetching details for conversation ${conv.id}:`, error);
         return conv; // Return basic conversation if details fail
       }
      })
    );
    detailedConversations.push(...batchResults);
  }

        // Since we're only fetching open conversations, no additional filtering needed
        response.conversations = detailedConversations;
      }

      return response;
    } catch (error) {
      console.error('Error fetching Intercom conversations:', error);
      throw error;
    }
  }

  async getConversationDetails(conversationId: string) {
    try {
      return await this.makeRequest(`/conversations/${conversationId}`);
    } catch (error) {
      console.error('Error fetching Intercom conversation details:', error);
      throw error;
    }
  }

  async replyToConversation(conversationId: string, message: string, messageType: 'comment' | 'note' = 'comment', adminId?: string) {
    try {
      console.log('Attempting to reply to conversation:', conversationId, 'with message:', message);

      // Get admin ID if not provided
      let currentAdminId = adminId;
      if (!currentAdminId) {
        try {
          const currentAdmin = await this.getCurrentAdmin();
          currentAdminId = currentAdmin.id;
          console.log('Using current admin ID:', currentAdminId);
        } catch (adminError) {
          console.error('Failed to get current admin, trying without admin_id:', adminError);
        }
      }

      // Build request body according to Intercom API requirements
const requestBody: any = {
  type: 'admin',
  message_type: messageType,
        body: message,
      };

      // Add admin_id if available (required for admin replies)
      if (currentAdminId) {
        requestBody.admin_id = currentAdminId;
      }

      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      return await this.makeRequest(`/conversations/${conversationId}/reply`, {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });
    } catch (error) {
      console.error('Error replying to Intercom conversation:', error);
      console.error('Conversation ID:', conversationId);
      console.error('Message:', message);
      console.error('Message Type:', messageType);
      throw error;
    }
  }

  async deleteConversation(conversationId: string, adminId?: string) {
    try {
      console.log('Attempting to delete/close conversation:', conversationId);

      // Get admin ID if not provided
      let currentAdminId = adminId;
      if (!currentAdminId) {
        try {
          const currentAdmin = await this.getCurrentAdmin();
          currentAdminId = currentAdmin.id;
          console.log('Using current admin ID for closing conversation:', currentAdminId);
        } catch (adminError) {
          console.error('Failed to get current admin for closing conversation:', adminError);
        }
      }

      // Use the reply endpoint to close the conversation
      const requestBody: any = {
        message_type: 'close',
        type: 'admin',
      };

      // Add admin_id if available (required for admin actions)
      if (currentAdminId) {
        requestBody.admin_id = currentAdminId;
      }

      console.log('Close conversation request body:', JSON.stringify(requestBody, null, 2));

      // Close the conversation using the reply endpoint with close message type
      const result = await this.makeRequest(`/conversations/${conversationId}/reply`, {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      console.log('Close conversation result:', result);
      return result;
    } catch (error) {
      console.error('Error closing Intercom conversation:', error);
      console.error('Conversation ID:', conversationId);
      throw error;
    }
  }

  // Events and Analytics
  async trackEvent(userId: string, eventName: string, metadata?: Record<string, any>) {
    try {
      return await this.makeRequest('/events', {
        method: 'POST',
        body: JSON.stringify({
          event_name: eventName,
          user_id: userId,
          metadata: metadata || {},
          created_at: Math.floor(Date.now() / 1000),
        }),
      });
    } catch (error) {
      console.error('Error tracking Intercom event:', error);
      throw error;
    }
  }

  // Sync user from your database to Intercom
  async syncUser(user: any) {
    try {
      const intercomUser: Partial<IntercomUser> = {
        user_id: user.id?.toString(),
        email: user.email,
        name: user.name || user.email,
        signed_up_at: user.createdAt ? Math.floor(new Date(user.createdAt).getTime() / 1000) : Math.floor(Date.now() / 1000),
        custom_attributes: {
          role: user.role,
          plan: 'free', // Customize based on your user model
          app_version: '1.0.0',
          last_login: user.lastLoginAt ? Math.floor(new Date(user.lastLoginAt).getTime() / 1000) : undefined,
        },
      };

      return await this.createOrUpdateUser(intercomUser);
    } catch (error) {
      console.error('Error syncing user to Intercom:', error);
      throw error;
    }
  }

  // Webhook verification
  verifyWebhook(body: string, signature: string, secret: string): boolean {
    const expectedSignature = crypto
      .createHmac('sha1', secret)
      .update(body)
      .digest('hex');

    return signature === `sha1=${expectedSignature}`;
  }
}

export const intercomService = new IntercomService();
