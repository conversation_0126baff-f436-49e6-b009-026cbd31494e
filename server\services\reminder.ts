import { db } from '../db.js';
import { projectContributors } from '../schema.js';
import { eq, and, lt, or, isNull } from 'drizzle-orm';
import { sendNotificationEmail } from './email.js';

type ReminderFrequency = '1min' | '2min' | '5min' | '15min' | '30min' | '1hour' | 'daily' | 'weekly' | 'biweekly' | 'monthly';

// Helper function to get milliseconds for a reminder frequency
function getReminderInterval(frequency: string): number {
  switch (frequency) {
    case '1min': return 60 * 1000; // 1 minute
    case '2min': return 2 * 60 * 1000; // 2 minutes
    case '5min': return 5 * 60 * 1000; // 5 minutes
    case '15min': return 15 * 60 * 1000; // 15 minutes
    case '30min': return 30 * 60 * 1000; // 30 minutes
    case '1hour': return 60 * 60 * 1000; // 1 hour
    case 'daily': return 24 * 60 * 60 * 1000; // 24 hours
    case 'weekly': return 7 * 24 * 60 * 60 * 1000; // 7 days
    case 'biweekly': return 14 * 24 * 60 * 60 * 1000; // 14 days
    case 'monthly': return 30 * 24 * 60 * 60 * 1000; // 30 days
    default: return 7 * 24 * 60 * 60 * 1000; // Default to weekly
  }
}

export async function checkAndSendReminders() {
  console.log('Checking for reminders to send...');
  const now = new Date();

  try {
    // Get all pending contributors that need reminders
    const pendingContributors = await db.query.projectContributors.findMany({
      where: and(
        eq(projectContributors.status, 'pending'),
        or(
          // No reminder sent yet
          isNull(projectContributors.lastReminderSentAt),
          // Last reminder was sent more than the reminder frequency ago
          lt(projectContributors.lastReminderSentAt, new Date(now.getTime() - getReminderInterval(String(projectContributors.reminderFrequency))))
        )
      ),
      with: {
        user: true,
        project: {
          with: {
            organizer: true
          }
        }
      }
    });

    console.log(`Found ${pendingContributors.length} contributors needing reminders`);

    for (const contributor of pendingContributors) {
      if (!contributor.user || !contributor.project || !contributor.project.organizer) {
        console.error('Missing required data for contributor:', contributor.id);
        continue;
      }

      try {
        console.log(`Sending reminder to contributor ${contributor.user.email} for project ${contributor.project.name}`);

        // Send reminder email
        const subject = `Reminder: Contribute to ${contributor.project.name}`;
        const message = `
          This is a reminder that you have been invited to contribute to ${contributor.project.name} by ${contributor.project.organizer.name}.
          Click the link below to accept the invitation:
          ${process.env.FRONTEND_URL}/accept-invitation/${contributor.invitationToken}
          ${contributor.deadline ? `\nDeadline: ${new Date(contributor.deadline).toLocaleDateString()}` : ''}
        `;

        await sendNotificationEmail(contributor.user.email, subject, message);

        // Update last reminder sent timestamp
        await db.update(projectContributors)
          .set({
            lastReminderSentAt: now,
            updatedAt: now
          })
          .where(eq(projectContributors.id, contributor.id));

        console.log(`Successfully sent reminder to ${contributor.user.email}`);
      } catch (error) {
        console.error(`Error sending reminder to ${contributor.user.email}:`, error);
      }
    }
  } catch (error) {
    console.error('Error checking reminders:', error);
  }
}