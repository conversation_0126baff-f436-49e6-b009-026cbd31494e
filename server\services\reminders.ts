import { db } from '../db.js';
import { projectContributors, projects, users } from '../schema.js';
import { and, eq, gt, lt, or, isNull } from 'drizzle-orm';
import { sendEmail } from './email.js';

// Helper function to check if it's time to send a reminder based on frequency
function shouldSendReminder(lastReminderSentAt: Date | null, reminderFrequency: string): boolean {
  if (!lastReminderSentAt) {
    console.log('No previous reminder sent, should send reminder');
    return true;
  }
  
  const now = new Date();
  const minutesSinceLastReminder = Math.floor((now.getTime() - lastReminderSentAt.getTime()) / (1000 * 60));
  console.log(`Minutes since last reminder: ${minutesSinceLastReminder}, frequency: ${reminderFrequency}`);
  
  switch (reminderFrequency) {
    case '1min':
      return minutesSinceLastReminder >= 1;
    case '2min':
      return minutesSinceLastReminder >= 2;
    case '5min':
      return minutesSinceLastReminder >= 5;
    case '15min':
      return minutesSinceLastReminder >= 15;
    case '30min':
      return minutesSinceLastReminder >= 30;
    case '1hour':
      return minutesSinceLastReminder >= 60;
    case 'daily':
      return minutesSinceLastReminder >= 24 * 60;
    case 'weekly':
      return minutesSinceLastReminder >= 7 * 24 * 60;
    case 'biweekly':
      return minutesSinceLastReminder >= 14 * 24 * 60;
    case 'monthly':
      return minutesSinceLastReminder >= 30 * 24 * 60;
    default:
      console.log('Unknown frequency, defaulting to false');
      return false;
  }
}

// Function to send reminder emails to contributors
export async function sendContributorReminders() {
  try {
    const now = new Date();
    console.log(`[${now.toISOString()}] Starting reminder check...`);
    
    // Find all contributors (both pending and accepted) with upcoming deadlines
    const contributors = await db.query.projectContributors.findMany({
      where: and(
        // Include both pending and accepted contributors
        or(
          eq(projectContributors.status, 'pending'),
          eq(projectContributors.status, 'accepted')
        ),
        // Only include contributors whose deadline hasn't passed
        or(
          isNull(projectContributors.deadline),
          gt(projectContributors.deadline, now)
        ),
        or(
          isNull(projectContributors.lastReminderSentAt),
          lt(projectContributors.lastReminderSentAt, now)
        )
      ),
      with: {
        user: true,
        project: {
          with: {
            organizer: true
          }
        }
      }
    });

    console.log(`Found ${contributors.length} contributors needing reminders`);

    for (const contributor of contributors) {
      if (!contributor.user?.email || !contributor.project) {
        console.log('Skipping contributor - missing user email or project');
        continue;
      }

      // Skip if deadline has passed
      if (contributor.deadline && contributor.deadline <= now) {
        console.log(`Skipping contributor ${contributor.user.email} - deadline passed`);
        continue;
      }

      // Check if it's time to send a reminder based on frequency
      if (!shouldSendReminder(contributor.lastReminderSentAt, contributor.reminderFrequency)) {
        console.log(`Skipping reminder for ${contributor.user.email} - not time yet (frequency: ${contributor.reminderFrequency})`);
        continue;
      }

      console.log(`Sending reminder to ${contributor.user.email} for project ${contributor.project.name}`);

      const daysUntilDeadline = contributor.deadline 
        ? Math.ceil((contributor.deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
        : null;
      
      // Send reminder email
      await sendEmail({
        to: contributor.user.email,
        subject: `Reminder: Recipe Submission${daysUntilDeadline ? ` Due in ${daysUntilDeadline} days` : ''}`,
        text: `Dear ${contributor.user.name},\n\nThis is a friendly reminder about your recipe submission for "${contributor.project.name}".${daysUntilDeadline ? `\n\nYour submission is due in ${daysUntilDeadline} days.` : ''}\n\nPlease make sure to submit your recipe${daysUntilDeadline ? ' before the deadline' : ''}.\n\nBest regards,\nThe RecipeBook Team`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #2E4B7A;">Recipe Submission Reminder</h2>
            <p>Dear ${contributor.user.name},</p>
            <p>This is a friendly reminder about your recipe submission for "${contributor.project.name}".</p>
            ${daysUntilDeadline ? `<p><strong>Your submission is due in ${daysUntilDeadline} days.</strong></p>` : ''}
            <p>Please make sure to submit your recipe${daysUntilDeadline ? ' before the deadline' : ''}.</p>
            <p>Best regards,<br>The RecipeBook Team</p>
          </div>
        `
      });

      // Update last reminder sent timestamp
      await db.update(projectContributors)
        .set({ lastReminderSentAt: now })
        .where(eq(projectContributors.id, contributor.id));

      console.log(`Successfully sent reminder to ${contributor.user.email}`);
    }
  } catch (error) {
    console.error('Error sending contributor reminders:', error);
  }
} 