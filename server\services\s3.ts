import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';

if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY || !process.env.AWS_REGION || !process.env.AWS_BUCKET_NAME) {
  console.error('AWS credentials or configuration not set in environment variables');
  process.exit(1);
}

const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
});

export async function getUploadUrl(fileName: string, fileType: string): Promise<string> {
  const key = `recipes/${uuidv4()}-${fileName}`;
  
  const command = new PutObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: key,
    ContentType: fileType,
  });

  try {
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
    return signedUrl;
  } catch (error) {
    console.error('Error generating signed URL:', error);
    throw new Error('Failed to generate upload URL');
  }
}

export async function getPresignedUrl(key: string): Promise<string> {
  const command = new GetObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: key,
  });

  try {
    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }); // 1 hour expiry
    return signedUrl;
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    throw new Error('Failed to generate presigned URL');
  }
}

export async function deleteObject(key: string): Promise<void> {
  if (!key) {
    console.error('Attempted to delete S3 object with empty key');
    throw new Error('Invalid S3 key: key cannot be empty');
  }

  // Remove any leading slash if present
  const normalizedKey = key.startsWith('/') ? key.slice(1) : key;
  
  console.log(`[S3 Delete] Attempting to delete object with key: ${normalizedKey} from bucket: ${process.env.AWS_BUCKET_NAME}`);
  
  const command = new DeleteObjectCommand({
    Bucket: process.env.AWS_BUCKET_NAME,
    Key: normalizedKey,
  });

  try {
    console.log('[S3 Delete] Sending delete command to S3...');
    const response = await s3Client.send(command);
    console.log('[S3 Delete] S3 delete response:', JSON.stringify(response, null, 2));
    console.log(`[S3 Delete] Successfully deleted object with key: ${normalizedKey}`);
  } catch (error) {
    console.error('[S3 Delete] Error deleting object from S3:', {
      error,
      bucket: process.env.AWS_BUCKET_NAME,
      key: normalizedKey,
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined
    });
    throw new Error(`Failed to delete object from S3: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// This function is no longer needed as we'll use presigned URLs
// export function getImageUrl(key: string): string {
//   return `https://${process.env.AWS_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;
// } 