import { ImageAnnotatorClient } from '@google-cloud/vision';
import { getPresignedUrl } from './s3.js';
import path from 'path';

// Set the path to the credentials file
const credentialsPath = path.resolve(process.cwd(), 'vision-api.json');
console.log(`[Vision API] Using credentials from: ${credentialsPath}`);

// Initialize Vision API client
// Try to use the credentials file
const visionClient = new ImageAnnotatorClient({
  keyFilename: credentialsPath
});

/**
 * Process an image URL through Google Cloud Vision OCR
 * @param imageKey S3 key for the image to process
 * @returns The extracted text and structured data
 */
export async function performOcr(imageKey: string) {
  try {
    console.log(`[Vision API] Performing OCR on image with key: ${imageKey}`);
    
    // Get presigned URL for the image from S3
    const imageUrl = await getPresignedUrl(imageKey);
    
    // Perform OCR on the image
    const [result] = await visionClient.textDetection(imageUrl);
    const detections = result.textAnnotations;
    
    if (!detections || detections.length === 0) {
      console.log('[Vision API] No text detected in the image');
      return { 
        rawText: '', 
        structuredData: null,
        success: false,
        error: 'No text detected in the image'
      };
    }
    
    // The first annotation contains the entire extracted text
    const fullText = detections[0].description || '';
    console.log(`[Vision API] Successfully extracted ${fullText.length} characters of text`);
    
    // Attempt to extract structured recipe data
    const structuredData = extractRecipeData(fullText);
    
    return {
      rawText: fullText,
      structuredData,
      success: true
    };
  } catch (error) {
    console.error('[Vision API] Error performing OCR:', error);
    return {
      rawText: '',
      structuredData: null,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during OCR processing'
    };
  }
}

/**
 * Extract structured recipe data from raw OCR text
 * This is a simple implementation that tries to identify recipe components
 * In a production environment, this would be enhanced with NLP or ML techniques
 */
function extractRecipeData(text: string) {
  // Basic extraction of recipe components
  const lines = text.split('\n').map(line => line.trim()).filter(Boolean);
  
  // Attempt to identify the title (usually at the beginning)
  const title = lines[0] || 'Untitled Recipe';
  
  // Look for ingredients section
  const ingredientsStartIndex = findSectionIndex(lines, ['ingredients', 'ingredients:']);
  const instructionsStartIndex = findSectionIndex(lines, ['instructions', 'directions', 'method', 'steps', 'preparation']);
  
  let ingredients: string[] = [];
  let instructions: string[] = [];
  let description = '';
  
  // Extract description (text before ingredients, excluding title)
  if (ingredientsStartIndex > 1) {
    description = lines.slice(1, ingredientsStartIndex).join('\n');
  } else if (lines.length > 1) {
    description = lines[1]; // Just take the second line if we can't find clear sections
  }
  
  // Extract ingredients
  if (ingredientsStartIndex >= 0 && instructionsStartIndex >= 0 && instructionsStartIndex > ingredientsStartIndex) {
    ingredients = lines.slice(ingredientsStartIndex + 1, instructionsStartIndex);
  } else if (ingredientsStartIndex >= 0) {
    // If we found ingredients but not instructions, assume ingredients go to the end
    ingredients = lines.slice(ingredientsStartIndex + 1);
  }
  
  // Extract instructions
  if (instructionsStartIndex >= 0) {
    instructions = lines.slice(instructionsStartIndex + 1);
  }
  
  // Format ingredients to match the app's structure
  const formattedIngredients = ingredients.map(ingredient => {
    // Try to extract amount and unit from ingredient text
    const match = ingredient.match(/^([\d¼½¾\.\s/]+)?\s*([a-zA-Z]+)?\s+(.+)$/);
    if (match && match.length >= 4) {
      const [, amountStr, unit, name] = match;
      const amount = amountStr ? parseFloat(amountStr.replace(/[^\d.]/g, '')) || 1 : 1;
      return { name: name.trim(), amount, unit: (unit || 'count').trim() };
    }
    return { name: ingredient.trim(), amount: 1, unit: 'count' };
  });
  
  return {
    title,
    description,
    ingredients: formattedIngredients,
    instructions: instructions.map(inst => inst.trim())
  };
}

/**
 * Find the index of a section header in the text lines
 */
function findSectionIndex(lines: string[], possibleHeaders: string[]): number {
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].toLowerCase();
    if (possibleHeaders.some(header => line === header || line.includes(header))) {
      return i;
    }
  }
  return -1;
} 