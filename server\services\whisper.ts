import fs from 'fs';
import { getPresignedUrl } from './s3.js';
import https from 'https';
import path from 'path';
import os from 'os';
import OpenAI from 'openai';
import FormData from 'form-data';
import fetch from 'node-fetch';

// Initialize Whisper API key from environment variable
const WHISPER_API_KEY = process.env.WHISPER_API_KEY;

if (!WHISPER_API_KEY) {
  console.warn('[Whisper API] API key not found in environment variables.');
}

// LemonFox API endpoint
const LEMONFOX_API_URL = 'https://api.lemonfox.ai/v1/audio/transcriptions';

/**
 * Tests connectivity to the LemonFox API
 * @returns True if connection is successful, false otherwise
 */
async function testConnectivity(): Promise<boolean> {
  return new Promise((resolve) => {
    console.log(`[Whisper API] Testing connectivity to LemonFox API...`);
    
    // Create a simple HTTPS request to test connectivity
    const req = https.request({
      host: 'api.lemonfox.ai',
      port: 443,
      path: '/v1',
      method: 'GET',
      timeout: 5000, // 5 second timeout
    }, (res) => {
      console.log(`[Whisper API] Connection to LemonFox API successful (status: ${res.statusCode})`);
      
      // Consume response data to free up memory
      res.resume();
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.error(`[Whisper API] Connection test failed:`, error);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.error(`[Whisper API] Connection test timed out`);
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * Retries a function multiple times with exponential backoff
 * @param fn Function to retry
 * @param maxRetries Maximum number of retries
 * @param delay Initial delay in ms
 */
async function retry<T>(fn: () => Promise<T>, maxRetries = 3, delay = 1000): Promise<T> {
  let lastError: any;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      console.warn(`[Retry] Attempt ${i + 1}/${maxRetries} failed: ${error instanceof Error ? error.message : 'unknown error'}`);
      
      if (i < maxRetries - 1) {
        // Calculate exponential backoff delay
        const backoffDelay = delay * Math.pow(2, i);
        console.log(`[Retry] Retrying in ${backoffDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, backoffDelay));
      }
    }
  }
  
  throw lastError;
}

/**
 * Process an audio file through LemonFox Whisper API for transcription
 * @param audioKey S3 key for the audio file to process
 * @returns The transcription and structured recipe data
 */
export async function performTranscription(audioKey: string) {
  try {
    console.log(`[Whisper API] Performing transcription on audio with key: ${audioKey}`);
    
    if (!WHISPER_API_KEY) {
      throw new Error('Whisper API key not configured');
    }
    
    // Test connectivity before proceeding
    const isConnected = await testConnectivity();
    if (!isConnected) {
      throw new Error('Cannot connect to LemonFox API. Please check your network connection and firewall settings.');
    }
    
    // Get a presigned URL for the audio file from S3
    const audioUrl = await getPresignedUrl(audioKey);
    console.log(`[Whisper API] Got presigned URL for audio file`);
    
    // Create FormData with the audio URL (as per LemonFox documentation)
    console.log(`[Whisper API] Calling LemonFox Whisper API...`);
    
    try {
      // Make API call with retry mechanism
      const transcriptionResult = await retry(async () => {
        const formData = new FormData();
        
        // Option 1: Use the S3 presigned URL directly
        formData.append('file', audioUrl);
        formData.append('language', 'english');
        formData.append('response_format', 'json');
        
        const response = await fetch(LEMONFOX_API_URL, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${WHISPER_API_KEY}`,
          },
          body: formData,
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`LemonFox API returned status ${response.status}: ${errorText}`);
        }
        
        return await response.json();
      }, 3, 2000); // 3 retries with 2 second initial delay
      
      console.log(`[Whisper API] Successfully got transcription from LemonFox API`);
      
      if (!transcriptionResult.text) {
        throw new Error('No transcription text returned from API');
      }
      
      console.log(`[Whisper API] Successfully transcribed ${transcriptionResult.text.length} characters of text`);
      console.log(`[Whisper API] Raw transcription text: "${transcriptionResult.text.substring(0, 200)}${transcriptionResult.text.length > 200 ? '...' : ''}"`);
      
      // Extract recipe data from the transcription
      const structuredData = extractRecipeData(transcriptionResult.text);
      
      console.log(`[Whisper API] Extracted structured data:`, JSON.stringify(structuredData, null, 2));
      
      return {
        rawText: transcriptionResult.text,
        structuredData,
        success: true
      };
    } catch (apiError: unknown) {
      console.error(`[Whisper API] Error from LemonFox API:`, apiError);
      if (apiError && typeof apiError === 'object' && 'cause' in apiError) {
        console.error(`[Whisper API] Underlying cause:`, (apiError as any).cause);
      }
      
      throw new Error(`LemonFox API error: ${apiError instanceof Error ? apiError.message : 'unknown API error'}`);
    }
  } catch (error) {
    console.error('[Whisper API] Error performing transcription:', error);
    return {
      rawText: '',
      structuredData: null,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during transcription'
    };
  }
}

/**
 * Extract structured recipe data from raw transcription text
 * Uses similar logic to OCR extraction but with some adjustments for speech patterns
 */
function extractRecipeData(text: string) {
  console.log('[Whisper API] Starting recipe data extraction from text');
  console.log('[Whisper API] Raw text:', text);
  
  // Safety check for empty text
  if (!text || text.trim().length === 0) {
    console.warn('[Whisper API] Empty transcription text received');
    return {
      title: 'Untitled Recipe',
      description: '',
      ingredients: [],
      instructions: []
    };
  }
  
  // Clean up the text for better processing
  let cleanedText = text
    .replace(/\s+/g, ' ')       // Replace multiple spaces with a single space
    .replace(/(\w)\.(\w)/g, '$1. $2') // Add space after periods between words
    .trim();
    
  // Split full text by common speech patterns and markers
  const titleMatch = cleanedText.match(/^([^.,:;]+?)(?:description|ingredients|instructions|this is|you will need)/i);
  let title = titleMatch ? titleMatch[1].trim() : '';
  
  // If no clear title was found, use the first segment until the first period
  if (!title) {
    const firstSegment = cleanedText.split(/[.]/)[0];
    title = firstSegment.length > 50 ? firstSegment.substring(0, 50) : firstSegment;
  }
  
  // Clean up title - remove "title" or "title is" or "the title is" patterns
  title = title.replace(/^(?:the\s+)?title(?:\s+is)?[:\s]*/i, '').trim();
  
  console.log(`[Whisper API] Identified title: "${title}"`);
  
  // Remove title from text to process the rest
  if (title) {
    // Use a regex that escapes special characters in the title
    const escapedTitle = title.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    cleanedText = cleanedText.replace(new RegExp(`^${escapedTitle}\\s*`, 'i'), '');
  }
  
  // Find sections using explicit markers
  const descriptionPattern = /description\s+(.*?)(?=ingredients|instructions|you will need|$)/i;
  const ingredientsPattern = /ingredients\s+(.*?)(?=instructions|steps|directions|$)/i;
  const instructionsPattern = /instructions\s+(.*?)(?=$)/i;
  
  // Extract description
  let description = '';
  const descMatch = cleanedText.match(descriptionPattern);
  if (descMatch && descMatch[1]) {
    description = descMatch[1].trim();
  } else {
    // If no explicit description section, look for text between title and ingredients
    const beforeIngredients = cleanedText.split(/ingredients|you will need/i)[0];
    if (beforeIngredients && beforeIngredients !== title) {
      description = beforeIngredients.replace(title, '').trim();
    }
  }
  
  // Clean up description - remove "description" or "description is" patterns
  description = description.replace(/^(?:the\s+)?description(?:\s+is)?[:\s]*/i, '').trim();
  
  console.log(`[Whisper API] Extracted description: "${description}"`);
  
  // Extract ingredients
  let ingredientsText = '';
  const ingredMatch = cleanedText.match(ingredientsPattern);
  if (ingredMatch && ingredMatch[1]) {
    ingredientsText = ingredMatch[1].trim();
  }
  
  // If no ingredients section found with marker, try alternative approaches
  if (!ingredientsText) {
    // Try to find the section between description and instructions
    const parts = cleanedText.split(/instructions|steps|directions/i);
    if (parts.length > 1) {
      // Extract the part after "ingredients" or similar phrases
      const ingPart = parts[0].split(/ingredients|you will need|you'll need|what you need/i);
      if (ingPart.length > 1) {
        ingredientsText = ingPart[1].trim();
      }
    }
  }
  
  // If still no ingredients text found, try to look for quantity patterns
  if (!ingredientsText && cleanedText.length > 0) {
    console.log('[Whisper API] No clear ingredients section found, attempting to identify by number patterns');
    
    // Look for text that contains common ingredient quantity patterns
    const lines = cleanedText.split(/[.,;]|\sand\s/).map(l => l.trim());
    const potentialIngredients = lines.filter(line => 
      /\d+\s+(tablespoon|tbsp|teaspoon|tsp|cup|ounce|oz|pound|lb|gram|g|ml|liter|l)/i.test(line) ||
      /half\s+a|quarter\s+of|one|two|three|four|five|six|seven|eight|nine|ten/i.test(line)
    );
    
    if (potentialIngredients.length > 0) {
      ingredientsText = potentialIngredients.join('. ');
      console.log(`[Whisper API] Found ${potentialIngredients.length} potential ingredients by pattern matching`);
    }
  }
  
  // Extract instructions
  let instructionsText = '';
  const instrMatch = cleanedText.match(instructionsPattern);
  if (instrMatch && instrMatch[1]) {
    instructionsText = instrMatch[1].trim();
  }
  
  // If no instructions section found with marker, use everything after ingredients
  if (!instructionsText) {
    const parts = cleanedText.split(/instructions|steps|directions|method/i);
    if (parts.length > 1) {
      instructionsText = parts[1].trim();
    }
  }
  
  // If still no instructions and we have ingredients, try to find instructions by action verbs
  if (!instructionsText && ingredientsText) {
    const remainingText = cleanedText.replace(ingredientsText, '').replace(description, '').replace(title, '').trim();
    
    if (remainingText.length > 0) {
      // Look for text with cooking action verbs
      if (/\b(heat|cook|bake|fry|boil|simmer|stir|mix|add|combine|pour|place|set|leave|let|remove|blend|whisk|prepare|chop|cut|slice)\b/i.test(remainingText)) {
        instructionsText = remainingText;
        console.log('[Whisper API] Identified instructions by cooking action verbs');
      }
    }
  }
  
  // Case: if no clear sections were found but we have text, make a best guess
  if (!ingredientsText && !instructionsText && cleanedText.length > 0) {
    console.log('[Whisper API] No clear sections found, attempting to divide text');
    
    // Split text into thirds - first third title/description, middle third ingredients, last third instructions
    const lines = cleanedText.split(/[.;]/).map(l => l.trim()).filter(Boolean);
    
    if (lines.length >= 3) {
      const firstThird = Math.floor(lines.length / 3);
      const secondThird = Math.floor(2 * lines.length / 3);
      
      // First third already used for title/description
      
      // Middle third for ingredients
      ingredientsText = lines.slice(firstThird, secondThird).join('. ');
      
      // Last third for instructions
      instructionsText = lines.slice(secondThird).join('. ');
      
      console.log(`[Whisper API] Split ${lines.length} lines into sections`);
    } else if (lines.length > 0) {
      // If very few lines, just put everything as instructions
      instructionsText = lines.join('. ');
    }
  }
  
  // Debug - log the raw instructions text
  console.log(`[Whisper API] Raw instructions text (${instructionsText.length} chars): "${instructionsText.substring(0, 100)}${instructionsText.length > 100 ? '...' : ''}"`);
  
  // Parse instructions into steps using a multi-strategy approach
  let instructions: string[] = [];
  
  if (instructionsText) {
    // Strategy 1: First try to split by explicit step markers
    const explicitStepSplit = instructionsText.split(/\s*(?:step\s+\d+|step\s*[:.\s])\s*/i)
      .map(step => step.trim())
      .filter(step => step.length > 5);
    
    if (explicitStepSplit.length > 1) {
      console.log(`[Whisper API] Found ${explicitStepSplit.length} explicit steps`);
      instructions = explicitStepSplit;
    } else {
      // Strategy 2: Try to split by periods followed by cooking verbs
      const verbPattern = /\.\s+(?=[A-Z]?[a-z]*\s*(?:heat|cook|bake|fry|boil|simmer|stir|mix|add|combine|pour|place|set|leave|let|remove|blend|whisk|prepare|chop|cut|slice))/i;
      const verbSplit = instructionsText.split(verbPattern)
        .map(step => step.trim())
        .filter(step => step.length > 5);
      
      if (verbSplit.length > 1) {
        console.log(`[Whisper API] Split instructions by period + cooking verbs into ${verbSplit.length} steps`);
        instructions = verbSplit;
      } else {
        // Strategy 3: Try to split by common step transition phrases
        const transitionPattern = /\.\s+(?:next|then|after that|following that|once done|when finished|finally|lastly)/i;
        const transitionSplit = instructionsText.split(transitionPattern)
          .map(step => step.trim())
          .filter(step => step.length > 5);
        
        if (transitionSplit.length > 1) {
          console.log(`[Whisper API] Split instructions by transition phrases into ${transitionSplit.length} steps`);
          instructions = transitionSplit;
        } else {
          // Strategy 4: Just split by periods for basic sentences
          const sentenceSplit = instructionsText.split(/\.\s+/)
            .map(step => step.trim())
            .filter(step => step.length > 5);
          
          console.log(`[Whisper API] Split instructions by periods into ${sentenceSplit.length} steps`);
          instructions = sentenceSplit;
        }
      }
    }
    
    // If we still have too few instructions but long text, try more aggressive splitting
    if (instructions.length < 2 && instructionsText.length > 200) {
      console.log('[Whisper API] Only one instruction detected but text is long, applying more aggressive splitting');
      
      // Strategy 5: Try to split by cooking verbs anywhere
      const verbAnywherePattern = /\b(?:heat|cook|bake|fry|boil|simmer|stir|mix|add|combine|pour|place|set|leave|let|remove|blend|whisk|prepare|chop|cut|slice)\b/i;
      let verbPositions: number[] = [];
      let match;
      const re = new RegExp(verbAnywherePattern, 'gi');
      
      // Find all positions of cooking verbs
      while ((match = re.exec(instructionsText)) !== null) {
        if (match.index > 15) { // Avoid splitting at the very beginning
          verbPositions.push(match.index);
        }
      }
      
      // If we found verb positions, split the text at those positions
      if (verbPositions.length > 0) {
        const newSteps: string[] = [];
        let lastPos = 0;
        
        // Add first part if needed
        if (verbPositions[0] > 30) {
          newSteps.push(instructionsText.substring(0, verbPositions[0]).trim());
          lastPos = verbPositions[0];
        }
        
        // Add middle parts
        for (let i = 0; i < verbPositions.length; i++) {
          const pos = verbPositions[i];
          const nextPos = i < verbPositions.length - 1 ? verbPositions[i + 1] : instructionsText.length;
          
          // Only split if the segment is long enough
          if (nextPos - pos > 30) {
            newSteps.push(instructionsText.substring(pos, nextPos).trim());
            lastPos = nextPos;
          }
        }
        
        // If we actually got multiple steps, use them
        if (newSteps.length > 1) {
          console.log(`[Whisper API] Split instructions by verb positions into ${newSteps.length} steps`);
          instructions = newSteps;
        }
      }
      
      // Strategy 6: Last resort - force split by length
      if (instructions.length < 2) {
        console.log('[Whisper API] Forcing split by text length');
        const avgStepLength = 100; // Target average step length
        const numSteps = Math.max(2, Math.ceil(instructionsText.length / avgStepLength));
        const stepLength = Math.ceil(instructionsText.length / numSteps);
        
        const forcedSteps: string[] = [];
        for (let i = 0; i < numSteps; i++) {
          const start = i * stepLength;
          let end = start + stepLength;
          
          // Try to find a better split point near the calculated position
          if (end < instructionsText.length) {
            const nextPeriod = instructionsText.indexOf('.', end - 20);
            if (nextPeriod > 0 && nextPeriod < end + 20) {
              end = nextPeriod + 1;
            }
          }
          
          const step = instructionsText.substring(start, Math.min(end, instructionsText.length)).trim();
          if (step.length > 5) {
            forcedSteps.push(step);
          }
        }
        
        if (forcedSteps.length > 1) {
          console.log(`[Whisper API] Force-split instructions into ${forcedSteps.length} steps by length`);
          instructions = forcedSteps;
        }
      }
    }
    
    // Clean up instructions
    instructions = instructions.map(step => {
      // Remove leading numbers or bullets
      let cleaned = step.replace(/^[\d\s.•*-]+/, '').trim();
      
      // Add period at end if missing
      if (!cleaned.endsWith('.') && !cleaned.endsWith('!') && !cleaned.endsWith('?')) {
        cleaned += '.';
      }
      
      // Capitalize first letter
      return cleaned.charAt(0).toUpperCase() + cleaned.slice(1);
    });
  }
  
  // Parse ingredients into structured format
  let ingredients: any[] = [];
  if (ingredientsText) {
    // Split by common ingredient separators
    const ingredientLines = ingredientsText
      .split(/[,.;]|\sand\s|\sthen\s/)
      .map(line => line.trim())
      .filter(Boolean);
    
    console.log(`[Whisper API] Extracted ${ingredientLines.length} ingredient lines`);
    
    ingredients = ingredientLines.map(ingredient => {
      // Try to extract amount and unit from ingredient text
      const match = ingredient.match(/^([\d¼½¾\.\s/]+)?\s*([a-zA-Z]+)?\s+(?:of\s+)?(.+)$/);
      if (match && match.length >= 4) {
        const [, amountStr, unit, name] = match;
        let amount = 1;
        
        if (amountStr) {
          // Handle fractions and mixed numbers
          if (amountStr.includes('/')) {
            const fractionParts = amountStr.split(/\s+/);
            if (fractionParts.length === 1) {
              // Simple fraction like "1/2"
              const [numerator, denominator] = amountStr.split('/').map(Number);
              amount = numerator / denominator;
            } else {
              // Mixed number like "1 1/2"
              const wholePart = Number(fractionParts[0]);
              const [numerator, denominator] = fractionParts[1].split('/').map(Number);
              amount = wholePart + (numerator / denominator);
            }
          } else {
            // Handle special fraction characters
            let processedAmount = amountStr;
            if (processedAmount.includes('¼')) amount = 0.25;
            else if (processedAmount.includes('½')) amount = 0.5;
            else if (processedAmount.includes('¾')) amount = 0.75;
            else amount = parseFloat(processedAmount.replace(/[^\d.]/g, '')) || 1;
          }
        }
        
        // Clean up the unit
        let cleanUnit = unit ? unit.toLowerCase().trim() : 'count';
        
        // Normalize common units
        if (cleanUnit === 'g' || cleanUnit === 'gram' || cleanUnit === 'grams') cleanUnit = 'gram';
        if (cleanUnit === 'kg' || cleanUnit === 'kilogram' || cleanUnit === 'kilograms') cleanUnit = 'kilogram';
        if (cleanUnit === 'oz' || cleanUnit === 'ounce' || cleanUnit === 'ounces') cleanUnit = 'ounce';
        if (cleanUnit === 'lb' || cleanUnit === 'lbs' || cleanUnit === 'pound' || cleanUnit === 'pounds') cleanUnit = 'pound';
        if (cleanUnit === 'ml' || cleanUnit === 'milliliter' || cleanUnit === 'milliliters') cleanUnit = 'milliliter';
        if (cleanUnit === 'l' || cleanUnit === 'liter' || cleanUnit === 'liters') cleanUnit = 'liter';
        if (cleanUnit === 'tsp' || cleanUnit === 'teaspoon' || cleanUnit === 'teaspoons') cleanUnit = 'teaspoon';
        if (cleanUnit === 'tbsp' || cleanUnit === 'tablespoon' || cleanUnit === 'tablespoons') cleanUnit = 'tablespoon';
        if (cleanUnit === 'cup' || cleanUnit === 'cups') cleanUnit = 'cup';
        
        // Clean up ingredient name
        let cleanName = name.trim();
        // Remove unnecessary words often included in spoken recipes
        cleanName = cleanName.replace(/^of\s+/, '');
        
        return { name: cleanName, amount, unit: cleanUnit };
      }
      return { name: ingredient.trim(), amount: 1, unit: 'count' };
    });
  }
  
  console.log(`[Whisper API] Extracted ${ingredients.length} ingredients`);
  console.log(`[Whisper API] Extracted ${instructions.length} instruction steps`);
  
  // For speech recognition, some cleanup might be needed for the title
  let cleanTitle = title.replace(/^recipe for |^recipe |^how to make /i, '').trim();
  // Capitalize the first letter
  cleanTitle = cleanTitle.charAt(0).toUpperCase() + cleanTitle.slice(1);
  
  // If we didn't extract meaningful data but have text, create a last resort fallback
  if ((!ingredients.length || !instructions.length) && text.length > 50) {
    console.warn('[Whisper API] Extraction failed to produce complete recipe data, creating fallback');
    
    if (!ingredients.length) {
      console.log('[Whisper API] Creating fallback ingredients');
      // Create a synthetic ingredient from the raw text
      ingredients = [{ 
        name: "See full text in description", 
        amount: 1, 
        unit: "item" 
      }];
    }
    
    if (!instructions.length) {
      console.log('[Whisper API] Creating fallback instructions');
      instructions = ["See the full transcription in the description section."];
      
      // If we don't have a meaningful description, put the full transcription there
      if (!description || description.length < 50) {
        description = "Full transcription of the recipe:\n\n" + text;
      }
    }
  }
  
  return {
    title: cleanTitle || 'Untitled Recipe',
    description: description || '',
    ingredients: ingredients.length ? ingredients : [{ name: "Ingredients not detected", amount: 0, unit: "count" }],
    instructions: instructions.length ? instructions : ["Instructions not detected"]
  };
}

/**
 * Find the index of a section header in the text lines
 */
function findSectionIndex(lines: string[], possibleHeaders: string[]): number {
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].toLowerCase();
    if (possibleHeaders.some(header => line.includes(header))) {
      return i;
    }
  }
  return -1;
} 