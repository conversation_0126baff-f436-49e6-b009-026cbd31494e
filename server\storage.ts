import { users } from "./schema.js";
import type { User } from "./schema.js";

// modify the interface with any CRUD methods
export interface IStorage {
  findUserByEmail: (email: string) => Promise<User | null>;
  createUser: (user: Omit<User, 'id'>) => Promise<User>;
  updateUser: (id: number, user: Partial<User>) => Promise<User | null>;
  deleteUser: (id: number) => Promise<boolean>;
}

class Storage implements IStorage {
  private users: User[] = [];
  private nextId = 1;

  async findUserByEmail(email: string): Promise<User | null> {
    return this.users.find((user) => user.email === email) || null;
  }

  async createUser(userData: Omit<User, 'id'>): Promise<User> {
    const id = this.nextId++;
    const user: User = {
      id,
      email: userData.email,
      password: userData.password,
      name: userData.name,
      role: userData.role,
      isActive: userData.isActive ?? true,
      lastLogin: userData.lastLogin ?? null,
      createdAt: userData.createdAt ?? new Date(),
      updatedAt: userData.updatedAt ?? new Date()
    };
    this.users.push(user);
    return user;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | null> {
    const index = this.users.findIndex((user) => user.id === id);
    if (index === -1) return null;

    this.users[index] = { ...this.users[index], ...userData };
    return this.users[index];
  }

  async deleteUser(id: number): Promise<boolean> {
    const index = this.users.findIndex((user) => user.id === id);
    if (index === -1) return false;

    this.users.splice(index, 1);
    return true;
  }
}

export default new Storage();
