import axios from 'axios';
import * as dotenv from 'dotenv';

dotenv.config();

// RPI Print API configuration
const API_BASE_URL = 'https://open.api.sandbox.rpiprint.com';
const API_KEY = process.env.RPI_PRINT_API_KEY;
const API_SECRET = process.env.RPI_PRINT_API_SECRET;

// Create Basic Auth header
const createAuthHeader = () => {
  if (!API_KEY || !API_SECRET) {
    throw new Error('RPI Print API credentials not configured. Please set RPI_PRINT_API_KEY and RPI_PRINT_API_SECRET in your .env file');
  }
  
  const credentials = Buffer.from(`${API_KEY}:${API_SECRET}`).toString('base64');
  return `Basic ${credentials}`;
};

// Test function to create a sample order
async function testCreateOrder() {
  console.log('🧪 Testing RPI Print API - Creating Sample Order');
  console.log('================================================');
  
  try {
    const startTime = Date.now();
    
    // Sample order data using RPI Print's test PDFs
    const orderData = {
      currency: "USD",
      shippingClassification: "priority",
      webhookUrl: "https://your-webhook-endpoint.com/webhook", // Optional
      destination: {
        name: "Test Customer",
        company: "Recipe Book Test",
        address1: "123 Test Street",
        address2: "",
        address3: "",
        city: "Test City",
        state: "California",
        postal: "90210",
        country: "US",
        phone: "(*************",
        email: "<EMAIL>"
      },
      orderItems: [
        {
          sku: "11x8.5 Imagewrap, Gloss Laminate, 100# Gloss Text",
          quantity: 1,
          retailPrice: "25.99",
          itemDescription: "Family Recipe Collection Test Book",
          product: {
            // Using RPI Print's sample PDFs for testing
            coverUrl: "https://docs.api.sandbox.rpiprint.com/pdfs/HCIW_11x8.5_GLS_GL100T_20pg_cover.pdf",
            gutsUrl: "https://docs.api.sandbox.rpiprint.com/pdfs/HCIW_11x8.5_GLS_GL100T_20pg_guts.pdf"
          }
        }
      ]
    };

    console.log('📤 Sending order request to RPI Print API...');
    console.log(`📍 Endpoint: ${API_BASE_URL}/orders/create`);
    console.log(`📦 Order Items: ${orderData.orderItems.length}`);
    console.log(`💰 Total Price: $${orderData.orderItems[0].retailPrice}`);
    
    const response = await axios.post(`${API_BASE_URL}/orders/create`, orderData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': createAuthHeader()
      },
      timeout: 30000 // 30 second timeout
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log('✅ Order created successfully!');
    console.log(`⏱️  Response time: ${responseTime}ms`);
    console.log('📋 Order Details:');
    console.log(`   Order ID: ${response.data.orderId}`);
    console.log(`   Status: ${response.data.status}`);
    console.log(`   Customer ID: ${response.data.customerId}`);
    
    if (response.data.trackingUrl) {
      console.log(`   Tracking URL: ${response.data.trackingUrl}`);
    }
    
    if (response.data.estimatedShipDate) {
      console.log(`   Estimated Ship Date: ${response.data.estimatedShipDate}`);
    }

    console.log('\n📊 Performance Analysis:');
    console.log(`   API Response Time: ${responseTime}ms`);
    console.log(`   Status: ${responseTime < 5000 ? '🟢 Fast' : responseTime < 10000 ? '🟡 Moderate' : '🔴 Slow'}`);
    
    return {
      success: true,
      orderId: response.data.orderId,
      responseTime,
      data: response.data
    };

  } catch (error) {
    console.error('❌ Error creating order:');
    
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Message: ${error.response.data?.message || error.response.statusText}`);
      console.error(`   Details:`, error.response.data);
    } else if (error.request) {
      console.error('   No response received from API');
      console.error('   This could indicate network issues or API downtime');
    } else {
      console.error(`   Error: ${error.message}`);
    }
    
    return {
      success: false,
      error: error.message,
      details: error.response?.data
    };
  }
}

// Test function to check order status
async function testOrderStatus(orderId) {
  console.log(`\n🔍 Checking order status for: ${orderId}`);
  console.log('==========================================');
  
  try {
    const startTime = Date.now();
    
    const response = await axios.get(`${API_BASE_URL}/orders/${orderId}`, {
      headers: {
        'Authorization': createAuthHeader()
      },
      timeout: 10000
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log('✅ Order status retrieved successfully!');
    console.log(`⏱️  Response time: ${responseTime}ms`);
    console.log('📋 Order Status:');
    console.log(`   Order ID: ${response.data.orderId}`);
    console.log(`   Status: ${response.data.status}`);
    console.log(`   Created: ${response.data.createdAt}`);
    console.log(`   Updated: ${response.data.updatedAt}`);
    
    if (response.data.shippingInfo) {
      console.log('📦 Shipping Info:');
      console.log(`   Carrier: ${response.data.shippingInfo.carrier || 'N/A'}`);
      console.log(`   Tracking: ${response.data.shippingInfo.trackingNumber || 'N/A'}`);
    }

    return {
      success: true,
      responseTime,
      data: response.data
    };

  } catch (error) {
    console.error('❌ Error checking order status:');
    console.error(`   Error: ${error.message}`);
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Main test function
async function runBlurbApiTest() {
  console.log('🚀 Starting Blurb/RPI Print API Performance Test');
  console.log('=================================================\n');
  
  // Test 1: Create Order
  const orderResult = await testCreateOrder();
  
  if (orderResult.success) {
    // Test 2: Check Order Status
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    await testOrderStatus(orderResult.orderId);
  }
  
  console.log('\n📈 Test Summary:');
  console.log('================');
  console.log(`Order Creation: ${orderResult.success ? '✅ Success' : '❌ Failed'}`);
  if (orderResult.success) {
    console.log(`Response Time: ${orderResult.responseTime}ms`);
    console.log(`Order ID: ${orderResult.orderId}`);
  }
  
  console.log('\n💡 Next Steps:');
  console.log('==============');
  if (orderResult.success) {
    console.log('✅ RPI Print API is working correctly');
    console.log('✅ You can proceed with full integration');
    console.log('📝 Consider implementing:');
    console.log('   - PDF generation for recipe books');
    console.log('   - Custom book layouts and designs');
    console.log('   - Order tracking and status updates');
    console.log('   - Webhook integration for real-time updates');
  } else {
    console.log('❌ RPI Print API test failed');
    console.log('🔧 Check your API credentials and network connection');
    console.log('📖 Review the RPI Print API documentation');
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runBlurbApiTest().catch(console.error);
}

export { testCreateOrder, testOrderStatus, runBlurbApiTest };
