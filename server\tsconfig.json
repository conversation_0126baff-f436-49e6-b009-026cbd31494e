{"compilerOptions": {"target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "allowJs": true, "resolveJsonModule": true, "noImplicitAny": false, "baseUrl": "."}, "ts-node": {"esm": true}, "include": ["*.ts", "**/*.ts", "types/**/*.d.ts"], "exclude": ["node_modules", "dist", "migrate*.ts", "scripts/**/*"]}